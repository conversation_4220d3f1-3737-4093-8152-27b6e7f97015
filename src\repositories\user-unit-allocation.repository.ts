import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {UserUnitAllocation, UserUnitAllocationRelations} from '../models';

export class UserUnitAllocationRepository extends DefaultCrudRepository<
  UserUnitAllocation,
  typeof UserUnitAllocation.prototype.id,
  UserUnitAllocationRelations
> {
  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource,
  ) {
    super(UserUnitAllocation, dataSource);
  }
}
