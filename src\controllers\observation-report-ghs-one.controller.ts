import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  ObservationReport,
  GhsOne,
} from '../models';
import {ObservationReportRepository} from '../repositories';

export class ObservationReportGhsOneController {
  constructor(
    @repository(ObservationReportRepository)
    public observationReportRepository: ObservationReportRepository,
  ) { }

  @get('/observation-reports/{id}/ghs-one', {
    responses: {
      '200': {
        description: 'GhsOne belonging to ObservationReport',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(GhsOne)},
          },
        },
      },
    },
  })
  async getGhsOne(
    @param.path.string('id') id: typeof ObservationReport.prototype.id,
  ): Promise<GhsOne> {
    return this.observationReportRepository.ghsOne(id);
  }
}
