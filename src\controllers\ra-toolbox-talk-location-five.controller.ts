import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  RaToolboxTalk,
  LocationFive,
} from '../models';
import {RaToolboxTalkRepository} from '../repositories';

export class RaToolboxTalkLocationFiveController {
  constructor(
    @repository(RaToolboxTalkRepository)
    public raToolboxTalkRepository: RaToolboxTalkRepository,
  ) { }

  @get('/ra-toolbox-talks/{id}/location-five', {
    responses: {
      '200': {
        description: 'LocationFive belonging to RaToolboxTalk',
        content: {
          'application/json': {
            schema: getModelSchemaRef(LocationFive),
          },
        },
      },
    },
  })
  async getLocationFive(
    @param.path.string('id') id: typeof RaToolboxTalk.prototype.id,
  ): Promise<LocationFive> {
    return this.raToolboxTalkRepository.locationFive(id);
  }
}
