import {Entity, model, property, hasMany} from '@loopback/repository';
import {ObservationReport} from './observation-report.model';
import {PermitReport} from './permit-report.model';

@model()
export class LocationSix extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'string',
  })
  description?: string;

  @property({
    type: 'string',
  })
  created?: string;

  @property({
    type: 'string',
  })
  modified?: string;

  @property({
    type: 'string',
  })
  locationFiveId?: string;

  @hasMany(() => ObservationReport)
  observationReports: ObservationReport[];

  @hasMany(() => PermitReport)
  permitReports: PermitReport[];

  constructor(data?: Partial<LocationSix>) {
    super(data);
  }
}

export interface LocationSixRelations {
  // describe navigational properties here
}

export type LocationSixWithRelations = LocationSix & LocationSixRelations;
