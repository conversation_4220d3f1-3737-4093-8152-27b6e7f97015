import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
  HttpErrors,
} from '@loopback/rest';
import { Action, ActionRelations } from '../models';
import { inject } from '@loopback/core';
import { ActionRepository, ObservationReportRepository, GoodCatchRepository, DocumentsRepository, PermitReportRepository, UserRepository, AirReportRepository, RiskAssessmentRepository } from '../repositories';
import { authenticate } from '@loopback/authentication';
import { SecurityBindings, securityId, UserProfile } from '@loopback/security';
import moment from "moment";

import { SqsService } from '../services/sqs-service.service';


@authenticate('jwt')
export class ActionController {
  constructor(
    @repository(ActionRepository)
    public actionRepository: ActionRepository,
    @repository(ObservationReportRepository)
    public observationReportRepository: ObservationReportRepository,
    @repository(PermitReportRepository)
    public permitReportRepository: PermitReportRepository,
    @repository(AirReportRepository)
    public airReportRepository: AirReportRepository,
    @repository(UserRepository)
    public userRepository: UserRepository,
    @repository(DocumentsRepository)
    public docRepository: DocumentsRepository,
    @repository(RiskAssessmentRepository)
    public riskAssessmentRepository: RiskAssessmentRepository,
    @repository(GoodCatchRepository)
    public goodCatchRepository: GoodCatchRepository,
    @inject('services.SqsService')
    public sqsService: SqsService,
  ) { }

  @post('/actions')
  @response(200, {
    description: 'Action model instance',
    content: { 'application/json': { schema: getModelSchemaRef(Action) } },
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Action, {
            title: 'NewAction',
            exclude: ['id'],
          }),
        },
      },
    })
    action: Omit<Action, 'id'>,
  ): Promise<Action> {

    return this.actionRepository.create(action);
  }

  @get('/actions/count')
  @response(200, {
    description: 'Action model count',
    content: { 'application/json': { schema: CountSchema } },
  })
  async count(
    @param.where(Action) where?: Where<Action>,
  ): Promise<Count> {
    return this.actionRepository.count(where);
  }

  @get('/actions')
  @response(200, {
    description: 'Array of Action model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(Action, { includeRelations: true }),
        },
      },
    },
  })
  async find(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.filter(Action) filter?: Filter<Action>,
  ): Promise<any> {
    const email = '<EMAIL>'
    const user = await this.userRepository.findOne({ where: { email: email } });

    let actions: any = await this.actionRepository.find({ where: { status: { nin: ['completed'] } } });


    actions = actions.filter((action: { assignedToId: string | string[]; }) =>
      user?.id && action.assignedToId && action.assignedToId.includes(user.id)
    );

    const actionsWithSubmittedBy = await Promise.all(actions.map(async (action: { submittedById: any; }) => {
      const submittedBy = await this.userRepository.findOne({
        where: { id: action.submittedById },
        fields: {
          id: true,
          firstName: true,
          email: true
        }
      });
      return { ...action, actionSubmittedBy: submittedBy };
    }));

    let results = [];

    for (const i of actionsWithSubmittedBy) {

      try {
        let applicationDetails: any = {};
        switch (i.application) {
          case 'Observation':
            {
              const obs = await this.observationReportRepository.findById(i.objectId, {
                include: [
                  { relation: 'locationOne' },
                  { relation: 'locationTwo' },
                  { relation: 'locationThree' },
                  { relation: 'locationFour' },
                  { relation: 'locationFive' },
                  { relation: 'locationSix' },
                ],
              });
              if (!obs) throw new Error("Observation not found");
              const [submittedBy, actionBy, reviewBy] = await Promise.all([
                obs.submittedId ? this.userRepository.findById(obs.submittedId) : {},
                obs.actionOwnerId ? this.userRepository.findById(obs.actionOwnerId) : {},
                obs.reviewerId ? this.userRepository.findById(obs.reviewerId) : {},
              ]);
              applicationDetails = {
                ...obs,
                submittedBy,
                actionBy,
                reviewBy,
              };

              break;
            }
          case 'PermitToWork':
            {
              const ptw = await this.permitReportRepository.findById(i.objectId, {
                include: [
                  { relation: 'locationOne' },
                  { relation: 'locationTwo' },
                  { relation: 'locationThree' },
                  { relation: 'locationFour' },
                  { relation: 'locationFive' },
                  { relation: 'locationSix' },
                ],
              });
              if (!ptw) throw new Error("ptw not found");
              const [applicant, approver, assessor, dcsoApprover] = await Promise.all([
                ptw.applicantId ? this.userRepository.findById(ptw.applicantId) : {},
                ptw.approverId ? this.userRepository.findById(ptw.approverId) : {},
                ptw.assessorId ? this.userRepository.findById(ptw.assessorId) : {},
                ptw.dcsoApproverId ? this.userRepository.findById(ptw.dcsoApproverId) : {},
              ]);
              applicationDetails = {
                ...ptw,
                applicant,
                approver,
                assessor,
                dcsoApprover
              };
              break;
            }
          case 'DOC':
            {
              const doc = await this.docRepository.findById(i.objectId);
              if (!doc) throw new Error("Doc not found");
              const [initiator, creator, reviewer, DocApprover] = await Promise.all([
                doc.initiatorId ? this.userRepository.findById(doc.initiatorId) : {},
                doc.creatorId ? this.userRepository.findById(doc.creatorId) : {},
                doc.reviewerId ? this.userRepository.findById(doc.reviewerId) : {},
                doc.approverId ? this.userRepository.findById(doc.approverId) : {},
                // ptw.dcsoApproverId ? this.userRepository.findById(ptw.dcsoApproverId) : {},
              ]);
              applicationDetails = {
                ...doc,
                initiator,
                creator,
                reviewer,
                DocApprover
              };
              break;
            }
          case 'RA':
            {
              const ra = await this.riskAssessmentRepository.findById(i.objectId);
              if (!ra) throw new Error("RA not found");

              applicationDetails = {
                ...ra,

              };
              break;
            }

          case 'GoodCatch':
            {
              const gc = await this.goodCatchRepository.findById(i.objectId, {
                include: [

                  { relation: 'locationThree' },
                  { relation: 'locationFour' },

                  { relation: 'reporter' },
                  { relation: 'admin' },
                  { relation: 'actionOwner' },

                ],

              });
              if (!gc) throw new Error("RA not found");

              applicationDetails = {
                ...gc,

              };
              break;
            }
          case 'AIR':
            {
              const air = await this.airReportRepository.findById(i.objectId, {
                include: [
                  { relation: 'locationOne' },
                  { relation: 'locationTwo' },
                  { relation: 'locationThree' },
                  { relation: 'locationFour' },
                  { relation: 'locationFive' },
                  { relation: 'locationSix' },
                  { relation: 'reviewer' },
                  { relation: 'reporter' },
                  { relation: 'surveyor' },
                  { relation: 'estimator' },
                  { relation: 'trainee' },
                  { relation: 'gmOps' },
                  { relation: 'thirdParty' },
                  { relation: 'security' },
                  { relation: 'costReviewer' },
                  { relation: 'financer' },
                  { relation: 'dutyEngManager' },
                  { relation: 'medicalOfficer' }
                ],

              });
              if (!air) throw new Error("IR not found");

              applicationDetails = {
                ...air
              };

              const propertiesToKeep = ['id', 'application', 'personInvolved', 'personnelImpacted', 'description', 'isArchive', 'locationOne', 'locationTwo', 'locationThree', 'locationFour', 'locationOneId', 'locationTwoId', 'locationThreeId', 'locationFourId', 'returnStatus', 'actionType', 'maskId', 'created', 'modified', 'incidentDate', 'reporter', 'reviewer', 'surveyor', 'estimator', 'trainee', 'gmOps', 'medicalOfficer', 'thirdParty', 'security', 'costReviewer', 'financer', 'dutyEngManager'];

              for (const prop in applicationDetails) {
                if (!propertiesToKeep.includes(prop)) {
                  delete applicationDetails[prop];
                }
              }

              break;
            }
        }
        results.push({ ...i, applicationDetails });
      } catch (error) {
        // Log the error, do not add the item to the results
        console.error(`Error processing action ${i.objectId}:`, error);
        // Optionally, you might want to log or handle this error more visibly
      }
    };

    return results;

  }

  @get('/all-actions')
  @response(200, {
    description: 'Array of Action model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(Action, { includeRelations: true }),
        },
      },
    },
  })
  async findAllActions(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.filter(Action) filter?: Filter<Action>,
  ): Promise<any> {
    const email = '<EMAIL>'
    const user = await this.userRepository.findOne({ where: { email: email } });

    let actions: any = await this.actionRepository.find();


    actions = actions.filter((action: { assignedToId: string | string[]; }) =>
      user?.id && action.assignedToId && action.assignedToId.includes(user.id)
    );

    const actionsWithSubmittedBy = await Promise.all(actions.map(async (action: { submittedById: any; }) => {
      const submittedBy = await this.userRepository.findOne({
        where: { id: action.submittedById },
        fields: {
          id: true,
          firstName: true,
          email: true
        }
      });
      return { ...action, actionSubmittedBy: submittedBy };
    }));

    let results = [];

    for (const i of actionsWithSubmittedBy) {

      try {
        let applicationDetails: any = {};
        switch (i.application) {
          case 'Observation':
            {
              const obs = await this.observationReportRepository.findById(i.objectId, {
                include: [
                  { relation: 'locationOne' },
                  { relation: 'locationTwo' },
                  { relation: 'locationThree' },
                  { relation: 'locationFour' },
                  { relation: 'locationFive' },
                  { relation: 'locationSix' },
                ],
              });
              if (!obs) throw new Error("Observation not found");
              const [submittedBy, actionBy, reviewBy] = await Promise.all([
                obs.submittedId ? this.userRepository.findById(obs.submittedId) : {},
                obs.actionOwnerId ? this.userRepository.findById(obs.actionOwnerId) : {},
                obs.reviewerId ? this.userRepository.findById(obs.reviewerId) : {},
              ]);
              applicationDetails = {
                ...obs,
                submittedBy,
                actionBy,
                reviewBy,
              };

              break;
            }
          case 'PermitToWork':
            {
              const ptw = await this.permitReportRepository.findById(i.objectId, {
                include: [
                  { relation: 'locationOne' },
                  { relation: 'locationTwo' },
                  { relation: 'locationThree' },
                  { relation: 'locationFour' },
                  { relation: 'locationFive' },
                  { relation: 'locationSix' },
                ],
              });
              if (!ptw) throw new Error("ptw not found");
              const [applicant, approver, assessor, dcsoApprover] = await Promise.all([
                ptw.applicantId ? this.userRepository.findById(ptw.applicantId) : {},
                ptw.approverId ? this.userRepository.findById(ptw.approverId) : {},
                ptw.assessorId ? this.userRepository.findById(ptw.assessorId) : {},
                ptw.dcsoApproverId ? this.userRepository.findById(ptw.dcsoApproverId) : {},
              ]);
              applicationDetails = {
                ...ptw,
                applicant,
                approver,
                assessor,
                dcsoApprover
              };
              break;
            }
          case 'DOC':
            {
              const doc = await this.docRepository.findById(i.objectId);
              if (!doc) throw new Error("Doc not found");
              const [initiator, creator, reviewer, DocApprover] = await Promise.all([
                doc.initiatorId ? this.userRepository.findById(doc.initiatorId) : {},
                doc.creatorId ? this.userRepository.findById(doc.creatorId) : {},
                doc.reviewerId ? this.userRepository.findById(doc.reviewerId) : {},
                doc.approverId ? this.userRepository.findById(doc.approverId) : {},
                // ptw.dcsoApproverId ? this.userRepository.findById(ptw.dcsoApproverId) : {},
              ]);
              applicationDetails = {
                ...doc,
                initiator,
                creator,
                reviewer,
                DocApprover
              };
              break;
            }
          case 'RA':
            {
              const ra = await this.riskAssessmentRepository.findById(i.objectId);
              if (!ra) throw new Error("RA not found");

              applicationDetails = {
                ...ra,

              };
              break;
            }
          case 'AIR':
            {
              const air = await this.airReportRepository.findById(i.objectId, {
                include: [
                  { relation: 'locationOne' },
                  { relation: 'locationTwo' },
                  { relation: 'locationThree' },
                  { relation: 'locationFour' },
                  { relation: 'locationFive' },
                  { relation: 'locationSix' },
                  { relation: 'reviewer' },
                  { relation: 'reporter' },
                  { relation: 'surveyor' },
                  { relation: 'estimator' },
                  { relation: 'trainee' },
                  { relation: 'gmOps' },
                  { relation: 'thirdParty' },
                  { relation: 'security' },
                  { relation: 'costReviewer' },
                  { relation: 'financer' },
                  { relation: 'dutyEngManager' },
                  { relation: 'medicalOfficer' }
                ],

              });
              if (!air) throw new Error("IR not found");

              applicationDetails = {
                ...air
              };

              const propertiesToKeep = ['id', 'application', 'personInvolved', 'personnelImpacted', 'description', 'isArchive', 'locationOne', 'locationTwo', 'locationThree', 'locationFour', 'locationOneId', 'locationTwoId', 'locationThreeId', 'locationFourId', 'returnStatus', 'actionType', 'maskId', 'created', 'modified', 'incidentDate', 'reporter', 'reviewer', 'surveyor', 'estimator', 'trainee', 'gmOps', 'medicalOfficer', 'thirdParty', 'security', 'costReviewer', 'financer', 'dutyEngManager'];

              for (const prop in applicationDetails) {
                if (!propertiesToKeep.includes(prop)) {
                  delete applicationDetails[prop];
                }
              }

              break;
            }
        }
        results.push({ ...i, applicationDetails });
      } catch (error) {
        // Log the error, do not add the item to the results
        console.error(`Error processing action ${i.objectId}:`, error);
        // Optionally, you might want to log or handle this error more visibly
      }
    };

    return results;

  }

  @get('/my-actions')
  @response(200, {
    description: 'Array of Action model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(Action, { includeRelations: true }),
        },
      },
    },
  })
  async findMy(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.filter(Action) filter?: Filter<Action>,
  ): Promise<any> {
    const email = '<EMAIL>'
    const user = await this.userRepository.findOne({ where: { email: email } });

    let actions = await this.actionRepository.find({ where: { submittedById: user?.id } });
    const action = await Promise.all(actions.map(async i => {
      let applicationDetails: any = {};
      switch (i.application) {
        case 'Observation':
          {
            const obs = await this.observationReportRepository.findById(i.objectId, {
              include: [
                { relation: 'locationOne' },
                { relation: 'locationTwo' },
                { relation: 'locationThree' },
                { relation: 'locationFour' },
                { relation: 'locationFive' },
                { relation: 'locationSix' },
              ],
            });

            const [submittedBy, actionBy, reviewBy] = await Promise.all([
              obs.submittedId ? this.userRepository.findById(obs.submittedId) : {},
              obs.actionOwnerId ? this.userRepository.findById(obs.actionOwnerId) : {},
              obs.reviewerId ? this.userRepository.findById(obs.reviewerId) : {},
            ]);
            applicationDetails = {
              ...obs,
              submittedBy,
              actionBy,
              reviewBy,
            };

            break;
          }
        case 'PermitToWork':
          {
            const ptw = await this.permitReportRepository.findById(i.objectId, {
              include: [
                { relation: 'locationOne' },
                { relation: 'locationTwo' },
                { relation: 'locationThree' },
                { relation: 'locationFour' },
                { relation: 'locationFive' },
                { relation: 'locationSix' },
              ],
            });

            const [applicant, approver, assessor, dcsoApprover] = await Promise.all([
              ptw.applicantId ? this.userRepository.findById(ptw.applicantId) : {},
              ptw.approverId ? this.userRepository.findById(ptw.approverId) : {},
              ptw.assessorId ? this.userRepository.findById(ptw.assessorId) : {},
              ptw.dcsoApproverId ? this.userRepository.findById(ptw.dcsoApproverId) : {},
            ]);
            applicationDetails = {
              ...ptw,
              applicant,
              approver,
              assessor,
              dcsoApprover
            };
            break;
          }
        case 'DOC':
          {
            const doc = await this.docRepository.findById(i.objectId);

            const [initiator, creator, reviewer, DocApprover] = await Promise.all([
              doc.initiatorId ? this.userRepository.findById(doc.initiatorId) : {},
              doc.creatorId ? this.userRepository.findById(doc.creatorId) : {},
              doc.reviewerId ? this.userRepository.findById(doc.reviewerId) : {},
              doc.approverId ? this.userRepository.findById(doc.approverId) : {},
              // ptw.dcsoApproverId ? this.userRepository.findById(ptw.dcsoApproverId) : {},
            ]);
            applicationDetails = {
              ...doc,
              initiator,
              creator,
              reviewer,
              DocApprover
            };
            break;
          }
        case 'RA':
          {
            const ra = await this.riskAssessmentRepository.findById(i.objectId);


            applicationDetails = {
              ...ra,

            };
            break;
          }
        case 'AIR':
          {
            const air = await this.airReportRepository.findById(i.objectId, {
              include: [
                { relation: 'locationOne' },
                { relation: 'locationTwo' },
                { relation: 'locationThree' },
                { relation: 'locationFour' },
                { relation: 'locationFive' },
                { relation: 'locationSix' },
                { relation: 'reviewer' },
                { relation: 'reporter' },
                { relation: 'surveyor' },
                { relation: 'estimator' },
                { relation: 'trainee' },
                { relation: 'gmOps' },
                { relation: 'thirdParty' },
                { relation: 'security' },
                { relation: 'costReviewer' },
                { relation: 'financer' },
                { relation: 'dutyEngManager' },
                { relation: 'medicalOfficer' }
              ],

            });


            applicationDetails = {
              ...air
            };

            const propertiesToKeep = ['id', 'application', 'description', 'isArchive', 'locationOne', 'locationTwo', 'locationThree', 'locationFour', 'locationOneId', 'locationTwoId', 'locationThreeId', 'locationFourId', 'returnStatus', 'actionType', 'maskId', 'created', 'modified', 'incidentDate', 'reporter', 'reviewer', 'surveyor', 'estimator', 'trainee', 'gmOps', 'medicalOfficer', 'thirdParty', 'security', 'costReviewer', 'financer', 'dutyEngManager'];

            for (const prop in applicationDetails) {
              if (!propertiesToKeep.includes(prop)) {
                delete applicationDetails[prop];
              }
            }

            break;
          }
      }
      return { ...i, applicationDetails: applicationDetails };
    }))

    return action;

  }

  @patch('/actions')
  @response(200, {
    description: 'Action PATCH success count',
    content: { 'application/json': { schema: CountSchema } },
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Action, { partial: true }),
        },
      },
    })
    action: Action,
    @param.where(Action) where?: Where<Action>,
  ): Promise<Count> {
    return this.actionRepository.updateAll(action, where);
  }

  @get('/actions/{id}')
  @response(200, {
    description: 'Action model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(Action, { includeRelations: true }),
      },
    },
  })
  async findById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @param.filter(Action, { exclude: 'where' }) filter?: FilterExcludingWhere<Action>
  ): Promise<Action> {
    const email = '<EMAIL>'
    const user = await this.userRepository.findOne({ where: { email: email } });
    const action = await this.actionRepository.findById(id);
    if (action.assignedToId === user?.id) {
      return action;
    } else {
      throw new HttpErrors.Unauthorized('UnAuthorized')
    }

  }

  @patch('/actions/{id}')
  @response(204, {
    description: 'Action PATCH success',
  })
  async updateById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Action, { partial: true }),
        },
      },
    })
    action: Action,
  ): Promise<void> {
    const email = '<EMAIL>'
    const user = await this.userRepository.findOne({ where: { email: email } });
    action.status = 'submitted'
    if (action.actionType === 'reviewer') {
      delete action.actionType
      await this.observationReportRepository.updateById(action.objectId, { status: 'In Review', actionTaken: action.actionTaken, reviewerId: action.assignedToId && action.assignedToId.length === 1 ? action.assignedToId[0] : '' });


      const actionItem = {
        application: "Observation",
        actionType: "reviewer",
        comments: action.actionTaken,
        actionTaken: action.actionTaken,
        actionToBeTaken: action.actionToBeTaken,
        description: action.description,
        dueDate: action.dueDate,
        uploads: action.uploads,
        status: "open",
        createdDate: action.createdDate,
        objectId: action.objectId,
        submittedById: user?.id,
        assignedToId: action.assignedToId
      }

      if (action.assignedToId && action.objectId) {
        const observationDetails = await this.observationReportRepository.findById(action.objectId)
        const reviewerDetails = await this.userRepository.findById(action.assignedToId[0])
        if (reviewerDetails) { this.sqsService.sendMessage(reviewerDetails, `Review the Observation ${observationDetails.maskId}`, `${user?.firstName} has taken action and submitted the Observation ${observationDetails.maskId} for Review.`) } else { throw new HttpErrors.NotFound(`User not found. Try again`); }
      }

      await this.actionRepository.create(actionItem)

    }

    if (action.actionType === 'approve') {

      if (action.objectId) {
        await this.observationReportRepository.updateById(action.objectId, { status: 'Approved', actionTaken: action.actionTaken });
        const observationDetails = await this.observationReportRepository.findById(action.objectId)

        if (observationDetails.submittedId && observationDetails.actionOwnerId && observationDetails.reviewerId) {
          const submittedUser = await this.userRepository.findById(observationDetails.submittedId);
          const actionOwnerUser = await this.userRepository.findById(observationDetails.actionOwnerId);
          const reviewerUser = await this.userRepository.findById(observationDetails.reviewerId);

          if (submittedUser) { this.sqsService.sendMessage(submittedUser, `Observation ${observationDetails.maskId} has been verified and closed`, `$Observation ${observationDetails.maskId} has been verified and closed`) } else { throw new HttpErrors.NotFound(`User not found. Try again`); }

          if (actionOwnerUser) { this.sqsService.sendMessage(actionOwnerUser, `Observation ${observationDetails.maskId} has been verified and closed`, `$Observation ${observationDetails.maskId} has been verified and closed`) } else { throw new HttpErrors.NotFound(`User not found. Try again`); }

          if (reviewerUser) { this.sqsService.sendMessage(reviewerUser, `Observation ${observationDetails.maskId} has been verified and closed`, `$Observation ${observationDetails.maskId} has been verified and closed`) } else { throw new HttpErrors.NotFound(`User not found. Try again`); }

        }

      }

    }

    if (action.actionType === 'reject') {
      await this.observationReportRepository.updateById(action.objectId, { status: 'Returned', actionOwnerId: action.assignedToId && action.assignedToId.length === 1 ? action.assignedToId[0] : '' });
      const actionItem = {
        application: "Observation",
        actionType: "action_owner",
        comments: action.comments,
        actionTaken: action.actionTaken,
        actionToBeTaken: action.actionToBeTaken,
        description: action.description,
        dueDate: action.dueDate,
        status: "returned",
        createdDate: action.createdDate,
        uploads: action.uploads,
        objectId: action.objectId,
        submittedById: user?.id,
        assignedToId: action.assignedToId
      }
      if (action.assignedToId && action.objectId) {
        const observationDetails = await this.observationReportRepository.findById(action.objectId)
        const actionOwnerDetails = await this.userRepository.findById(action.assignedToId[0])
        if (actionOwnerDetails) { this.sqsService.sendMessage(actionOwnerDetails, `Observation ${observationDetails.maskId} Returned by ${user?.firstName}`, `${user?.firstName} has reviewed and returned the Observation ${observationDetails.maskId}. Please take corrective actions and resubmit`) } else { throw new HttpErrors.NotFound(`User not found. Try again`); }
      }
      await this.actionRepository.create(actionItem)
    }
    delete action.assignedToId
    await this.actionRepository.updateById(id, action);
  }



  @patch('/actions-read/{id}')
  @response(204, {
    description: 'Action PATCH success',
  })
  async updateStatusById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Action, { partial: true }),
        },
      },
    })
    action: Action,
  ): Promise<void> {
    const email = '<EMAIL>'
    const user = await this.userRepository.findOne({ where: { email: email } });
    const actionData = await this.actionRepository.findById(id)

    if (actionData.assignedToId === user?.id)
      action.status = 'read'

    await this.actionRepository.updateById(id, action);
  }

  @put('/actions/{id}')
  @response(204, {
    description: 'Action PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() action: Action,
  ): Promise<void> {
    await this.actionRepository.replaceById(id, action);
  }

  @del('/actions/{id}')
  @response(204, {
    description: 'Action DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.actionRepository.deleteById(id);
  }
}
