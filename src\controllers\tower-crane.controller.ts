import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {TowerCrane} from '../models';
import {TowerCraneRepository} from '../repositories';

export class TowerCraneController {
  constructor(
    @repository(TowerCraneRepository)
    public towerCraneRepository : TowerCraneRepository,
  ) {}

  @post('/tower-cranes')
  @response(200, {
    description: 'TowerCrane model instance',
    content: {'application/json': {schema: getModelSchemaRef(TowerCrane)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(TowerCrane, {
            title: 'NewTowerCrane',
            exclude: ['id'],
          }),
        },
      },
    })
    towerCrane: Omit<TowerCrane, 'id'>,
  ): Promise<TowerCrane> {
    return this.towerCraneRepository.create(towerCrane);
  }

  @get('/tower-cranes/count')
  @response(200, {
    description: 'TowerCrane model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(TowerCrane) where?: Where<TowerCrane>,
  ): Promise<Count> {
    return this.towerCraneRepository.count(where);
  }

  @get('/tower-cranes')
  @response(200, {
    description: 'Array of TowerCrane model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(TowerCrane, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(TowerCrane) filter?: Filter<TowerCrane>,
  ): Promise<TowerCrane[]> {
    return this.towerCraneRepository.find(filter);
  }

  @patch('/tower-cranes')
  @response(200, {
    description: 'TowerCrane PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(TowerCrane, {partial: true}),
        },
      },
    })
    towerCrane: TowerCrane,
    @param.where(TowerCrane) where?: Where<TowerCrane>,
  ): Promise<Count> {
    return this.towerCraneRepository.updateAll(towerCrane, where);
  }

  @get('/tower-cranes/{id}')
  @response(200, {
    description: 'TowerCrane model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(TowerCrane, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(TowerCrane, {exclude: 'where'}) filter?: FilterExcludingWhere<TowerCrane>
  ): Promise<TowerCrane> {
    return this.towerCraneRepository.findById(id, filter);
  }

  @patch('/tower-cranes/{id}')
  @response(204, {
    description: 'TowerCrane PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(TowerCrane, {partial: true}),
        },
      },
    })
    towerCrane: TowerCrane,
  ): Promise<void> {
    await this.towerCraneRepository.updateById(id, towerCrane);
  }

  @put('/tower-cranes/{id}')
  @response(204, {
    description: 'TowerCrane PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() towerCrane: TowerCrane,
  ): Promise<void> {
    await this.towerCraneRepository.replaceById(id, towerCrane);
  }

  @del('/tower-cranes/{id}')
  @response(204, {
    description: 'TowerCrane DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.towerCraneRepository.deleteById(id);
  }
}
