import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  Unit,
  StepTitle,
} from '../models';
import {UnitRepository} from '../repositories';

export class UnitStepTitleController {
  constructor(
    @repository(UnitRepository) protected unitRepository: UnitRepository,
  ) { }

  @get('/units/{id}/step-titles', {
    responses: {
      '200': {
        description: 'Array of Unit has many StepTitle',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(StepTitle)},
          },
        },
      },
    },
  })
  async find(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<StepTitle>,
  ): Promise<StepTitle[]> {
    return this.unitRepository.stepTitles(id).find(filter);
  }

  @post('/units/{id}/step-titles', {
    responses: {
      '200': {
        description: 'Unit model instance',
        content: {'application/json': {schema: getModelSchemaRef(StepTitle)}},
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof Unit.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(StepTitle, {
            title: 'NewStepTitleInUnit',
            exclude: ['id'],
            optional: ['unitId']
          }),
        },
      },
    }) stepTitle: Omit<StepTitle, 'id'>,
  ): Promise<StepTitle> {
    return this.unitRepository.stepTitles(id).create(stepTitle);
  }

  @patch('/units/{id}/step-titles', {
    responses: {
      '200': {
        description: 'Unit.StepTitle PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(StepTitle, {partial: true}),
        },
      },
    })
    stepTitle: Partial<StepTitle>,
    @param.query.object('where', getWhereSchemaFor(StepTitle)) where?: Where<StepTitle>,
  ): Promise<Count> {
    return this.unitRepository.stepTitles(id).patch(stepTitle, where);
  }

  @del('/units/{id}/step-titles', {
    responses: {
      '200': {
        description: 'Unit.StepTitle DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(StepTitle)) where?: Where<StepTitle>,
  ): Promise<Count> {
    return this.unitRepository.stepTitles(id).delete(where);
  }
}
