// Copyright IBM Corp. 2020. All Rights Reserved.
// Node module: loopback4-example-shopping
// This file is licensed under the MIT License.
// License text available at https://opensource.org/licenses/MIT

import {
  authenticate,
  TokenService,
  UserService
} from '@loopback/authentication';
import { TokenServiceBindings } from '@loopback/authentication-jwt';
import { authorize } from '@loopback/authorization';
import { inject } from '@loopback/core';
import { model, property, repository } from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  HttpErrors,
  param,
  patch,
  post,
  put,
  requestBody,
  response
} from '@loopback/rest';
import { SecurityBindings, securityId, UserProfile } from '@loopback/security';
import isemail from 'isemail';
import _ from 'lodash';
import { SentMessageInfo } from 'nodemailer';
import { PasswordHasherBindings, UserServiceBindings } from '../keys';
import { KeyAndPassword, ResetPasswordInit, User } from '../models';
import { Credentials, UserRepository, UserCredentialsRepository, UserLocationRoleRepository, IncidentRoleRepository, PlantRoleRepository, DocumentRoleRepository } from '../repositories';
import {
  basicAuthorization,
  PasswordHasher,

  UserManagementService,
  validateCredentials,
  validateKeyPassword
} from '../services';
import { OPERATION_SECURITY_SPEC } from '../utils';
import {
  CredentialsRequestBody,
  PasswordResetRequestBody,
  UserProfileSchema
} from './specs/user-controller.specs';
import axios from 'axios';
import { CognitoIdentityServiceProvider, SNS } from 'aws-sdk';
import { SqsService } from '../services/sqs-service.service';

const cognito = new CognitoIdentityServiceProvider({
  region: process.env.AWS_REGION,
  accessKeyId: process.env.AWS_COGNITO_ACCESS_KEY,
  secretAccessKey: process.env.AWS_COGNITO_SECRET_KEY
})

@model()
export class NewUserRequest extends User {
  @property({
    type: 'string',
    required: true,
  })
  password: string;
}


export class DeviceTokenRequest {
  @property({
    type: 'string',
    required: true,
  })
  deviceToken: string;
}

export class UserManagementController {
  constructor(
    @repository(UserRepository)
    public userRepository: UserRepository,
    @repository(UserLocationRoleRepository)
    public userLocationRoleRepository: UserLocationRoleRepository,
    @repository(UserCredentialsRepository)
    public userCredentialRepository: UserCredentialsRepository,
    @repository(IncidentRoleRepository)
    public incidentRoleRepository: IncidentRoleRepository,
    @repository(DocumentRoleRepository)
    public documentRoleRepository: DocumentRoleRepository,
    @repository(PlantRoleRepository)
    public plantRoleRepository: PlantRoleRepository,
    @inject(PasswordHasherBindings.PASSWORD_HASHER)
    public passwordHasher: PasswordHasher,
    @inject(TokenServiceBindings.TOKEN_SERVICE)
    public jwtService: TokenService,
    @inject(UserServiceBindings.USER_SERVICE)
    public userService: UserService<User, Credentials>,
    @inject(UserServiceBindings.USER_SERVICE)
    public userManagementService: UserManagementService,
    @inject('services.SqsService')
    public sqsService: SqsService,
  ) {


  }

  @get('/users/test', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'The current user profile',
        content: {
          'application/json': {
            schema: UserProfileSchema,
          },
        },
      },
    },

  })
  @authenticate('jwt')
  async testNotification(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
  ): Promise<any> {

    const email = '<EMAIL>'
    const user = await this.userRepository.findOne({ where: { email: email } });

    if (user) { return this.sqsService.sendMessage(user, 'Test Title', 'Test Message from API') } else { throw new HttpErrors.NotFound(`User not found. Try again`); }



  }

  @post('/users', {
    responses: {
      '200': {
        description: 'User',
        content: {
          'application/json': {
            schema: {
              'x-ts-type': User,
            },
          },
        },
      },
    },
  })
  @authenticate('jwt')
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(NewUserRequest, {
            title: 'NewUser',
          }),
        },
      },
    })
    newUserRequest: NewUserRequest,
  ): Promise<User> {
    // All new users have the "customer" role by default
    newUserRequest.roles = ['user'];
    // ensure a valid email value and password value
    // validateCredentials(_.pick(newUserRequest, ['email', 'password']));

    try {
      newUserRequest.resetKey = '';
      return await this.userManagementService.createUser(newUserRequest);
    } catch (error) {
      // MongoError 11000 duplicate key
      if (error.code === 11000 && error.errmsg.includes('index: uniqueEmail')) {
        throw new HttpErrors.Conflict('Email value is already taken');
      } else {
        throw error;
      }
    }
  }


  @post('/users/external', {
    responses: {
      '200': {
        description: 'User',
        content: {
          'application/json': {
            schema: {
              'x-ts-type': User,
            },
          },
        },
      },
    },
  })
  @authenticate('jwt')
  async createExternal(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(NewUserRequest, {
            title: 'NewUser',
          }),
        },
      },
    })
    newUserRequest: NewUserRequest,
  ): Promise<User> {
    // All new users have the "customer" role by default
    newUserRequest.roles = ['user'];
    newUserRequest.type = 'External';
    // ensure a valid email value and password value
    // validateCredentials(_.pick(newUserRequest, ['email', 'password']));

    try {
      newUserRequest.resetKey = '';

      const params = {
        UserPoolId: `${process.env.AWS_USER_POOL_ID}`,
        Username: newUserRequest.email,
        TemporaryPassword: newUserRequest.password,
        MessageAction: 'SUPPRESS',
        UserAttributes: [
          {
            Name: 'email',
            Value: newUserRequest.email
          },
          {
            Name: 'email_verified',
            Value: 'true'
          }
        ]
      };

      const awsNewUser = await cognito.adminCreateUser(params).promise();
      console.log(awsNewUser)
      if (awsNewUser.User?.Username) {
        newUserRequest.id = awsNewUser.User.Username;
        return await this.userManagementService.createUser(newUserRequest);
      } else {
        throw new HttpErrors.NotFound(`User not created. Try again`);
      }

    } catch (error) {
      // MongoError 11000 duplicate key
      if (error.code === 11000 && error.errmsg.includes('index: uniqueEmail')) {
        throw new HttpErrors.Conflict('Email value is already taken');
      } else {
        throw error;
      }
    }
  }

  @del('/users/{id}')
  @response(204, {
    description: 'User DELETE success',
  })
  @authenticate('jwt')
  async deleteUser(@param.path.string('id') id: string): Promise<void> {
    const user = await this.userRepository.findById(id);

    if (!user) {
      throw new HttpErrors.NotFound(`User with id ${id} not found.`);
    }

    await this.userRepository.delete(user);

  }
  @patch('/users/{userId}', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'User',
        content: {
          'application/json': {
            schema: {
              'x-ts-type': User,
            },
          },
        },
      },
    },
  })
  @authenticate('jwt')

  async set(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('userId') userId: string,
    @requestBody({ description: 'update user' }) user: User,
  ): Promise<void> {
    try {
      // Only admin can assign roles

      // console.log(user)
      return await this.userRepository.updateById(userId, user);
    } catch (e) {
      // console.log(e)
      return e;
    }
  }

  @get('/users/{userId}', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'User',
        content: {
          'application/json': {
            schema: {
              'x-ts-type': User,
            },
          },
        },
      },
    },
  })
  @authenticate('jwt')

  async findById(@param.path.string('userId') userId: string): Promise<User> {
    return this.userRepository.findById(userId);
  }




  @get('/users', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'User',
        content: {
          'application/json': {
            schema: {
              'x-ts-type': User,
            },
          },
        },
      },
    },
  })
  @authenticate('jwt')
  async find(): Promise<User[]> {
    return this.userRepository.find();
  }


  // 644acfc949e6a60b6940f99e for action plan reviewer
  @authenticate('jwt')
  @get('/action-assignee-list')
  async getActionAssignee(

  ): Promise<any> {
    const users = await this.userRepository.find()
    const id = "644acfd149e6a60b6940f99f";

    const filteredUsers = users.filter(user => {
      return user.customRoles?.ehs?.includes(id);
    }).map(user => {
      return {
        id: user.id,
        title: user.firstName
      };
    });

    return filteredUsers;

  }

  @post('/action-assignee-list', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'User',
        content: {
          'application/json': {
            schema: {
              'x-ts-type': User,
            },
          },
        },
      },
    },
  })
  // @authenticate('jwt')
  async findActionAssignee(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              locationOneId: { type: 'string' },
              locationTwoId: { type: 'string' },
              locationThreeId: { type: 'string' },
              locationFourId: { type: 'string' },
            },
            required: ['locationOneId', 'locationTwoId', 'locationThreeId', 'locationFourId'],
          },
        },
      },
    })
    payload: {
      locationOneId: string;
      locationTwoId: string;
      locationThreeId: string;
      locationFourId: string;
    }
  ): Promise<User[]> {
    const roleId = "644acfd149e6a60b6940f99f";
    const allLocationId = 'all';

    let whereCondition = {
      roles: { inq: [[roleId]] },
      or: [
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: [payload.locationThreeId] },
          locationFourId: { inq: [payload.locationFourId] },
        },
        {
          locationOneId: { inq: ['tier1-all'] },
          locationTwoId: '',
          locationThreeId: '',
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: ['tier2-all'] },
          locationThreeId: '',
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: ['tier3-all'] },
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: [payload.locationThreeId] },
          locationFourId: { inq: ['tier4-all'] },
        },
      ]
    };

    const userLocationRoles = await this.userLocationRoleRepository.find({
      where: whereCondition,
    });

    const userIds = userLocationRoles.map((userLocationRole) => userLocationRole.userId);
    const uniqueUserIds = Array.from(new Set(userIds));

    const users = await this.userRepository.find({
      where: { id: { inq: uniqueUserIds as string[] } },
    });

    return users;
  }

  @authenticate('jwt')
  @get('/eptw-general-list')
  async getPtwGeneral(

  ): Promise<any> {
    const users = await this.userRepository.find()
    const id = "644acfd149e6a60b6940f99f";

    const filteredUsers = users.filter(user => {
      return user.customRoles?.ehs?.includes(id);
    }).map(user => {
      return {
        id: user.id,
        title: user.firstName
      };
    });

    return filteredUsers;

  }


  @authenticate('jwt')
  @get('/action-reviewer-list')
  async getActionReviewer(

  ): Promise<any> {
    const users = await this.userRepository.find()
    const id = "6581351d6a70fc030e0de2c2";

    const filteredUsers = users.filter(user => {
      return user.customRoles?.ehs?.includes(id);
    }).map(user => {
      return {
        id: user.id,
        title: user.firstName
      };
    });

    return filteredUsers;

  }

  @post('/action-reviewer-list', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'User',
        content: {
          'application/json': {
            schema: {
              'x-ts-type': User,
            },
          },
        },
      },
    },
  })
  // @authenticate('jwt')
  async findActionReviewer(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              locationOneId: { type: 'string' },
              locationTwoId: { type: 'string' },
              locationThreeId: { type: 'string' },
              locationFourId: { type: 'string' },
            },
            required: ['locationOneId', 'locationTwoId', 'locationThreeId', 'locationFourId'],
          },
        },
      },
    })
    payload: {
      locationOneId: string;
      locationTwoId: string;
      locationThreeId: string;
      locationFourId: string;
    }
  ): Promise<User[]> {
    const roleId = "644acfc949e6a60b6940f99e";
    const allLocationId = 'all';

    let whereCondition = {
      roles: { inq: [[roleId]] },
      or: [
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: [payload.locationThreeId] },
          locationFourId: { inq: [payload.locationFourId] },
        },
        {
          locationOneId: { inq: ['tier1-all'] },
          locationTwoId: '',
          locationThreeId: '',
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: ['tier2-all'] },
          locationThreeId: '',
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: ['tier3-all'] },
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: [payload.locationThreeId] },
          locationFourId: { inq: ['tier4-all'] },
        },
      ]
    };

    const userLocationRoles = await this.userLocationRoleRepository.find({
      where: whereCondition,
    });

    const userIds = userLocationRoles.map((userLocationRole) => userLocationRole.userId);
    const uniqueUserIds = Array.from(new Set(userIds));

    const users = await this.userRepository.find({
      where: { id: { inq: uniqueUserIds as string[] } },
    });

    return users;
  }


  @post('/incident-owner-list', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'User',
        content: {
          'application/json': {
            schema: {
              'x-ts-type': User,
            },
          },
        },
      },
    },
  })
  // @authenticate('jwt')
  async findIncidentOwner(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              locationOneId: { type: 'string' },
              locationTwoId: { type: 'string' },
              locationThreeId: { type: 'string' },
              locationFourId: { type: 'string' },
              level: { type: 'string' },
            },
            required: ['locationOneId', 'locationTwoId', 'locationThreeId', 'locationFourId', 'level'],
          },
        },
      },
    })
    payload: {
      locationOneId: string;
      locationTwoId: string;
      locationThreeId: string;
      locationFourId: string;
      level: string;
    }
  ): Promise<User[]> {

    let roleId = "";
    switch (payload.level) {
      case '1': roleId = "64b7766dd5c7a6b02233901c"; break;
      case '2': roleId = "64b7766dd5c7a6b02233901c"; break;
      case '3': roleId = "64b77484d5c7a6b022339016"; break;
      case '4': roleId = "64b7756cd5c7a6b022339018"; break;
      case '5': roleId = "64b77582d5c7a6b022339019"; break;

      default: throw new HttpErrors.NotFound('Invalid Impact Classification');
    }

    const allLocationId = 'all';

    let whereCondition = {
      roles: { inq: [[roleId]] },
      or: [
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: [payload.locationThreeId] },
          locationFourId: { inq: [payload.locationFourId] },
        },
        {
          locationOneId: { inq: ['tier1-all'] },
          locationTwoId: '',
          locationThreeId: '',
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: ['tier2-all'] },
          locationThreeId: '',
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: ['tier3-all'] },
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: [payload.locationThreeId] },
          locationFourId: { inq: ['tier4-all'] },
        },
      ]
    };

    const userLocationRoles = await this.userLocationRoleRepository.find({
      where: whereCondition,
    });

    const userIds = userLocationRoles.map((userLocationRole) => userLocationRole.userId);
    const uniqueUserIds = Array.from(new Set(userIds));

    const users = await this.userRepository.find({
      where: { id: { inq: uniqueUserIds as string[] } },
    });

    return users;
  }

  @post('/users/ra-team-member', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'User',
        content: {
          'application/json': {
            schema: {
              'x-ts-type': User,
            },
          },
        },
      },
    },
  })
  // @authenticate('jwt')
  async findRaTeamMember(

    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              locationOneId: { type: 'string' },
              locationTwoId: { type: 'string' },
              locationThreeId: { type: 'string' },
              locationFourId: { type: 'string' },
            },
            required: ['locationOneId', 'locationTwoId', 'locationThreeId', 'locationFourId'],
          },
        },
      },
    })
    payload: {
      locationOneId: string;
      locationTwoId: string;
      locationThreeId: string;
      locationFourId: string;
    }
  ): Promise<User[]> {
    const roleId = "64d1d05320d96f3e26c30c13";
    const allLocationId = 'all';

    let whereCondition = {
      roles: { inq: [[roleId]] },
      or: [
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: [payload.locationThreeId] },
          locationFourId: { inq: [payload.locationFourId] },
        },
        {
          locationOneId: { inq: ['tier1-all'] },
          locationTwoId: '',
          locationThreeId: '',
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: ['tier2-all'] },
          locationThreeId: '',
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: ['tier3-all'] },
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: [payload.locationThreeId] },
          locationFourId: { inq: ['tier4-all'] },
        },
      ]
    };

    const userLocationRoles = await this.userLocationRoleRepository.find({
      where: whereCondition,
    });

    const userIds = userLocationRoles.map((userLocationRole) => userLocationRole.userId);
    const uniqueUserIds = Array.from(new Set(userIds));

    const users = await this.userRepository.find({
      where: { id: { inq: uniqueUserIds as string[] } },
      include: ['department', 'ghsOne', 'workingGroup', 'ehsRole']
    });

    return users;
  }

  @post('/users/air-reviewer', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'User',
        content: {
          'application/json': {
            schema: {
              'x-ts-type': User,
            },
          },
        },
      },
    },
  })
  // @authenticate('jwt')
  async findAirReviewer(

    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              locationOneId: { type: 'string' },
              locationTwoId: { type: 'string' },
              locationThreeId: { type: 'string' },
              locationFourId: { type: 'string' },
            },
            required: ['locationOneId', 'locationTwoId', 'locationThreeId', 'locationFourId'],
          },
        },
      },
    })
    payload: {
      locationOneId: string;
      locationTwoId: string;
      locationThreeId: string;
      locationFourId: string;
    }
  ): Promise<User[]> {
    const roleId = "64d0e6a320d96f3e26c30bba";
    const allLocationId = 'all';

    let whereCondition = {
      roles: { inq: [[roleId]] },
      or: [
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: [payload.locationThreeId] },
          locationFourId: { inq: [payload.locationFourId] },
        },
        {
          locationOneId: { inq: ['tier1-all'] },
          locationTwoId: '',
          locationThreeId: '',
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: ['tier2-all'] },
          locationThreeId: '',
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: ['tier3-all'] },
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: [payload.locationThreeId] },
          locationFourId: { inq: ['tier4-all'] },
        },
      ]
    };

    const userLocationRoles = await this.userLocationRoleRepository.find({
      where: whereCondition,
    });

    const userIds = userLocationRoles.map((userLocationRole) => userLocationRole.userId);
    const uniqueUserIds = Array.from(new Set(userIds));

    const users = await this.userRepository.find({
      where: { id: { inq: uniqueUserIds as string[] } },
    });

    return users;
  }


  @post('/users/observation-reviewer', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'User',
        content: {
          'application/json': {
            schema: {
              'x-ts-type': User,
            },
          },
        },
      },
    },
  })
  // @authenticate('jwt')
  async findObservationReviewer(

    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              locationOneId: { type: 'string' },
              locationTwoId: { type: 'string' },
              locationThreeId: { type: 'string' },
              locationFourId: { type: 'string' },
            },
            required: ['locationOneId', 'locationTwoId', 'locationThreeId', 'locationFourId'],
          },
        },
      },
    })
    payload: {
      locationOneId: string;
      locationTwoId: string;
      locationThreeId: string;
      locationFourId: string;
    }
  ): Promise<User[]> {
    const roleId = "6581351d6a70fc030e0de2c2";
    const allLocationId = 'all';

    let whereCondition = {
      roles: { inq: [[roleId]] },
      or: [
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: [payload.locationThreeId] },
          locationFourId: { inq: [payload.locationFourId] },
        },
        {
          locationOneId: { inq: ['tier1-all'] },
          locationTwoId: '',
          locationThreeId: '',
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: ['tier2-all'] },
          locationThreeId: '',
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: ['tier3-all'] },
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: [payload.locationThreeId] },
          locationFourId: { inq: ['tier4-all'] },
        },
      ]
    };

    const userLocationRoles = await this.userLocationRoleRepository.find({
      where: whereCondition,
    });

    const userIds = userLocationRoles.map((userLocationRole) => userLocationRole.userId);
    const uniqueUserIds = Array.from(new Set(userIds));

    const users = await this.userRepository.find({
      where: { id: { inq: uniqueUserIds as string[] } },
    });

    return users;
  }

  @post('/users/observation-owner', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'User',
        content: {
          'application/json': {
            schema: {
              'x-ts-type': User,
            },
          },
        },
      },
    },
  })
  // @authenticate('jwt')
  async findObservationOwner(

    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              locationOneId: { type: 'string' },
              locationTwoId: { type: 'string' },
              locationThreeId: { type: 'string' },
              locationFourId: { type: 'string' },
            },
            required: ['locationOneId', 'locationTwoId', 'locationThreeId', 'locationFourId'],
          },
        },
      },
    })
    payload: {
      locationOneId: string;
      locationTwoId: string;
      locationThreeId: string;
      locationFourId: string;
    }
  ): Promise<User[]> {
    const roleId = "658135016a70fc030e0de2c1";
    const allLocationId = 'all';

    let whereCondition = {
      roles: { inq: [[roleId]] },
      or: [
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: [payload.locationThreeId] },
          locationFourId: { inq: [payload.locationFourId] },
        },
        {
          locationOneId: { inq: ['tier1-all'] },
          locationTwoId: '',
          locationThreeId: '',
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: ['tier2-all'] },
          locationThreeId: '',
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: ['tier3-all'] },
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: [payload.locationThreeId] },
          locationFourId: { inq: ['tier4-all'] },
        },
      ]
    };

    const userLocationRoles = await this.userLocationRoleRepository.find({
      where: whereCondition,
    });

    const userIds = userLocationRoles.map((userLocationRole) => userLocationRole.userId);
    const uniqueUserIds = Array.from(new Set(userIds));

    const users = await this.userRepository.find({
      where: { id: { inq: uniqueUserIds as string[] } },
    });

    return users;
  }

  @post('/users/document-initiator', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'User',
        content: {
          'application/json': {
            schema: {
              'x-ts-type': User,
            },
          },
        },
      },
    },
  })
  // @authenticate('jwt')
  async findDocumentInitiator(

    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              locationOneId: { type: 'string' },
              locationTwoId: { type: 'string' },
              locationThreeId: { type: 'string' },
              locationFourId: { type: 'string' },
            },
            required: ['locationOneId', 'locationTwoId', 'locationThreeId', 'locationFourId'],
          },
        },
      },
    })
    payload: {
      locationOneId: string;
      locationTwoId: string;
      locationThreeId: string;
      locationFourId: string;
    }
  ): Promise<User[]> {
    const roleId = "658402cb46de3082904f7564";
    const allLocationId = 'all';

    let whereCondition = {
      roles: { inq: [[roleId]] },
      or: [
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: [payload.locationThreeId] },
          locationFourId: { inq: [payload.locationFourId] },
        },
        {
          locationOneId: { inq: ['tier1-all'] },
          locationTwoId: '',
          locationThreeId: '',
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: ['tier2-all'] },
          locationThreeId: '',
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: ['tier3-all'] },
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: [payload.locationThreeId] },
          locationFourId: { inq: ['tier4-all'] },
        },
      ]
    };

    const userLocationRoles = await this.userLocationRoleRepository.find({
      where: whereCondition,
    });

    const userIds = userLocationRoles.map((userLocationRole) => userLocationRole.userId);
    const uniqueUserIds = Array.from(new Set(userIds));

    const users = await this.userRepository.find({
      where: { id: { inq: uniqueUserIds as string[] } },
    });

    return users;
  }

  @post('/users/document-creator', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'User',
        content: {
          'application/json': {
            schema: {
              'x-ts-type': User,
            },
          },
        },
      },
    },
  })
  // @authenticate('jwt')
  async findDocumentCreator(

    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              locationOneId: { type: 'string' },
              locationTwoId: { type: 'string' },
              locationThreeId: { type: 'string' },
              locationFourId: { type: 'string' },
            },
            required: ['locationOneId', 'locationTwoId', 'locationThreeId', 'locationFourId'],
          },
        },
      },
    })
    payload: {
      locationOneId: string;
      locationTwoId: string;
      locationThreeId: string;
      locationFourId: string;
    }
  ): Promise<User[]> {
    const roleId = "658402d646de3082904f7565";
    const allLocationId = 'all';

    let whereCondition = {
      roles: { inq: [[roleId]] },
      or: [
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: [payload.locationThreeId] },
          locationFourId: { inq: [payload.locationFourId] },
        },
        {
          locationOneId: { inq: ['tier1-all'] },
          locationTwoId: '',
          locationThreeId: '',
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: ['tier2-all'] },
          locationThreeId: '',
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: ['tier3-all'] },
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: [payload.locationThreeId] },
          locationFourId: { inq: ['tier4-all'] },
        },
      ]
    };

    const userLocationRoles = await this.userLocationRoleRepository.find({
      where: whereCondition,
    });

    const userIds = userLocationRoles.map((userLocationRole) => userLocationRole.userId);
    const uniqueUserIds = Array.from(new Set(userIds));

    const users = await this.userRepository.find({
      where: { id: { inq: uniqueUserIds as string[] } },
    });

    return users;
  }

  @post('/users/document-reviewer', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'User',
        content: {
          'application/json': {
            schema: {
              'x-ts-type': User,
            },
          },
        },
      },
    },
  })
  // @authenticate('jwt')
  async findDocumentReviewer(

    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              locationOneId: { type: 'string' },
              locationTwoId: { type: 'string' },
              locationThreeId: { type: 'string' },
              locationFourId: { type: 'string' },
            },
            required: ['locationOneId', 'locationTwoId', 'locationThreeId', 'locationFourId'],
          },
        },
      },
    })
    payload: {
      locationOneId: string;
      locationTwoId: string;
      locationThreeId: string;
      locationFourId: string;
    }
  ): Promise<User[]> {
    const roleId = "658402df46de3082904f7566";
    const allLocationId = 'all';

    let whereCondition = {
      roles: { inq: [[roleId]] },
      or: [
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: [payload.locationThreeId] },
          locationFourId: { inq: [payload.locationFourId] },
        },
        {
          locationOneId: { inq: ['tier1-all'] },
          locationTwoId: '',
          locationThreeId: '',
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: ['tier2-all'] },
          locationThreeId: '',
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: ['tier3-all'] },
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: [payload.locationThreeId] },
          locationFourId: { inq: ['tier4-all'] },
        },
      ]
    };

    const userLocationRoles = await this.userLocationRoleRepository.find({
      where: whereCondition,
    });

    const userIds = userLocationRoles.map((userLocationRole) => userLocationRole.userId);
    const uniqueUserIds = Array.from(new Set(userIds));

    const users = await this.userRepository.find({
      where: { id: { inq: uniqueUserIds as string[] } },
    });

    return users;
  }

  @post('/users/document-approver', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'User',
        content: {
          'application/json': {
            schema: {
              'x-ts-type': User,
            },
          },
        },
      },
    },
  })
  // @authenticate('jwt')
  async findDocumentApprover(

    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              locationOneId: { type: 'string' },
              locationTwoId: { type: 'string' },
              locationThreeId: { type: 'string' },
              locationFourId: { type: 'string' },
            },
            required: ['locationOneId', 'locationTwoId', 'locationThreeId', 'locationFourId'],
          },
        },
      },
    })
    payload: {
      locationOneId: string;
      locationTwoId: string;
      locationThreeId: string;
      locationFourId: string;
    }
  ): Promise<User[]> {
    const roleId = "658402e446de3082904f7567";
    const allLocationId = 'all';

    let whereCondition = {
      roles: { inq: [[roleId]] },
      or: [
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: [payload.locationThreeId] },
          locationFourId: { inq: [payload.locationFourId] },
        },
        {
          locationOneId: { inq: ['tier1-all'] },
          locationTwoId: '',
          locationThreeId: '',
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: ['tier2-all'] },
          locationThreeId: '',
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: ['tier3-all'] },
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: [payload.locationThreeId] },
          locationFourId: { inq: ['tier4-all'] },
        },
      ]
    };

    const userLocationRoles = await this.userLocationRoleRepository.find({
      where: whereCondition,
    });

    const userIds = userLocationRoles.map((userLocationRole) => userLocationRole.userId);
    const uniqueUserIds = Array.from(new Set(userIds));

    const users = await this.userRepository.find({
      where: { id: { inq: uniqueUserIds as string[] } },
    });

    return users;
  }

  @post('/users/air-surveyor', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'User',
        content: {
          'application/json': {
            schema: {
              'x-ts-type': User,
            },
          },
        },
      },
    },
  })
  // @authenticate('jwt')
  async findAirSurveyor(

    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              locationOneId: { type: 'string' },
              locationTwoId: { type: 'string' },
              locationThreeId: { type: 'string' },
              locationFourId: { type: 'string' },
            },
            required: ['locationOneId', 'locationTwoId', 'locationThreeId', 'locationFourId'],
          },
        },
      },
    })
    payload: {
      locationOneId: string;
      locationTwoId: string;
      locationThreeId: string;
      locationFourId: string;
    }
  ): Promise<User[]> {
    const roleId = "64d45c09a46f6405f2232654";
    const allLocationId = 'all';

    let whereCondition = {
      roles: { inq: [[roleId]] },
      or: [
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: [payload.locationThreeId] },
          locationFourId: { inq: [payload.locationFourId] },
        },
        {
          locationOneId: { inq: ['tier1-all'] },
          locationTwoId: '',
          locationThreeId: '',
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: ['tier2-all'] },
          locationThreeId: '',
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: ['tier3-all'] },
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: [payload.locationThreeId] },
          locationFourId: { inq: ['tier4-all'] },
        },
      ]
    };

    const userLocationRoles = await this.userLocationRoleRepository.find({
      where: whereCondition,
    });

    const userIds = userLocationRoles.map((userLocationRole) => userLocationRole.userId);
    const uniqueUserIds = Array.from(new Set(userIds));

    const users = await this.userRepository.find({
      where: { id: { inq: uniqueUserIds as string[] } },
    });

    return users;
  }



  @post('/users/air-cost-estimator', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'User',
        content: {
          'application/json': {
            schema: {
              'x-ts-type': User,
            },
          },
        },
      },
    },
  })
  // @authenticate('jwt')
  async findCostEstimator(

    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              locationOneId: { type: 'string' },
              locationTwoId: { type: 'string' },
              locationThreeId: { type: 'string' },
              locationFourId: { type: 'string' },
            },
            required: ['locationOneId', 'locationTwoId', 'locationThreeId', 'locationFourId'],
          },
        },
      },
    })
    payload: {
      locationOneId: string;
      locationTwoId: string;
      locationThreeId: string;
      locationFourId: string;
    }
  ): Promise<User[]> {
    const roleId = "64d495a7a3e49d1da2e04f4c";
    const allLocationId = 'all';

    let whereCondition = {
      roles: { inq: [[roleId]] },
      or: [
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: [payload.locationThreeId] },
          locationFourId: { inq: [payload.locationFourId] },
        },
        {
          locationOneId: { inq: ['tier1-all'] },
          locationTwoId: '',
          locationThreeId: '',
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: ['tier2-all'] },
          locationThreeId: '',
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: ['tier3-all'] },
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: [payload.locationThreeId] },
          locationFourId: { inq: ['tier4-all'] },
        },
      ]
    };

    const userLocationRoles = await this.userLocationRoleRepository.find({
      where: whereCondition,
    });

    const userIds = userLocationRoles.map((userLocationRole) => userLocationRole.userId);
    const uniqueUserIds = Array.from(new Set(userIds));

    const users = await this.userRepository.find({
      where: { id: { inq: uniqueUserIds as string[] } },
    });

    return users;
  }

  @post('/users/air-trainee', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'User',
        content: {
          'application/json': {
            schema: {
              'x-ts-type': User,
            },
          },
        },
      },
    },
  })
  // @authenticate('jwt')
  async findAirTrainee(

    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              locationOneId: { type: 'string' },
              locationTwoId: { type: 'string' },
              locationThreeId: { type: 'string' },
              locationFourId: { type: 'string' },
            },
            required: ['locationOneId', 'locationTwoId', 'locationThreeId', 'locationFourId'],
          },
        },
      },
    })
    payload: {
      locationOneId: string;
      locationTwoId: string;
      locationThreeId: string;
      locationFourId: string;
    }
  ): Promise<User[]> {
    const roleId = "64d5c09ea3e49d1da2e04f64";
    const allLocationId = 'all';

    let whereCondition = {
      roles: { inq: [[roleId]] },
      or: [
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: [payload.locationThreeId] },
          locationFourId: { inq: [payload.locationFourId] },
        },
        {
          locationOneId: { inq: ['tier1-all'] },
          locationTwoId: '',
          locationThreeId: '',
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: ['tier2-all'] },
          locationThreeId: '',
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: ['tier3-all'] },
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: [payload.locationThreeId] },
          locationFourId: { inq: ['tier4-all'] },
        },
      ]
    };

    const userLocationRoles = await this.userLocationRoleRepository.find({
      where: whereCondition,
    });

    const userIds = userLocationRoles.map((userLocationRole) => userLocationRole.userId);
    const uniqueUserIds = Array.from(new Set(userIds));

    const users = await this.userRepository.find({
      where: { id: { inq: uniqueUserIds as string[] } },
    });

    return users;
  }

  @post('/users/air-third-party', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'User',
        content: {
          'application/json': {
            schema: {
              'x-ts-type': User,
            },
          },
        },
      },
    },
  })
  // @authenticate('jwt')
  async findAirThirdParty(

    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              locationOneId: { type: 'string' },
              locationTwoId: { type: 'string' },
              locationThreeId: { type: 'string' },
              locationFourId: { type: 'string' },
            },
            required: ['locationOneId', 'locationTwoId', 'locationThreeId', 'locationFourId'],
          },
        },
      },
    })
    payload: {
      locationOneId: string;
      locationTwoId: string;
      locationThreeId: string;
      locationFourId: string;
    }
  ): Promise<User[]> {
    const roleId = "64d4617aa46f6405f2232656";
    const allLocationId = 'all';

    let whereCondition = {
      roles: { inq: [[roleId]] },
      or: [
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: [payload.locationThreeId] },
          locationFourId: { inq: [payload.locationFourId] },
        },
        {
          locationOneId: { inq: ['tier1-all'] },
          locationTwoId: '',
          locationThreeId: '',
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: ['tier2-all'] },
          locationThreeId: '',
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: ['tier3-all'] },
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: [payload.locationThreeId] },
          locationFourId: { inq: ['tier4-all'] },
        },
      ]
    };

    const userLocationRoles = await this.userLocationRoleRepository.find({
      where: whereCondition,
    });

    const userIds = userLocationRoles.map((userLocationRole) => userLocationRole.userId);
    const uniqueUserIds = Array.from(new Set(userIds));

    const users = await this.userRepository.find({
      where: { id: { inq: uniqueUserIds as string[] } },
    });

    return users;
  }

  @post('/users/air-security', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'User',
        content: {
          'application/json': {
            schema: {
              'x-ts-type': User,
            },
          },
        },
      },
    },
  })
  // @authenticate('jwt')
  async findAirSecurity(

    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              locationOneId: { type: 'string' },
              locationTwoId: { type: 'string' },
              locationThreeId: { type: 'string' },
              locationFourId: { type: 'string' },
            },
            required: ['locationOneId', 'locationTwoId', 'locationThreeId', 'locationFourId'],
          },
        },
      },
    })
    payload: {
      locationOneId: string;
      locationTwoId: string;
      locationThreeId: string;
      locationFourId: string;
    }
  ): Promise<User[]> {
    const roleId = "64d5c0aca3e49d1da2e04f65";
    const allLocationId = 'all';

    let whereCondition = {
      roles: { inq: [[roleId]] },
      or: [
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: [payload.locationThreeId] },
          locationFourId: { inq: [payload.locationFourId] },
        },
        {
          locationOneId: { inq: ['tier1-all'] },
          locationTwoId: '',
          locationThreeId: '',
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: ['tier2-all'] },
          locationThreeId: '',
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: ['tier3-all'] },
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: [payload.locationThreeId] },
          locationFourId: { inq: ['tier4-all'] },
        },
      ]
    };

    const userLocationRoles = await this.userLocationRoleRepository.find({
      where: whereCondition,
    });

    const userIds = userLocationRoles.map((userLocationRole) => userLocationRole.userId);
    const uniqueUserIds = Array.from(new Set(userIds));

    const users = await this.userRepository.find({
      where: { id: { inq: uniqueUserIds as string[] } },
    });

    return users;
  }


  @post('/users/air-cost-reviewer', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'User',
        content: {
          'application/json': {
            schema: {
              'x-ts-type': User,
            },
          },
        },
      },
    },
  })
  // @authenticate('jwt')
  async findAirCostReviewer(

    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              locationOneId: { type: 'string' },
              locationTwoId: { type: 'string' },
              locationThreeId: { type: 'string' },
              locationFourId: { type: 'string' },
            },
            required: ['locationOneId', 'locationTwoId', 'locationThreeId', 'locationFourId'],
          },
        },
      },
    })
    payload: {
      locationOneId: string;
      locationTwoId: string;
      locationThreeId: string;
      locationFourId: string;
    }
  ): Promise<User[]> {
    const roleId = "64d5c0bda3e49d1da2e04f66";
    const allLocationId = 'all';

    let whereCondition = {
      roles: { inq: [[roleId]] },
      or: [
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: [payload.locationThreeId] },
          locationFourId: { inq: [payload.locationFourId] },
        },
        {
          locationOneId: { inq: ['tier1-all'] },
          locationTwoId: '',
          locationThreeId: '',
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: ['tier2-all'] },
          locationThreeId: '',
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: ['tier3-all'] },
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: [payload.locationThreeId] },
          locationFourId: { inq: ['tier4-all'] },
        },
      ]
    };

    const userLocationRoles = await this.userLocationRoleRepository.find({
      where: whereCondition,
    });

    const userIds = userLocationRoles.map((userLocationRole) => userLocationRole.userId);
    const uniqueUserIds = Array.from(new Set(userIds));

    const users = await this.userRepository.find({
      where: { id: { inq: uniqueUserIds as string[] } },
    });

    return users;
  }

  @post('/users/air-finance', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'User',
        content: {
          'application/json': {
            schema: {
              'x-ts-type': User,
            },
          },
        },
      },
    },
  })
  // @authenticate('jwt')
  async findAirFinance(

    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              locationOneId: { type: 'string' },
              locationTwoId: { type: 'string' },
              locationThreeId: { type: 'string' },
              locationFourId: { type: 'string' },
            },
            required: ['locationOneId', 'locationTwoId', 'locationThreeId', 'locationFourId'],
          },
        },
      },
    })
    payload: {
      locationOneId: string;
      locationTwoId: string;
      locationThreeId: string;
      locationFourId: string;
    }
  ): Promise<User[]> {
    const roleId = "64d5c0c0a3e49d1da2e04f67";
    const allLocationId = 'all';

    let whereCondition = {
      roles: { inq: [[roleId]] },
      or: [
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: [payload.locationThreeId] },
          locationFourId: { inq: [payload.locationFourId] },
        },
        {
          locationOneId: { inq: ['tier1-all'] },
          locationTwoId: '',
          locationThreeId: '',
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: ['tier2-all'] },
          locationThreeId: '',
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: ['tier3-all'] },
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: [payload.locationThreeId] },
          locationFourId: { inq: ['tier4-all'] },
        },
      ]
    };

    const userLocationRoles = await this.userLocationRoleRepository.find({
      where: whereCondition,
    });

    const userIds = userLocationRoles.map((userLocationRole) => userLocationRole.userId);
    const uniqueUserIds = Array.from(new Set(userIds));

    const users = await this.userRepository.find({
      where: { id: { inq: uniqueUserIds as string[] } },
    });

    return users;
  }

  @post('/users/air-duty-engineer-manager', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'User',
        content: {
          'application/json': {
            schema: {
              'x-ts-type': User,
            },
          },
        },
      },
    },
  })
  // @authenticate('jwt')
  async findAirDutyEngineerManager(

    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              locationOneId: { type: 'string' },
              locationTwoId: { type: 'string' },
              locationThreeId: { type: 'string' },
              locationFourId: { type: 'string' },
            },
            required: ['locationOneId', 'locationTwoId', 'locationThreeId', 'locationFourId'],
          },
        },
      },
    })
    payload: {
      locationOneId: string;
      locationTwoId: string;
      locationThreeId: string;
      locationFourId: string;
    }
  ): Promise<User[]> {
    const roleId = "64d5e4b4a3e49d1da2e04f70";
    const allLocationId = 'all';

    let whereCondition = {
      roles: { inq: [[roleId]] },
      or: [
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: [payload.locationThreeId] },
          locationFourId: { inq: [payload.locationFourId] },
        },
        {
          locationOneId: { inq: ['tier1-all'] },
          locationTwoId: '',
          locationThreeId: '',
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: ['tier2-all'] },
          locationThreeId: '',
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: ['tier3-all'] },
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: [payload.locationThreeId] },
          locationFourId: { inq: ['tier4-all'] },
        },
      ]
    };

    const userLocationRoles = await this.userLocationRoleRepository.find({
      where: whereCondition,
    });

    const userIds = userLocationRoles.map((userLocationRole) => userLocationRole.userId);
    const uniqueUserIds = Array.from(new Set(userIds));

    const users = await this.userRepository.find({
      where: { id: { inq: uniqueUserIds as string[] } },
    });

    return users;
  }


  @post('/users/air-gm-ops', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'User',
        content: {
          'application/json': {
            schema: {
              'x-ts-type': User,
            },
          },
        },
      },
    },
  })
  // @authenticate('jwt')
  async findGmOps(

    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              locationOneId: { type: 'string' },
              locationTwoId: { type: 'string' },
              locationThreeId: { type: 'string' },
              locationFourId: { type: 'string' },
            },
            required: ['locationOneId', 'locationTwoId', 'locationThreeId', 'locationFourId'],
          },
        },
      },
    })
    payload: {
      locationOneId: string;
      locationTwoId: string;
      locationThreeId: string;
      locationFourId: string;
    }
  ): Promise<User[]> {
    const roleId = "64d4a2aea3e49d1da2e04f51";
    const allLocationId = 'all';

    let whereCondition = {
      roles: { inq: [[roleId]] },
      or: [
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: [payload.locationThreeId] },
          locationFourId: { inq: [payload.locationFourId] },
        },
        {
          locationOneId: { inq: ['tier1-all'] },
          locationTwoId: '',
          locationThreeId: '',
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: ['tier2-all'] },
          locationThreeId: '',
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: ['tier3-all'] },
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: [payload.locationThreeId] },
          locationFourId: { inq: ['tier4-all'] },
        },
      ]
    };

    const userLocationRoles = await this.userLocationRoleRepository.find({
      where: whereCondition,
    });

    const userIds = userLocationRoles.map((userLocationRole) => userLocationRole.userId);
    const uniqueUserIds = Array.from(new Set(userIds));

    const users = await this.userRepository.find({
      where: { id: { inq: uniqueUserIds as string[] } },
    });

    return users;
  }


  @post('/users/air-medical-officer', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'User',
        content: {
          'application/json': {
            schema: {
              'x-ts-type': User,
            },
          },
        },
      },
    },
  })
  // @authenticate('jwt')
  async findMedicalOfficer(

    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              locationOneId: { type: 'string' },
              locationTwoId: { type: 'string' },
              locationThreeId: { type: 'string' },
              locationFourId: { type: 'string' },
            },
            required: ['locationOneId', 'locationTwoId', 'locationThreeId', 'locationFourId'],
          },
        },
      },
    })
    payload: {
      locationOneId: string;
      locationTwoId: string;
      locationThreeId: string;
      locationFourId: string;
    }
  ): Promise<User[]> {
    const roleId = "64f0375eb7adc567b4925e92";
    const allLocationId = 'all';

    let whereCondition = {
      roles: { inq: [[roleId]] },
      or: [
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: [payload.locationThreeId] },
          locationFourId: { inq: [payload.locationFourId] },
        },
        {
          locationOneId: { inq: ['tier1-all'] },
          locationTwoId: '',
          locationThreeId: '',
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: ['tier2-all'] },
          locationThreeId: '',
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: ['tier3-all'] },
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: [payload.locationThreeId] },
          locationFourId: { inq: ['tier4-all'] },
        },
      ]
    };

    const userLocationRoles = await this.userLocationRoleRepository.find({
      where: whereCondition,
    });

    const userIds = userLocationRoles.map((userLocationRole) => userLocationRole.userId);
    const uniqueUserIds = Array.from(new Set(userIds));

    const users = await this.userRepository.find({
      where: { id: { inq: uniqueUserIds as string[] } },
    });

    return users;
  }

  @post('/users/air-engineer', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'User',
        content: {
          'application/json': {
            schema: {
              'x-ts-type': User,
            },
          },
        },
      },
    },
  })
  // @authenticate('jwt')
  async findEngineer(

    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              locationOneId: { type: 'string' },
              locationTwoId: { type: 'string' },
              locationThreeId: { type: 'string' },
              locationFourId: { type: 'string' },
            },
            required: ['locationOneId', 'locationTwoId', 'locationThreeId', 'locationFourId'],
          },
        },
      },
    })
    payload: {
      locationOneId: string;
      locationTwoId: string;
      locationThreeId: string;
      locationFourId: string;
    }
  ): Promise<User[]> {
    const roleId = "64f7c768fd6464b3d71f11dc";
    const allLocationId = 'all';

    let whereCondition = {
      roles: { inq: [[roleId]] },
      or: [
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: [payload.locationThreeId] },
          locationFourId: { inq: [payload.locationFourId] },
        },
        {
          locationOneId: { inq: ['tier1-all'] },
          locationTwoId: '',
          locationThreeId: '',
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: ['tier2-all'] },
          locationThreeId: '',
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: ['tier3-all'] },
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: [payload.locationThreeId] },
          locationFourId: { inq: ['tier4-all'] },
        },
      ]
    };

    const userLocationRoles = await this.userLocationRoleRepository.find({
      where: whereCondition,
    });

    const userIds = userLocationRoles.map((userLocationRole) => userLocationRole.userId);
    const uniqueUserIds = Array.from(new Set(userIds));

    const users = await this.userRepository.find({
      where: { id: { inq: uniqueUserIds as string[] } },
    });

    return users;
  }

  @post('/eptw-construction-assessor', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'User',
        content: {
          'application/json': {
            schema: {
              'x-ts-type': User,
            },
          },
        },
      },
    },
  })
  // @authenticate('jwt')
  async findConstructionAssessor(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              locationOneId: { type: 'string' },
              locationTwoId: { type: 'string' },
              locationThreeId: { type: 'string' },
              locationFourId: { type: 'string' },
              isWah: { type: 'boolean' },
              isConfinedSpace: { type: 'boolean' },
              isLifting: { type: 'boolean' },
              isHighRisk: { type: 'boolean' }
            },
            required: ['locationOneId', 'locationTwoId', 'locationThreeId', 'locationFourId'],
          },
        },
      },
    })
    payload: {
      locationOneId: string;
      locationTwoId: string;
      locationThreeId: string;
      locationFourId: string;
      isWah: boolean;
      isConfinedSpace: boolean;
      isLifting: boolean;

    }
  ): Promise<User[]> {


    const wahRoleId = "644b27e641cb57116200a88e";
    const confinedSpaceRoleId = "644b287241cb57116200a88f";
    const liftingRoleId = "64741c0447efd939764b3f23";
    const allLocationId = 'all';

    let roleIds: string[] = ["64741c2347efd939764b3f24"];
    if (payload.isWah) {
      roleIds.push(wahRoleId);
    }
    if (payload.isConfinedSpace) {
      roleIds.push(confinedSpaceRoleId);
    }
    if (payload.isLifting) {
      roleIds.push(liftingRoleId);
    }

    console.log(roleIds);
    const andConditions = roleIds.map(roleId => ({ roles: { inq: [roleId] } }));
    let whereCondition = {
      and: andConditions,
      or: [
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: [payload.locationThreeId] },
          locationFourId: { inq: [payload.locationFourId] },
        },
        {
          locationOneId: { inq: ['tier1-all'] },
          locationTwoId: '',
          locationThreeId: '',
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: ['tier2-all'] },
          locationThreeId: '',
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: ['tier3-all'] },
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: [payload.locationThreeId] },
          locationFourId: { inq: ['tier4-all'] },
        },
      ]
    };

    const userLocationRoles = await this.userLocationRoleRepository.find({
      where: whereCondition,
    });

    console.log(userLocationRoles)


    const userIds = userLocationRoles.map((userLocationRole) => userLocationRole.userId);
    const uniqueUserIds = Array.from(new Set(userIds));

    const users = await this.userRepository.find({
      where: { id: { inq: uniqueUserIds as string[] } },
    });

    return users;
  }
  @get('/users-all', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'User',
        content: {
          'application/json': {
            schema: {
              'x-ts-type': User,
            },
          },
        },
      },
    },
  })
  @authenticate('jwt')
  async findUserAll(): Promise<User[]> {
    return this.userRepository.find({ include: [{ relation: 'userLocation' }, { relation: 'department' }, { relation: 'ghsOne' }, { relation: 'designation' }, { relation: 'workingGroup' }] });
  }

  @get('/users/me', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'The current user profile',
        content: {
          'application/json': {
            schema: UserProfileSchema,
          },
        },
      },
    },
  })
  @authenticate('jwt')
  async getCurrentUser(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
  ): Promise<any> {
    // (@jannyHou)FIXME: explore a way to generate OpenAPI schema for symbol property

    const email = '<EMAIL>'

    const user = await this.userRepository.findOne({ where: { email }, include: [{ relation: 'userLocation' }] });
    
    if (!user) {
      throw new HttpErrors.Unauthorized('UnAuthorized');
    }

    const userRoles = await this.userLocationRoleRepository.find({ where: { userId: user.id } });
    const uniqueRolesArray = Array.from(new Set(userRoles.map(role => role.roles).flat()));

    const airRoles = await this.incidentRoleRepository.find({
      where: { id: { inq: uniqueRolesArray } },
      fields: {
        id: true,
        name: true,
      }
    });

    const riskRoles = await this.plantRoleRepository.find({
      where: { id: { inq: uniqueRolesArray } },
      fields: {
        id: true,
        name: true,
      }
    });

    const documentRoles = await this.documentRoleRepository.find({
      where: { id: { inq: uniqueRolesArray } },
      fields: {
        id: true,
        name: true,
      }
    });

    const combinedRoles = [...airRoles, ...riskRoles, ...documentRoles];

    return { id: user.id, email: user.email, firstName: user.firstName, roles: combinedRoles };
  }

  @post('/users/me', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'The current user profile',
        content: {
          'application/json': {
            schema: UserProfileSchema,
          },
        },
      },
    },

  })
  @authenticate('jwt')
  async printCurrentUser(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(DeviceTokenRequest, {
            title: 'NewUser',
          }),
        },
      },
    })
    deviceTokenRequest: DeviceTokenRequest,
  ): Promise<any> {

    const email = '<EMAIL>'
    const user = await this.userRepository.findOne({ where: { email: email }, include: [{ relation: 'userLocation' }] });
    if (user) {

      if (!user.deviceToken || user.deviceToken !== deviceTokenRequest.deviceToken) {

        // Replace with your platform application ARN
        const sns = new SNS({
          region: process.env.AWS_REGION,
          accessKeyId: process.env.AWS_ACCESS_KEY_ID,
          secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
        });
        try {
          const createEndpointResponse = await sns
            .createPlatformEndpoint({
              PlatformApplicationArn: `${process.env.AWS_SNS_TOPIC_ARN}`,
              Token: deviceTokenRequest.deviceToken
            })
            .promise();

          const endpointArn = createEndpointResponse.EndpointArn;

          await this.userRepository.updateById(user?.id, { deviceToken: deviceTokenRequest.deviceToken, arn: endpointArn })
        }
        catch (error) {
          console.log(error)
          throw new Error('Failed to create endpoint');

        }

      }

      const userRoles = await this.userLocationRoleRepository.find({ where: { userId: user.id } });
      const uniqueRolesArray = Array.from(new Set(userRoles.map(role => role.roles).flat()));

      const airRoles = await this.incidentRoleRepository.find({
        where: { id: { inq: uniqueRolesArray } },
        fields: {
          id: true,
          name: true,
        }
      });

      const riskRoles = await this.plantRoleRepository.find({
        where: { id: { inq: uniqueRolesArray } },
        fields: {
          id: true,
          name: true,
        }
      });

      const combinedRoles = [...airRoles, ...riskRoles];

      return { id: user.id, email: user.email, firstName: user.firstName, roles: combinedRoles };

    } else {
      throw new HttpErrors.Unauthorized('Unauthorized');
    }

  }


  @post('/users/login', {
    responses: {
      '200': {
        description: 'Token',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                token: {
                  type: 'string',
                },
              },
            },
          },
        },
      },
    },
  })
  async login(
    @requestBody(CredentialsRequestBody) credentials: Credentials,
  ): Promise<{ token: string }> {
    // ensure the user exists, and the password is correct
    const user = await this.userService.verifyCredentials(credentials);

    // convert a User object into a UserProfile object (reduced set of properties)
    const userProfile = this.userService.convertToUserProfile(user);

    // create a JSON Web Token based on the user profile
    const token = await this.jwtService.generateToken(userProfile);

    return { token };
  }

  @put('/users/forgot-password', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'The updated user profile',
        content: {
          'application/json': {
            schema: UserProfileSchema,
          },
        },
      },
    },
  })
  @authenticate('jwt')
  async forgotPassword(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @requestBody(PasswordResetRequestBody) credentials: Credentials,
  ): Promise<{ token: string }> {
    const { email, password } = credentials;
    const { id } = currentUserProfile;

    const user = await this.userRepository.findById(id);

    if (!user) {
      throw new HttpErrors.NotFound('User account not found');
    }

    if (email !== user?.email) {
      throw new HttpErrors.Forbidden('Invalid email address');
    }

    validateCredentials(_.pick(credentials, ['email', 'password']));

    const passwordHash = await this.passwordHasher.hashPassword(password);

    await this.userRepository
      .userCredentials(user.id)
      .patch({ password: passwordHash });

    const userProfile = this.userService.convertToUserProfile(user);

    const token = await this.jwtService.generateToken(userProfile);

    return { token };
  }

  @post('/users/reset-password/init', {
    responses: {
      '200': {
        description: 'Confirmation that reset password email has been sent',
      },
    },
  })
  async resetPasswordInit(
    @requestBody() resetPasswordInit: ResetPasswordInit,
  ): Promise<string> {
    if (!isemail.validate(resetPasswordInit.email)) {
      throw new HttpErrors.UnprocessableEntity('Invalid email address');
    }

    const sentMessageInfo: SentMessageInfo =
      await this.userManagementService.requestPasswordReset(
        resetPasswordInit.email,
      );

    if (sentMessageInfo.accepted.length) {
      return 'Successfully sent reset password link';
    }
    throw new HttpErrors.InternalServerError(
      'Error sending reset password email',
    );
  }

  @put('/users/reset-password/finish', {
    responses: {
      '200': {
        description: 'A successful password reset response',
      },
    },
  })
  async resetPasswordFinish(
    @requestBody() keyAndPassword: KeyAndPassword,
  ): Promise<string> {
    validateKeyPassword(keyAndPassword);

    const foundUser = await this.userRepository.findOne({
      where: { resetKey: keyAndPassword.resetKey },
    });

    if (!foundUser) {
      throw new HttpErrors.NotFound(
        'No associated account for the provided reset key',
      );
    }

    const user = await this.userManagementService.validateResetKeyLifeSpan(
      foundUser,
    );

    const passwordHash = await this.passwordHasher.hashPassword(
      keyAndPassword.password,
    );

    try {
      await this.userRepository
        .userCredentials(user.id)
        .patch({ password: passwordHash });

      await this.userRepository.updateById(user.id, user);
    } catch (e) {
      return e;
    }

    return 'Password reset successful';
  }

  @post('/users/get_users', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'User',
        content: {
          'application/json': {
            schema: {
              'x-ts-type': User,
            },
          },
        },
      },
    },
  })
  // @authenticate('jwt')
  async findUsers(

    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              locationOneId: { type: 'string' },
              locationTwoId: { type: 'string' },
              locationThreeId: { type: 'string' },
              locationFourId: { type: 'string' },
              mode: { type: 'string' }
            },
            required: ['locationOneId', 'locationTwoId', 'locationThreeId', 'locationFourId', 'mode'],
          },
        },
      },
    })
    payload: {
      locationOneId: string;
      locationTwoId: string;
      locationThreeId: string;
      locationFourId: string;
      mode: string;
    }
  ): Promise<User[]> {

    let roleId = '';
    switch (payload.mode) {
      case 'document-initiator':
        roleId = "658402cb46de3082904f7564";
        break;
      case 'document-creator':
        roleId = "658402d646de3082904f7565";
        break;
      case 'document-reviewer':
        roleId = "658402df46de3082904f7566";
        break;
      case 'document-approver':
        roleId = "658402e446de3082904f7567";
        break;
      case 'ir-action-reviewer':
        roleId = "658548be46de3082904f7568";
        break;
      case 'ir-action-approver':
        roleId = "658548c346de3082904f7569";
        break;
      case 'eptw-applicant':
        roleId = "650410979d06d6a9f3c51eb9";
        break;
      case 'eptw-assessor':
        roleId = "6504109e9d06d6a9f3c51eba";
        break;
      case 'eptw-approver':
        roleId = "650410a49d06d6a9f3c51ebb";
        break;
      case 'eptw-low-risk-approver':
        roleId = "6603b69ad6bc0f0356cb9b37";
        break;
      case 'eptw-medium-risk-approver-stage-1':
        roleId = "6603b6add6bc0f0356cb9b38";
        break;
      case 'eptw-medium-risk-approver-stage-2':
        roleId = "6603b6bfd6bc0f0356cb9b39";
        break;
      case 'eptw-high-risk-approver-stage-1':
        roleId = "6603b6d1d6bc0f0356cb9b3a";
        break;
      case 'eptw-high-risk-approver-stage-2':
        roleId = "6603b6dbd6bc0f0356cb9b3b";
        break;
      case 'eptw-high-risk-approver-stage-3':
        roleId = "6603b6e4d6bc0f0356cb9b3c";
        break;
      case 'eptw-high-risk-approver-stage-4':
        roleId = "6603b6ecd6bc0f0356cb9b3d";
        break;
      case 'eptw-high-risk-approver-stage-5':
        roleId = "6603b6f6d6bc0f0356cb9b3e";
        break;
      case 'ir-action-implementor':
        roleId = "658548be46de3082904f7568";
        break;
      case 'ir-action-verifier':
        roleId = "658548c346de3082904f7569";
        break;
      case 'ir-cost-estimator':
        roleId = "64d495a7a3e49d1da2e04f4c";
        break;
      case 'ir-supervisor':
        roleId = "65d4426a0d90cf0383f18bc9";
        break;
      case 'ir-hod':
        roleId = "64f5a456fd6464b3d71f1165";
        break;
      case 'ir-skill-trainer':
        roleId = "64d1b29d20d96f3e26c30bc5";
        break;
      case 'ir-hr':
        roleId = "65fc0a69d6bc0f0356cb9b30";
        break;
      // case 'ir-hr':
      //   roleId = "660245b8f11f6af259f9375d";
      //   break;
      case 'ir-investigator':
        roleId = '64d1b26a20d96f3e26c30bbf';
        break;

      case 'ra-member':
        roleId = "64d1d05320d96f3e26c30c13";
        break;
      case 'good-catch-general-user':
        roleId = "67602c66549ac0ec8b0cd856";
        break;
      case 'good-catch-program-administrator':
        roleId = "67602ca8549ac0ec8b0cd857";
        break;
      case 'good-catch-action-owner':
        roleId = "67602d25549ac0ec8b0cd858";
        break;



      default:

        throw new Error('Type is missing')
    }

    const allLocationId = 'all';

    let whereCondition = {
      roles: { inq: [[roleId]] },
      or: [
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: [payload.locationThreeId] },
          locationFourId: { inq: [payload.locationFourId] },
        },
        {
          locationOneId: { inq: ['tier1-all'] },
          locationTwoId: '',
          locationThreeId: '',
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: ['tier2-all'] },
          locationThreeId: '',
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: ['tier3-all'] },
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: [payload.locationThreeId] },
          locationFourId: { inq: ['tier4-all'] },
        },
      ]
    };

    const userLocationRoles = await this.userLocationRoleRepository.find({
      where: whereCondition,
    });

    const userIds = userLocationRoles.map((userLocationRole) => userLocationRole.userId);
    const uniqueUserIds = Array.from(new Set(userIds));

    const users = await this.userRepository.find({
      where: { id: { inq: uniqueUserIds as string[] } },
    });

    return users;
  }
  @post('/users/incident-reviewer', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'User',
        content: {
          'application/json': {
            schema: {
              'x-ts-type': User,
            },
          },
        },
      },
    },
  })
  // @authenticate('jwt')
  async findIncidentReviewer(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              locationOneId: { type: 'string' },
              locationTwoId: { type: 'string' },
              locationThreeId: { type: 'string' },
              locationFourId: { type: 'string' },
            },
            required: ['locationOneId', 'locationTwoId', 'locationThreeId', 'locationFourId'],
          },
        },
      },
    })
    payload: {
      locationOneId: string;
      locationTwoId: string;
      locationThreeId: string;
      locationFourId: string;
    }
  ): Promise<User[]> {
    const roleId = "64a526ea9e7163b6c2e5ab3f";
    const allLocationId = 'all';

    let whereCondition = {
      roles: { inq: [[roleId]] },
      or: [
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: [payload.locationThreeId] },
          locationFourId: { inq: [payload.locationFourId] },
        },
        {
          locationOneId: { inq: ['tier1-all'] },
          locationTwoId: '',
          locationThreeId: '',
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: ['tier2-all'] },
          locationThreeId: '',
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: ['tier3-all'] },
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: [payload.locationThreeId] },
          locationFourId: { inq: ['tier4-all'] },
        },
      ]
    };

    const userLocationRoles = await this.userLocationRoleRepository.find({
      where: whereCondition,
    });

    const userIds = userLocationRoles.map((userLocationRole) => userLocationRole.userId);
    const uniqueUserIds = Array.from(new Set(userIds));

    const users = await this.userRepository.find({
      where: { id: { inq: uniqueUserIds as string[] } },
    });

    return users;
  }

}
