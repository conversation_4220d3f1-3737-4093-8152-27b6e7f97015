import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {DocumentCategory} from '../models';
import {DocumentCategoryRepository} from '../repositories';

export class DocumentCategoryController {
  constructor(
    @repository(DocumentCategoryRepository)
    public documentCategoryRepository : DocumentCategoryRepository,
  ) {}

  @post('/document-categories')
  @response(200, {
    description: 'DocumentCategory model instance',
    content: {'application/json': {schema: getModelSchemaRef(DocumentCategory)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(DocumentCategory, {
            title: 'NewDocumentCategory',
            exclude: ['id'],
          }),
        },
      },
    })
    documentCategory: Omit<DocumentCategory, 'id'>,
  ): Promise<DocumentCategory> {
    return this.documentCategoryRepository.create(documentCategory);
  }

  @get('/document-categories/count')
  @response(200, {
    description: 'DocumentCategory model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(DocumentCategory) where?: Where<DocumentCategory>,
  ): Promise<Count> {
    return this.documentCategoryRepository.count(where);
  }

  @get('/document-categories')
  @response(200, {
    description: 'Array of DocumentCategory model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(DocumentCategory, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(DocumentCategory) filter?: Filter<DocumentCategory>,
  ): Promise<DocumentCategory[]> {
    return this.documentCategoryRepository.find(filter);
  }

  @patch('/document-categories')
  @response(200, {
    description: 'DocumentCategory PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(DocumentCategory, {partial: true}),
        },
      },
    })
    documentCategory: DocumentCategory,
    @param.where(DocumentCategory) where?: Where<DocumentCategory>,
  ): Promise<Count> {
    return this.documentCategoryRepository.updateAll(documentCategory, where);
  }

  @get('/document-categories/{id}')
  @response(200, {
    description: 'DocumentCategory model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(DocumentCategory, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(DocumentCategory, {exclude: 'where'}) filter?: FilterExcludingWhere<DocumentCategory>
  ): Promise<DocumentCategory> {
    return this.documentCategoryRepository.findById(id, filter);
  }

  @patch('/document-categories/{id}')
  @response(204, {
    description: 'DocumentCategory PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(DocumentCategory, {partial: true}),
        },
      },
    })
    documentCategory: DocumentCategory,
  ): Promise<void> {
    await this.documentCategoryRepository.updateById(id, documentCategory);
  }

  @put('/document-categories/{id}')
  @response(204, {
    description: 'DocumentCategory PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() documentCategory: DocumentCategory,
  ): Promise<void> {
    await this.documentCategoryRepository.replaceById(id, documentCategory);
  }

  @del('/document-categories/{id}')
  @response(204, {
    description: 'DocumentCategory DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.documentCategoryRepository.deleteById(id);
  }
}
