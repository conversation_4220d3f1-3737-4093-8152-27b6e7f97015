import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {TitleConfig} from '../models';
import {TitleConfigRepository} from '../repositories';

export class TitleConfigController {
  constructor(
    @repository(TitleConfigRepository)
    public titleConfigRepository : TitleConfigRepository,
  ) {}

  @post('/title-configs')
  @response(200, {
    description: 'TitleConfig model instance',
    content: {'application/json': {schema: getModelSchemaRef(TitleConfig)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(TitleConfig, {
            title: 'NewTitleConfig',
            exclude: ['id'],
          }),
        },
      },
    })
    titleConfig: Omit<TitleConfig, 'id'>,
  ): Promise<TitleConfig> {
    return this.titleConfigRepository.create(titleConfig);
  }

  @get('/title-configs/count')
  @response(200, {
    description: 'TitleConfig model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(TitleConfig) where?: Where<TitleConfig>,
  ): Promise<Count> {
    return this.titleConfigRepository.count(where);
  }

  @get('/title-configs')
  @response(200, {
    description: 'Array of TitleConfig model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(TitleConfig, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(TitleConfig) filter?: Filter<TitleConfig>,
  ): Promise<TitleConfig[]> {
    return this.titleConfigRepository.find(filter);
  }

  @patch('/title-configs')
  @response(200, {
    description: 'TitleConfig PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(TitleConfig, {partial: true}),
        },
      },
    })
    titleConfig: TitleConfig,
    @param.where(TitleConfig) where?: Where<TitleConfig>,
  ): Promise<Count> {
    return this.titleConfigRepository.updateAll(titleConfig, where);
  }

  @get('/title-configs/{id}')
  @response(200, {
    description: 'TitleConfig model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(TitleConfig, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(TitleConfig, {exclude: 'where'}) filter?: FilterExcludingWhere<TitleConfig>
  ): Promise<TitleConfig> {
    return this.titleConfigRepository.findById(id, filter);
  }

  @patch('/title-configs/{id}')
  @response(204, {
    description: 'TitleConfig PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(TitleConfig, {partial: true}),
        },
      },
    })
    titleConfig: TitleConfig,
  ): Promise<void> {
    await this.titleConfigRepository.updateById(id, titleConfig);
  }

  @put('/title-configs/{id}')
  @response(204, {
    description: 'TitleConfig PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() titleConfig: TitleConfig,
  ): Promise<void> {
    await this.titleConfigRepository.replaceById(id, titleConfig);
  }

  @del('/title-configs/{id}')
  @response(204, {
    description: 'TitleConfig DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.titleConfigRepository.deleteById(id);
  }
}
