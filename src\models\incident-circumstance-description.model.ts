import {Entity, model, property} from '@loopback/repository';

@model()
export class IncidentCircumstanceDescription extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'string',
  })
  description?: string;

  @property({
    type: 'string',
  })
  incidentCircumstanceTypeId?: string;

  constructor(data?: Partial<IncidentCircumstanceDescription>) {
    super(data);
  }
}

export interface IncidentCircumstanceDescriptionRelations {
  // describe navigational properties here
}

export type IncidentCircumstanceDescriptionWithRelations = IncidentCircumstanceDescription & IncidentCircumstanceDescriptionRelations;
