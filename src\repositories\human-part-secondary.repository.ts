import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasManyRepositoryFactory} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {HumanPartSecondary, HumanPartSecondaryRelations, HumanPartTertiary} from '../models';
import {HumanPartTertiaryRepository} from './human-part-tertiary.repository';

export class HumanPartSecondaryRepository extends DefaultCrudRepository<
  HumanPartSecondary,
  typeof HumanPartSecondary.prototype.id,
  HumanPartSecondaryRelations
> {

  public readonly humanPartTertiaries: HasManyRepositoryFactory<HumanPartTertiary, typeof HumanPartSecondary.prototype.id>;

  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource, @repository.getter('HumanPartTertiaryRepository') protected humanPartTertiaryRepositoryGetter: Getter<HumanPartTertiaryRepository>,
  ) {
    super(HumanPartSecondary, dataSource);
    this.humanPartTertiaries = this.createHasManyRepositoryFactoryFor('humanPartTertiaries', humanPartTertiaryRepositoryGetter,);
    this.registerInclusionResolver('humanPartTertiaries', this.humanPartTertiaries.inclusionResolver);
  }
}
