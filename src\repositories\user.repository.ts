// Copyright IBM Corp. 2019,2020. All Rights Reserved.
// Node module: loopback4-example-shopping
// This file is licensed under the MIT License.
// License text available at https://opensource.org/licenses/MIT

import {Getter, inject} from '@loopback/core';
import {
  DefaultCrudRepository,
  HasManyRepositoryFactory,
  HasOneRepositoryFactory,
  juggler,
  repository, BelongsToAccessor, HasManyThroughRepositoryFactory} from '@loopback/repository';
import { User, UserCredentials, EhsRole, Action, UserLocation, UserLocationRole, ReportIncident, TowerCrane, PermitReport, Department, Unit, UserUnitAllocation, Groups, GroupUser, Checklist, UserChecklistAllocation, Documents, UserDocumentAllocation, Designation, WorkingGroup, GhsOne} from '../models';

import {UserCredentialsRepository} from './user-credentials.repository';
import {EhsRoleRepository} from './ehs-role.repository';
import {UserLocationRepository} from './user-location.repository';
import {UserLocationRoleRepository} from './user-location-role.repository';
import {ReportIncidentRepository} from './report-incident.repository';
import {TowerCraneRepository} from './tower-crane.repository';
import {PermitReportRepository} from './permit-report.repository';
import {DepartmentRepository} from './department.repository';
import {UserUnitAllocationRepository} from './user-unit-allocation.repository';
import {UnitRepository} from './unit.repository';
import {GroupUserRepository} from './group-user.repository';
import {GroupsRepository} from './groups.repository';
import {UserChecklistAllocationRepository} from './user-checklist-allocation.repository';
import {ChecklistRepository} from './checklist.repository';
import {UserDocumentAllocationRepository} from './user-document-allocation.repository';
import {DocumentsRepository} from './documents.repository';
import {DesignationRepository} from './designation.repository';
import {WorkingGroupRepository} from './working-group.repository';
import {GhsOneRepository} from './ghs-one.repository';

// import {ActionRepository} from './action.repository';

export type Credentials = {
  email: string;
  password: string;
};

export class UserRepository extends DefaultCrudRepository<
  User,
  typeof User.prototype.id
> {
 

  public readonly userCredentials: HasOneRepositoryFactory<
    UserCredentials,
    typeof User.prototype.id
  >;

  public readonly ehsRole: BelongsToAccessor<EhsRole, typeof User.prototype.id>;

  public readonly actions: HasManyRepositoryFactory<Action, typeof User.prototype.id>;

  public readonly UserAssigned: HasManyRepositoryFactory<Action, typeof User.prototype.id>;

  public readonly userLocation: HasOneRepositoryFactory<UserLocation, typeof User.prototype.id>;

  public readonly userLocationRoles: HasManyRepositoryFactory<UserLocationRole, typeof User.prototype.id>;

  public readonly reportIncidents: HasManyRepositoryFactory<ReportIncident, typeof User.prototype.id>;

  public readonly towerCranes: HasManyRepositoryFactory<TowerCrane, typeof User.prototype.id>;

  public readonly applicantReports: HasManyRepositoryFactory<PermitReport, typeof User.prototype.id>;

  public readonly assessorReports: HasManyRepositoryFactory<PermitReport, typeof User.prototype.id>;

  public readonly approverReports: HasManyRepositoryFactory<PermitReport, typeof User.prototype.id>;

  public readonly dscoApproverReports: HasManyRepositoryFactory<PermitReport, typeof User.prototype.id>;

  public readonly department: BelongsToAccessor<Department, typeof User.prototype.id>;

  public readonly units: HasManyThroughRepositoryFactory<Unit, typeof Unit.prototype.id,
          UserUnitAllocation,
          typeof User.prototype.id
        >;

  public readonly groups: HasManyThroughRepositoryFactory<Groups, typeof Groups.prototype.id,
          GroupUser,
          typeof User.prototype.id
        >;

  public readonly checklists: HasManyThroughRepositoryFactory<Checklist, typeof Checklist.prototype.id,
          UserChecklistAllocation,
          typeof User.prototype.id
        >;

  public readonly documents: HasManyThroughRepositoryFactory<Documents, typeof Documents.prototype.id,
          UserDocumentAllocation,
          typeof User.prototype.id
        >;

  public readonly designation: BelongsToAccessor<Designation, typeof User.prototype.id>;

  public readonly workingGroup: BelongsToAccessor<WorkingGroup, typeof User.prototype.id>;

  public readonly ghsOne: BelongsToAccessor<GhsOne, typeof User.prototype.id>;

  constructor(
    @inject('datasources.mongo') dataSource: juggler.DataSource,
    @repository.getter('UserCredentialsRepository')
    protected userCredentialsRepositoryGetter: Getter<UserCredentialsRepository>, @repository.getter('EhsRoleRepository') protected ehsRoleRepositoryGetter: Getter<EhsRoleRepository>, @repository.getter('UserLocationRepository') protected userLocationRepositoryGetter: Getter<UserLocationRepository>, @repository.getter('UserLocationRoleRepository') protected userLocationRoleRepositoryGetter: Getter<UserLocationRoleRepository>, @repository.getter('ReportIncidentRepository') protected reportIncidentRepositoryGetter: Getter<ReportIncidentRepository>, @repository.getter('TowerCraneRepository') protected towerCraneRepositoryGetter: Getter<TowerCraneRepository>, @repository.getter('PermitReportRepository') protected permitReportRepositoryGetter: Getter<PermitReportRepository>, @repository.getter('DepartmentRepository') protected departmentRepositoryGetter: Getter<DepartmentRepository>, @repository.getter('UserUnitAllocationRepository') protected userUnitAllocationRepositoryGetter: Getter<UserUnitAllocationRepository>, @repository.getter('UnitRepository') protected unitRepositoryGetter: Getter<UnitRepository>, @repository.getter('GroupUserRepository') protected groupUserRepositoryGetter: Getter<GroupUserRepository>, @repository.getter('GroupsRepository') protected groupsRepositoryGetter: Getter<GroupsRepository>, @repository.getter('UserChecklistAllocationRepository') protected userChecklistAllocationRepositoryGetter: Getter<UserChecklistAllocationRepository>, @repository.getter('ChecklistRepository') protected checklistRepositoryGetter: Getter<ChecklistRepository>, @repository.getter('UserDocumentAllocationRepository') protected userDocumentAllocationRepositoryGetter: Getter<UserDocumentAllocationRepository>, @repository.getter('DocumentsRepository') protected documentsRepositoryGetter: Getter<DocumentsRepository>, @repository.getter('DesignationRepository') protected designationRepositoryGetter: Getter<DesignationRepository>, @repository.getter('WorkingGroupRepository') protected workingGroupRepositoryGetter: Getter<WorkingGroupRepository>, @repository.getter('GhsOneRepository') protected ghsOneRepositoryGetter: Getter<GhsOneRepository>,
  ) {
    super(User, dataSource);
    this.ghsOne = this.createBelongsToAccessorFor('ghsOne', ghsOneRepositoryGetter,);
    this.registerInclusionResolver('ghsOne', this.ghsOne.inclusionResolver);
    this.workingGroup = this.createBelongsToAccessorFor('workingGroup', workingGroupRepositoryGetter,);
    this.registerInclusionResolver('workingGroup', this.workingGroup.inclusionResolver);
    this.designation = this.createBelongsToAccessorFor('designation', designationRepositoryGetter,);
    this.registerInclusionResolver('designation', this.designation.inclusionResolver);
    this.documents = this.createHasManyThroughRepositoryFactoryFor('documents', documentsRepositoryGetter, userDocumentAllocationRepositoryGetter,);
    this.registerInclusionResolver('documents', this.documents.inclusionResolver);
    this.checklists = this.createHasManyThroughRepositoryFactoryFor('checklists', checklistRepositoryGetter, userChecklistAllocationRepositoryGetter,);
    this.registerInclusionResolver('checklists', this.checklists.inclusionResolver);
    this.groups = this.createHasManyThroughRepositoryFactoryFor('groups', groupsRepositoryGetter, groupUserRepositoryGetter,);
    this.registerInclusionResolver('groups', this.groups.inclusionResolver);
    this.units = this.createHasManyThroughRepositoryFactoryFor('units', unitRepositoryGetter, userUnitAllocationRepositoryGetter,);
    this.registerInclusionResolver('units', this.units.inclusionResolver);
    this.department = this.createBelongsToAccessorFor('department', departmentRepositoryGetter,);
    this.registerInclusionResolver('department', this.department.inclusionResolver);
    this.dscoApproverReports = this.createHasManyRepositoryFactoryFor('dscoApproverReports', permitReportRepositoryGetter,);
    this.registerInclusionResolver('dscoApproverReports', this.dscoApproverReports.inclusionResolver);
    this.approverReports = this.createHasManyRepositoryFactoryFor('approverReports', permitReportRepositoryGetter,);
    this.registerInclusionResolver('approverReports', this.approverReports.inclusionResolver);
    this.assessorReports = this.createHasManyRepositoryFactoryFor('assessorReports', permitReportRepositoryGetter,);
    this.registerInclusionResolver('assessorReports', this.assessorReports.inclusionResolver);
    this.applicantReports = this.createHasManyRepositoryFactoryFor('applicantReports', permitReportRepositoryGetter,);
    this.registerInclusionResolver('applicantReports', this.applicantReports.inclusionResolver);
    this.towerCranes = this.createHasManyRepositoryFactoryFor('towerCranes', towerCraneRepositoryGetter,);
    this.registerInclusionResolver('towerCranes', this.towerCranes.inclusionResolver);
    this.reportIncidents = this.createHasManyRepositoryFactoryFor('reportIncidents', reportIncidentRepositoryGetter,);
    this.registerInclusionResolver('reportIncidents', this.reportIncidents.inclusionResolver);
    this.userLocationRoles = this.createHasManyRepositoryFactoryFor('userLocationRoles', userLocationRoleRepositoryGetter,);
    this.registerInclusionResolver('userLocationRoles', this.userLocationRoles.inclusionResolver);
    this.userLocation = this.createHasOneRepositoryFactoryFor('userLocation', userLocationRepositoryGetter);
    this.registerInclusionResolver('userLocation', this.userLocation.inclusionResolver);
 
    this.ehsRole = this.createBelongsToAccessorFor('ehsRole', ehsRoleRepositoryGetter,);
    this.registerInclusionResolver('ehsRole', this.ehsRole.inclusionResolver);
 
 
    this.userCredentials = this.createHasOneRepositoryFactoryFor(
      'userCredentials',
      userCredentialsRepositoryGetter,
    );
  
  }

  async findCredentials(
    userId: typeof User.prototype.id,
  ): Promise<UserCredentials | undefined> {
    try {
      return await this.userCredentials(userId).get();
    } catch (err) {
      if (err.code === 'ENTITY_NOT_FOUND') {
        return undefined;
      }
      throw err;
    }
  }
}
