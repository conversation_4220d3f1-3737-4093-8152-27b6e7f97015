import {Entity, model, property} from '@loopback/repository';

@model({settings: {strict: false}})
export class GroupUnit extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  groupsId?: string;

  @property({
    type: 'string',
  })
  unitId?: string;
  // Define well-known properties here

  // Indexer property to allow additional data
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [prop: string]: any;

  constructor(data?: Partial<GroupUnit>) {
    super(data);
  }
}

export interface GroupUnitRelations {
  // describe navigational properties here
}

export type GroupUnitWithRelations = GroupUnit & GroupUnitRelations;
