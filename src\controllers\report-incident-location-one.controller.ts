import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  ReportIncident,
  LocationOne,
} from '../models';
import {ReportIncidentRepository} from '../repositories';

export class ReportIncidentLocationOneController {
  constructor(
    @repository(ReportIncidentRepository)
    public reportIncidentRepository: ReportIncidentRepository,
  ) { }

  @get('/report-incidents/{id}/location-one', {
    responses: {
      '200': {
        description: 'LocationOne belonging to ReportIncident',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(LocationOne)},
          },
        },
      },
    },
  })
  async getLocationOne(
    @param.path.string('id') id: typeof ReportIncident.prototype.id,
  ): Promise<LocationOne> {
    return this.reportIncidentRepository.locationOne(id);
  }
}
