import {Entity, model, property} from '@loopback/repository';

@model({settings: {strict: false}})
export class UserChecklistAllocation extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  userId?: string;

  @property({
    type: 'string',
  })
  checklistId?: string;

  @property({
    type: 'string',
    default: null
  })
  groupId?: string;

  @property({
    type: 'string',
  })

  type?: string;
  // Define well-known properties here

  // Indexer property to allow additional data
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [prop: string]: any;

  constructor(data?: Partial<UserChecklistAllocation>) {
    super(data);
  }
}

export interface UserChecklistAllocationRelations {
  // describe navigational properties here
}

export type UserChecklistAllocationWithRelations = UserChecklistAllocation & UserChecklistAllocationRelations;
