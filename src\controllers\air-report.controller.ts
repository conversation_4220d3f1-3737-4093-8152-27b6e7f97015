import {
  Count,
  CountSchema,
  DataObject,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
  HttpErrors,
} from '@loopback/rest';
import { AirReport, Action } from '../models';
import { AirReportRepository, UserRepository, ActionRepository, UserLocationRoleRepository, GeneralGroupRepository } from '../repositories';

import { SqsService } from '../services/sqs-service.service';

import { inject } from '@loopback/core';
import { SecurityBindings, securityId, UserProfile } from '@loopback/security';
import { authenticate } from '@loopback/authentication';
import moment from 'moment';
import { report } from 'process';



@authenticate('jwt')
export class AirReportController {
  constructor(
    @repository(AirReportRepository)
    public airReportRepository: AirReportRepository,
    @repository(GeneralGroupRepository)
    public generalGroupRepository: GeneralGroupRepository,
    @repository(ActionRepository)
    public actionRepository: ActionRepository,
    @repository(UserRepository)
    public userRepository: UserRepository,
    @repository(UserLocationRoleRepository)
    public userLocationRoleRepository: UserLocationRoleRepository,
    @inject('services.SqsService')
    public sqsService: SqsService,
  ) { }

  @post('/air-reports')
  @response(200, {
    description: 'AirReport model instance',
    content: { 'application/json': { schema: getModelSchemaRef(AirReport) } },
  })
  async create(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AirReport, {
            title: 'NewAirReport',
            exclude: ['id'],
          }),
        },
      },
    })
    airReport: Omit<AirReport, 'id'>,
  ): Promise<AirReport> {

    const email = '<EMAIL>'
    const user = await this.userRepository.findOne({ where: { email: email } });
    if (user) {
      let medicalOfficerIds: string[] = []
      let dutyEngManagerIds: string[] = []
      airReport.reporterId = user.id;
      const currentYear = moment().format('YY');
      const yearPrefix = `IR-${currentYear}/`;

      const allReports = await this.airReportRepository.find();
      const countResult = allReports.filter(report =>
        report.maskId?.startsWith(yearPrefix)).length;

      const newId = countResult + 1;
      const formattedDate = moment().format('YY/MM/DD');
      const paddedId = newId.toString().padStart(4, '0');
      const maskId = `IR-${formattedDate}-${paddedId}`;
      airReport.maskId = maskId;
      airReport.status = 'Stage I: Preliminary Notification'
      airReport.stage = 'review'
      airReport.medicalNotificationTime = moment().utcOffset('+05:30').format('DD/MM/YYYY HH:mm')
      airReport.propertyNotificationTime = moment().utcOffset('+05:30').format('DD/MM/YYYY HH:mm')
      const aReport = await this.airReportRepository.create(airReport);
      const actionItem = {
        application: "AIR",
        actionType: "air_reviewer",

        description: airReport.description,
        dueDate: '',

        status: "open",
        createdDate: aReport.created,
        objectId: aReport.id,
        submittedById: user?.id,
        assignedToId: [airReport.reviewerId]
      }

      const hasDamagedEquipment = airReport.damagedEquipmentNumber.some((i: any) => i.category !== "");


      // Check for any injured person inside personInvolved and personnelImpacted
      const personsInvolved = airReport.personInvolved || [];
      const personsImpacted = airReport.personnelImpacted || [];



      // Filter combined array to get only injured individuals

      const isAnyPersonInjured = [...(personsInvolved as any[]), ...(personsImpacted as any[])].some(person => person.injured)
      console.log(isAnyPersonInjured, 'check here')
      let damagedEquipment = airReport.damagedEquipmentNumber.map((i: any) => {
        return (
          `<li>${i.category}${i.number} - Damage Type: ${i.damageType}</li>`
        )
      });

      if (hasDamagedEquipment) {
        const mailSubject = `Notification to Duty Engineering Manager`;
        const mailBody = `<!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Medical Report</title>
        </head>
        <body>
          
            <p><strong>IR Ref Number:</strong> ${airReport.maskId}</p>
            <p><strong>Date & Time of Incident:</strong> ${airReport.incidentDate}</p>
            <p><strong>Damaged Equipment: </strong> </p>
            <ol>
              ${damagedEquipment}
            </ol>
            
            <p><strong>Any Other Type of Damage:</strong> ${airReport.damageType}</p>
            <p><strong>How / Why did the incident occur:</strong> ${airReport.moreDetails}</p>
            <p><strong>Submitted By:</strong> ${user?.firstName}</p>
            <p><strong>Submitted Date / Time:</strong> ${airReport.created}</p>
           
        </body>
        </html>`;

        const roleId = "64d5e4b4a3e49d1da2e04f70";
        const allLocationId = 'all';


        let whereCondition = {
          roles: { inq: [[roleId]] },
          or: [

            {
              locationOneId: { inq: ['tier1-all'] },
              locationTwoId: '',
              locationThreeId: '',
              locationFourId: '',
            },

          ]
        };

        const userLocationRoles = await this.userLocationRoleRepository.find({
          where: whereCondition,
        });

        const userIds = userLocationRoles.map((userLocationRole) => userLocationRole.userId);
        const uniqueUserIds = Array.from(new Set(userIds));
        dutyEngManagerIds = uniqueUserIds;
        const users = await this.userRepository.find({
          where: { id: { inq: uniqueUserIds as string[] } },
        });

        if (users && users.length) {
          for (const user of users) {
            this.sqsService.sendMessage(user, mailSubject, mailBody);
          }
        } else {
          throw new HttpErrors.NotFound(`User not found. Try again`);
        }
      }

      if (isAnyPersonInjured) {
        const personsInvolved = airReport.personInvolved || [];
        const personsImpacted = airReport.personnelImpacted || [];
        const isAnyPersonInjured = [...personsInvolved, ...personsImpacted].filter(person => {
          if (typeof person === 'object' && person !== null) {
            return 'injured' in person && person.injured;
          }
          return false;
        });

        const formattedDetails = isAnyPersonInjured?.map((person: any, index) => {
          if (person && person?.selectedEmp && person?.selectedEmp.name && person.injured)
            return `
              <li>   
                <p>Person ${index + 1}: </p>
                <p>Name: ${person?.selectedEmp.name || 'N/A'} </p>
                <p>Employee ID: ${person?.selectedEmp.uniqueId || 'N/A'} </p>
                <p>Designation: ${person?.selectedEmp.designation || 'N/A'} </p>
                <p>Injured Parts: ${person?.injuryParts.map((i: any) => i).join(',')} </p>
               
            </li>
            `;
          else
            return ``;
        }).join('\n\n');

        const mailSubject = `Notification to Medical Officer`;
        const mailBody = `<!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Medical Report</title>
        </head>
        <body>
          
            <p><strong>IR Ref Number:</strong>  ${airReport.maskId}</p>
            <p><strong>Date & Time of Incident:</strong> ${airReport.incidentDate}</p>
            <p><strong>Person/s Impacted:</strong> </p>
            <ol>
            ${formattedDetails}
            </ol>
            <p><strong>Submitted By:</strong> ${user?.firstName}</p>
            <p><strong>Submitted Date / Time:</strong> ${airReport.created}</p>
           
        </body>
        </html>`;

        const roleId = "64f0375eb7adc567b4925e92";
        const allLocationId = 'all';

        let whereCondition = {
          roles: { inq: [[roleId]] },
          or: [

            {
              locationOneId: { inq: ['tier1-all'] },
              locationTwoId: '',
              locationThreeId: '',
              locationFourId: '',
            },

          ]
        };

        const userLocationRoles = await this.userLocationRoleRepository.find({
          where: whereCondition,
        });

        const userIds = userLocationRoles.map((userLocationRole) => userLocationRole.userId);
        const uniqueUserIds = Array.from(new Set(userIds));
        medicalOfficerIds = uniqueUserIds;
        const users = await this.userRepository.find({
          where: { id: { inq: uniqueUserIds as string[] } },
        });

        if (users && users.length) {
          for (const user of users) {
            this.sqsService.sendMessage(user, mailSubject, mailBody);
          }
        } else {
          throw new HttpErrors.NotFound(`User not found. Try again`);
        }
      }




      if (airReport.reviewerId && user?.id) {
        const airReviewer = await this.userRepository.findById(airReport.reviewerId);
        if (airReviewer) { this.sqsService.sendMessage(airReviewer, `Review incident Reported by ${user.firstName} |  ${aReport.maskId}`, ` ${aReport.maskId} is submitted by ${user?.firstName}. Please Review.`) } else { throw new HttpErrors.NotFound(`User not found. Try again`); }
      }
      await this.actionRepository.create(actionItem)


      let actionList: any[] = []
      if (medicalOfficerIds.length > 0) {
        actionList = [...actionList, {
          application: "AIR",
          actionType: "air_medical_officer",

          description: aReport.description,

          dueDate: '',
          status: "open",
          createdDate: moment().utcOffset('+05:30').format('DD/MM/YYYY HH:mm'),
          objectId: aReport.id,
          submittedById: user?.id,
          assignedToId: medicalOfficerIds
        }]
      }

      if (dutyEngManagerIds.length > 0) {
        actionList = [...actionList, {
          application: "AIR",
          actionType: "air_engineer",

          description: aReport.description,

          dueDate: '',
          status: "open",
          createdDate: moment().utcOffset('+05:30').format('DD/MM/YYYY HH:mm'),
          objectId: aReport.id,
          submittedById: user?.id,
          assignedToId: dutyEngManagerIds
        }]
      }
      if (actionList && actionList.length > 0)
        await this.actionRepository.createAll(actionList)

      return aReport;
    } else {
      throw new HttpErrors.Unauthorized('Unauthorized')
    }



  }

  @get('/air-reports/count')
  @response(200, {
    description: 'AirReport model count',
    content: { 'application/json': { schema: CountSchema } },
  })
  async count(
    @param.where(AirReport) where?: Where<AirReport>,
  ): Promise<any> {

    const currentYear = moment().format('YY');
    const yearPrefix = `IR-${currentYear}/`;

    const allReports = await this.airReportRepository.find();
    const countResult = allReports.filter(report =>
      report.maskId?.startsWith(yearPrefix)).length;

    const newId = countResult + 1;
    const formattedDate = moment().format('YY/MM/DD');
    const paddedId = newId.toString().padStart(4, '0');
    const maskId = `IR-${formattedDate}-${paddedId}`;

    return maskId;
  }

  @get('/air-reports')
  @response(200, {
    description: 'Array of AirReport model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(AirReport, { includeRelations: true }),
        },
      },
    },
  })
  async find(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.filter(AirReport) filter?: Filter<AirReport>,
  ): Promise<AirReport[]> {
    const email = '<EMAIL>'
    const user = await this.userRepository.findOne({ where: { email: email } });

    if (user) {
      const where = {
        or: [
          { reviewerId: user.id },
          { reporterId: user.id },
          { surveyorId: user.id },
          { estimatorId: user.id },
          { traineeId: user.id },
          { gmOpsId: user.id },
          { thirdPartyId: user.id },
          { securityId: user.id },
          { costReviewerId: user.id },
          { financerId: user.id },
          { dutyEngManagerId: user.id },
        ]
      };


      return this.airReportRepository.find({ ...filter, where });
    } else {
      throw new HttpErrors.Unauthorized('Unauthorized')
    }

  }

  @get('/air-reports-classified-groups')
  @response(200, {
    description: 'Array of AirReport model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(AirReport, { includeRelations: true }),
        },
      },
    },
  })
  async findClassifiedGroups(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.filter(AirReport) filter?: Filter<AirReport>,
  ): Promise<AirReport[]> {
    const email = '<EMAIL>'
    const user = await this.userRepository.findOne({ where: { email: email } });
    const userIds = await this.getUserLocationRolesByRoleId('6630b9b9e6a41e57bbe69afc');

    if (!user) {
      throw new HttpErrors.Unauthorized('Unauthorized');
    }

    const excludedStatuses = ["Stage I: Preliminary Notification", "Stage 6: IR Closed"];
    const baseWhereCondition = {
      status: { nin: excludedStatuses }  // Excludes records with specified statuses
    };

    if (userIds.includes(user.id)) {
      // User ID is in the userIds array, display all airReport data excluding specific statuses
      return this.airReportRepository.find({ ...filter, where: { ...filter?.where, ...baseWhereCondition } });
    } else {
      // User ID is not in the userIds array, return airReport data where reviewerId is user.id excluding specific statuses
      const whereCondition = {
        ...baseWhereCondition,
        reviewerId: user.id
      };
      return this.airReportRepository.find({ ...filter, where: { ...filter?.where, ...whereCondition } });
    }

  }

  @get('/air-reports-reviewer')
  @response(200, {
    description: 'Array of AirReport model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(AirReport, { includeRelations: true }),
        },
      },
    },
  })
  async findReviewerAir(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.filter(AirReport) filter?: Filter<AirReport>,
  ): Promise<AirReport[]> {
    const email = '<EMAIL>'
    const user = await this.userRepository.findOne({ where: { email: email } });

    if (user) {
      const where = {
        or: [
          { reviewerId: user.id }
        ]
      };


      return this.airReportRepository.find({ ...filter, where });
    } else {
      throw new HttpErrors.Unauthorized('Unauthorized')
    }

  }

  @get('/air-review-reports')
  @response(200, {
    description: 'Array of AirReport model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(AirReport, { includeRelations: true }),
        },
      },
    },
  })
  async findReviewReports(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.filter(AirReport) filter?: Filter<AirReport>,
  ): Promise<AirReport[]> {
    const email = '<EMAIL>'
    const user = await this.userRepository.findOne({ where: { email: email } });

    if (user) {
      const where = {
        and: [
          { reviewerId: user.id },
          { stage: 'review' }
        ]
      };


      return this.airReportRepository.find({ ...filter, where });
    } else {
      throw new HttpErrors.Unauthorized('Unauthorized')
    }

  }

  @get('/air-investigate-reports')
  @response(200, {
    description: 'Array of AirReport model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(AirReport, { includeRelations: true }),
        },
      },
    },
  })
  async findInvestigateReports(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.filter(AirReport) filter?: Filter<AirReport>,
  ): Promise<AirReport[]> {
    const email = '<EMAIL>'
    const user = await this.userRepository.findOne({ where: { email: email } });

    if (user) {
      const where = {
        and: [
          { status: 'Stage 2: Initial Assessment' },
          { stage: 'trigger_investigation' }
        ]
      };


      return this.airReportRepository.find({ ...filter, where });
    } else {
      throw new HttpErrors.Unauthorized('Unauthorized')
    }

  }

  @get('/air-supervisor-reports')
  @response(200, {
    description: 'Array of AirReport model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(AirReport, { includeRelations: true }),
        },
      },
    },
  })
  async findSupervisor(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.filter(AirReport) filter?: Filter<AirReport>,
  ): Promise<AirReport[]> {
    const email = '<EMAIL>'
    const user = await this.userRepository.findOne({ where: { email: email } });

    if (user) {
      const where = {
        or: [
          { supervisorId: user.id },
        ]
      };


      return this.airReportRepository.find({ ...filter, where });
    } else {
      throw new HttpErrors.Unauthorized('Unauthorized')
    }

  }

  @get('/all-air-reports')
  @response(200, {
    description: 'Array of AirReport model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(AirReport, { includeRelations: true }),
        },
      },
    },
  })
  async findAll(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.filter(AirReport) filter?: Filter<AirReport>,
  ): Promise<AirReport[]> {
    const email = '<EMAIL>'
    const user = await this.userRepository.findOne({ where: { email: email } });

    if (!user) {
      throw new HttpErrors.Unauthorized('Unauthorized');
    }



    return this.airReportRepository.find(filter);



  }


  @get('/my-air-reports')
  @response(200, {
    description: 'Array of AirReport model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(AirReport, { includeRelations: true }),
        },
      },
    },
  })
  async findMyReport(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.filter(AirReport) filter?: Filter<AirReport>,
  ): Promise<AirReport[]> {
    const email = '<EMAIL>'
    const user = await this.userRepository.findOne({ where: { email: email } });
    if (user) {
      return this.airReportRepository.find({ where: { reporterId: user.id } });
    } else {
      throw new HttpErrors.Unauthorized('Unauthorized')
    }

  }

  @patch('/air-reports')
  @response(200, {
    description: 'AirReport PATCH success count',
    content: { 'application/json': { schema: CountSchema } },
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AirReport, { partial: true }),
        },
      },
    })
    airReport: AirReport,
    @param.where(AirReport) where?: Where<AirReport>,
  ): Promise<Count> {
    return this.airReportRepository.updateAll(airReport, where);
  }

  @authenticate.skip()
  @get('/public-air-reports/{id}')
  @response(200, {
    description: 'AirReport model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(AirReport, { includeRelations: true }),
      },
    },
  })
  async publicFindById(
    @param.path.string('id') id: string,
    @param.filter(AirReport, { exclude: 'where' }) filter?: FilterExcludingWhere<AirReport>
  ): Promise<AirReport> {
    return this.airReportRepository.findById(id, filter);
  }

  @get('/air-reports/{id}')
  @response(200, {
    description: 'AirReport model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(AirReport, { includeRelations: true }),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(AirReport, { exclude: 'where' }) filter?: FilterExcludingWhere<AirReport>
  ): Promise<AirReport> {
    return this.airReportRepository.findById(id, filter);
  }

  @patch('/air-reports/{id}')
  @response(204, {
    description: 'AirReport PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AirReport, { partial: true }),
        },
      },
    })
    airReport: AirReport,
  ): Promise<void> {
    await this.airReportRepository.updateById(id, airReport);
  }

  @patch('/air-reports-truck-status/{id}')
  @response(204, {
    description: 'AirReport PATCH success',
  })
  async updateTruckStatusById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AirReport, { partial: true }),
        },
      },
    })
    airReport: AirReport,
  ): Promise<void> {

    //Mail Here | truckStatus
    const formattedDate = moment().utcOffset('+05:30').format('DD/MM/YYYY HH:mm');
    const airReportData = await this.airReportRepository.findById(id)
    const mailSubject = `Release Truck: Notification to Custodian -  ${airReportData.shortDescription}`;
    const mailBody = `<!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Custodian</title>
    </head>
    <body>
      
        <p><strong>IR Ref Number:</strong>  ${airReportData.maskId}</p>
        <p><strong>Date & Time of Release:</strong> ${formattedDate}</p>
        <p><strong>Truck Details</strong> </p>
       
        ${airReportData.bannedTruckDetails}
       
    
        <p><strong>Submitted Date / Time:</strong> ${formattedDate}</p>
        <p>Please Release the vehicle which is under terminal custody.</p>
    </body>
    </html>`;

    const roleId = "64d5c0aca3e49d1da2e04f65";
    const allLocationId = 'all';

    let whereCondition = {
      roles: { inq: [[roleId]] },
      or: [

        {
          locationOneId: { inq: ['tier1-all'] },
          locationTwoId: '',
          locationThreeId: '',
          locationFourId: '',
        },

      ]
    };

    const userLocationRoles = await this.userLocationRoleRepository.find({
      where: whereCondition,
    });

    const userIds = userLocationRoles.map((userLocationRole) => userLocationRole.userId);
    const uniqueUserIds = Array.from(new Set(userIds));

    const users = await this.userRepository.find({
      where: { id: { inq: uniqueUserIds as string[] } },
    });

    if (users && users.length) {
      for (const user of users) {
        await this.sqsService.sendMessage(user, mailSubject, mailBody);
      }
    } else {
      throw new HttpErrors.NotFound(`User not found. Try again`);
    }
    await this.airReportRepository.updateById(id, airReport);
  }

  @patch('/air-reports-update-documents/{id}')
  @response(204, {
    description: 'AirReport PATCH success',
  })
  async updateDocumentsById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AirReport, { partial: true }),
        },
      },
    })
    airReport: AirReport,
  ): Promise<void> {



    await this.airReportRepository.updateById(id, airReport);
  }

  //check this too
  @patch('/air-reviewer-reports/{id}/{actionId}')
  @response(204, {
    description: 'AirReport PATCH success',
  })
  async updateReviewerById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @param.path.string('actionId') actionId: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AirReport, { partial: true }),
        },
      },
    })
    airReport: AirReport,
  ): Promise<void> {

    const email = '<EMAIL>'
    const user = await this.userRepository.findOne({ where: { email: email } });

    const airReportData = await this.airReportRepository.findById(id, {
      include: [
        { relation: "locationThree" },
        { relation: "locationFour" },
        { relation: "weatherCondition" }
      ]
    })

    const locationThreeName = (airReportData as any).locationThree?.name;
    const locationFourName = (airReportData as any).locationFour?.name;
    const weatherCondition = (airReportData as any).weatherCondition?.name;

    airReport.status = 'Stage 2: Initial Assessment';
    airReport.stage = 'classified_documents'
    const formattedDate = moment().utcOffset('+05:30').format('DD/MM/YYYY HH:mm');

    if (airReport.cllInvolved) {
      airReport.custodianNotificationTime = formattedDate;
    }

    if (airReport.insuranceTeamSelected) {
      airReport.insuranceNotificationTime = formattedDate;
    }
    // airReport.investigationNotificationTime = formattedDate;

    if (airReport.insuranceTeamSelected) {

      switch (airReport.insuranceTeamSelected) {

        case 'Fair First':
          const fairFirst = await this.generalGroupRepository.findOne({ where: { name: 'Fair First' } })
          let personInvolved = airReportData.personnelImpacted?.filter((person: any) => person.selectedEmp && person.selectedEmp.name).map(
            (i: any) => {
              return (
                `<li>${i.name} - ${i.uniqueId} </li>`
              )
            }
          );

          let damagedEquipment = airReportData.damagedEquipmentNumber.map((i: any) => {
            return (
              `<li>${i.category} - ${i.number} - ${i.damageType}</li>`
            )
          });

          let mailSubject = `Insurance Notification - ${airReport.shortDescription}`;
          let mailBody = `<!DOCTYPE html>
          <html lang="en">
          <head>
              <meta charset="UTF-8">
              <meta name="viewport" content="width=device-width, initial-scale=1.0">
              <title>Insurance Notification</title>
          </head>
          <body>
            
              <p><strong>IR Ref Number: </strong>  ${airReportData.maskId}</p>
              <p><strong>Date & Time of Incident: </strong> ${airReportData.incidentDate}</p>
              <p><strong>Weather Condition: </strong> ${weatherCondition}</p>
              <p><strong>Area: </strong> ${locationThreeName}</p>
              <p><strong>Zone: </strong> ${locationFourName}</p>
              <p><strong>Did this incident involve a vessel?</strong> ${airReportData.vesselDetails ? airReport.vesselDetails : 'No'}</p>
              <p><strong>Personnel Involved</strong> </p>    
              <ol>
                  ${personInvolved}
              </ol>
              <p><strong>Description of the Incident </strong> ${airReportData.description}</p>
              <p><strong>How and Why did incident occur? </strong> ${airReportData.moreDetails}</p>
              <p><strong>Damaged Equipment: </strong> </p>
              <ol>
                ${damagedEquipment}
              </ol>
            
              <p><strong>Submitted By: </strong> ${user?.firstName}</p>
              <p><strong>Submitted Date / Time:</strong> ${formattedDate}</p>
  
          </body>
          </html>`;

          if (fairFirst && fairFirst.email) {
            for (const email of fairFirst.email) {
              if ('address' in email && email.address) {
                if (typeof email.address === 'string') {
                  this.sqsService.sendEmail(email.address, mailSubject, mailBody);
                }

              }
            }
          }


          break;

        case 'TT Club':
          const ttClub = await this.generalGroupRepository.findOne({ where: { name: 'TT Club' } })
          let personInvolved1 = airReportData.personnelImpacted?.filter((person: any) => person.selectedEmp && person.selectedEmp.name).map(
            (i: any) => {
              return (
                `<li>${i.name} - ${i.uniqueId} </li>`
              )
            }
          );

          let damagedEquipment1 = airReportData.damagedEquipmentNumber.map((i: any) => {
            return (
              `<li>${i.category} - ${i.number} - ${i.damageType}</li>`
            )
          });

          let mailSubject1 = `Insurance Notification - ${airReport.shortDescription}`;
          let mailBody1 = `<!DOCTYPE html>
          <html lang="en">
          <head>
              <meta charset="UTF-8">
              <meta name="viewport" content="width=device-width, initial-scale=1.0">
              <title>Insurance Notification</title>
          </head>
          <body>
            
              <p><strong>IR Ref Number: </strong>  ${airReportData.maskId}</p>
              <p><strong>Date & Time of Incident: </strong> ${airReportData.incidentDate}</p>
              <p><strong>Weather Condition: </strong> ${weatherCondition}</p>
              <p><strong>Area: </strong> ${locationThreeName}</p>
              <p><strong>Zone: </strong> ${locationFourName}</p>
              <p><strong>Did this incident involve a vessel?</strong> ${airReportData.vesselDetails ? airReport.vesselDetails : 'No'}</p>
              <p><strong>Personnel Involved</strong> </p>    
              <ol>
                  ${personInvolved1}
              </ol>
              <p><strong>Description of the Incident </strong> ${airReportData.description}</p>
              <p><strong>How and Why did incident occur? </strong> ${airReportData.moreDetails}</p>
              <p><strong>Damaged Equipment: </strong> </p>
              <ol>
                ${damagedEquipment1}
              </ol>
       
              <p><strong>Submitted By: </strong> ${user?.firstName}</p>
              <p><strong>Submitted Date / Time:</strong> ${formattedDate}</p>
  
          </body>
          </html>`;

          if (ttClub && ttClub.email) {
            for (const email of ttClub.email) {
              if ('address' in email && email.address) {
                if (typeof email.address === 'string') {
                  this.sqsService.sendEmail(email.address, mailSubject1, mailBody1);
                }

              }
            }
          }
          break;
      }
    }




    // if (airReport.investigation && airReport.investigation.investigationTeam && airReport.investigation.investigationTeam.length > 0) {

    //   const personInvolved = airReport.investigation.investigationTeam.map((i: any) => i.label).join(', ')
    //   const mailSubject = `Notification to INVESTIGATION TEAM - ${airReport.shortDescription}`;
    //   const mailBody = `<!DOCTYPE html>
    //     <html lang="en">
    //     <head>
    //         <meta charset="UTF-8">
    //         <meta name="viewport" content="width=device-width, initial-scale=1.0">
    //         <title>Investigation Team</title>
    //     </head>
    //     <body>

    //         <p><strong>IR Ref Number:</strong>  ${airReportData.maskId}</p>
    //         <p><strong>Date & Time scheduled for Investigation:</strong> ${airReport.investigationDate}</p>
    //         <p><strong>Area: </strong> ${locationThreeName}</p>
    //         <p><strong>Zone: </strong> ${locationFourName}</p>
    //         <p><strong>Investigation Team Members: </strong> </p>

    //         ${personInvolved}

    //         <p><strong>Submitted By:</strong> ${user?.firstName}</p>
    //         <p><strong>Submitted Date / Time:</strong> ${formattedDate}</p>

    //     </body>
    //     </html>`;

    //   for (const user of airReport.investigation.investigationTeam) {
    //     const userDetails = await this.userRepository.findById(user.value)
    //     this.sqsService.sendMessage(userDetails, mailSubject, mailBody);
    //   }
    // }

    if (airReport.immediateActionRequired) {
      //Notify concern people
      if (airReport.cllInvolved) {
        airReport.truckStatus = true
        //notify custodion | 64d5c0aca3e49d1da2e04f65
        const mailSubject = `Hold Truck: Notification to Custodian -  ${airReport.shortDescription}`;
        const mailBody = `<!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Custodian</title>
        </head>
        <body>
          
            <p><strong>IR Ref Number:</strong>  ${airReportData.maskId}</p>
            <p><strong>Date & Time of hold:</strong> ${formattedDate}</p>
            <p><strong>Truck Details</strong> </p>
           
            ${airReport.bannedTruckDetails}
           
            <p><strong>Submitted By:</strong> ${user?.firstName}</p>
            <p><strong>Submitted Date / Time:</strong> ${formattedDate}</p>
            <p>Please HOLD the vehicle under terminal custody until further notice.</p>
        </body>
        </html>`;

        const roleId = "64d5c0aca3e49d1da2e04f65";
        const allLocationId = 'all';

        let whereCondition = {
          roles: { inq: [[roleId]] },
          or: [

            {
              locationOneId: { inq: ['tier1-all'] },
              locationTwoId: '',
              locationThreeId: '',
              locationFourId: '',
            },

          ]
        };

        const userLocationRoles = await this.userLocationRoleRepository.find({
          where: whereCondition,
        });

        const userIds = userLocationRoles.map((userLocationRole) => userLocationRole.userId);
        const uniqueUserIds = Array.from(new Set(userIds));

        const users = await this.userRepository.find({
          where: { id: { inq: uniqueUserIds as string[] } },
        });

        if (users && users.length) {
          for (const user of users) {
            this.sqsService.sendMessage(user, mailSubject, mailBody);
          }
        } else {
          throw new HttpErrors.NotFound(`User not found. Try again`);
        }

      }
    }

    if (airReport.isMajorIncident) {
      //inform TT Club
    }

    //email to air group 64d610e7a3e49d1da2e04f72
    const mailSubject = `IR Circulated - ${airReportData.maskId} - ${airReport.shortDescription}`;
    const mailBody = `<!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Custodian</title>
        </head>
        <body>
          
            <p><strong>IR Ref Number:</strong>  ${airReportData.maskId}</p>
            <p><strong>Date & Time of Incident: </strong> ${airReportData.incidentDate}</p>
          
           
            <p><strong>Submitted By:</strong> ${user?.firstName}</p>
            <p><strong>Submitted Date / Time:</strong> ${formattedDate}</p>
            <p>Please login in portal to view in detail or click on the below link to view IR.</p>
            <a href="https://sagt.acuizen.com/download-pdf/${airReportData.id}" target="_blank">Click to View ${airReportData.maskId}</a>
        </body>
        </html>`;

    const roleId = "64d610e7a3e49d1da2e04f72";
    const allLocationId = 'all';

    let whereCondition = {
      roles: { inq: [[roleId]] },
      or: [

        {
          locationOneId: { inq: ['tier1-all'] },
          locationTwoId: '',
          locationThreeId: '',
          locationFourId: '',
        },

      ]
    };

    const userLocationRoles = await this.userLocationRoleRepository.find({
      where: whereCondition,
    });

    const userIds = userLocationRoles.map((userLocationRole) => userLocationRole.userId);
    const uniqueUserIds = Array.from(new Set(userIds));

    const users = await this.userRepository.find({
      where: { id: { inq: uniqueUserIds as string[] } },
    });

    if (users && users.length) {
      for (const user of users) {
        this.sqsService.sendMessage(user, mailSubject, mailBody);
      }
    } else {
      throw new HttpErrors.NotFound(`User not found. Try again`);
    }



    let actionItem = [];



    // actionItem = [
    //   {
    //     application: "AIR",
    //     actionType: "air_cost_estimator",

    //     description: airReportData.description,

    //     dueDate: '',
    //     status: "open",
    //     createdDate: moment().utcOffset('+05:30').format('DD/MM/YYYY HH:mm'),
    //     objectId: airReportData.id,
    //     submittedById: user?.id,
    //     assignedToId: await this.getUserLocationRolesByRoleId('64d495a7a3e49d1da2e04f4c')
    //   }


    // ]

    // if (airReport.reviewerId && user?.id) {
    //   const airReviewer = await this.userRepository.findById(airReportData.reviewerId);
    //   if (airReviewer) { this.sqsService.sendMessage(airReviewer, `Investigate incident Reported by ${user.firstName} |  ${airReportData.maskId}`, ` ${airReportData.maskId} is submitted by ${user?.firstName}. Please Investigate.`) } else { throw new HttpErrors.NotFound(`User not found. Try again`); }
    // }

    // if (airReport.estimatorId && user?.id) {
    //   const airEstimator = await this.userRepository.findById(airReport.estimatorId);
    //   if (airEstimator) { this.sqsService.sendMessage(airEstimator, `Estimate the Cost of incident Reported by ${user.firstName} |  ${airReportData.maskId}`, ` ${airReportData.maskId} is submitted by ${user?.firstName}. Please Estimate the cost of Incident.`) } else { throw new HttpErrors.NotFound(`User not found. Try again`); }
    // }







    await this.actionRepository.updateById(actionId, { status: 'completed' })

    await this.airReportRepository.updateById(id, airReport);
  }


  @patch('/air-initiate-investigation/{id}')
  @response(204, {
    description: 'AirReport PATCH success',
  })
  async updateInitiateInvestigationById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AirReport, { partial: true }),
        },
      },
    })
    airReport: AirReport,
  ): Promise<void> {

    const email = '<EMAIL>'
    const user = await this.userRepository.findOne({ where: { email: email } });
    const formattedDate = moment().utcOffset('+05:30').format('DD/MM/YYYY HH:mm');
    airReport.investigationNotificationTime = moment().utcOffset('+05:30').format('DD/MM/YYYY HH:mm');

    const airReportData = await this.airReportRepository.findById(id, {
      include: [
        { relation: "locationThree" },
        { relation: "locationFour" },
        { relation: "weatherCondition" }
      ]
    })

    const locationThreeName = (airReportData as any).locationThree?.name;
    const locationFourName = (airReportData as any).locationFour?.name;

    airReport.status = 'Stage 3: Reported';
    airReport.stage = 'trigger_investigation';

    await this.airReportRepository.updateById(id, airReport)
    if (airReport.investigation && airReport.investigation.investigationTeam && airReport.investigation.investigationTeam.length > 0) {

      const personInvolved = airReport.investigation.investigationTeam.map((i: any) => i.label).join(', ')
      const mailSubject = `Notification to INVESTIGATION TEAM - ${airReport.shortDescription}`;
      const mailBody = `<!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Investigation Team</title>
        </head>
        <body>
          
            <p><strong>IR Ref Number:</strong>  ${airReportData.maskId}</p>
            <p><strong>Date & Time scheduled for Investigation:</strong> ${airReport.investigationDate}</p>
            <p><strong>Area: </strong> ${locationThreeName}</p>
            <p><strong>Zone: </strong> ${locationFourName}</p>
            <p><strong>Investigation Team Members: </strong> </p>
           
            ${personInvolved}
           
            <p><strong>Submitted By:</strong> ${user?.firstName}</p>
            <p><strong>Submitted Date / Time:</strong> ${formattedDate}</p>
          
        </body>
        </html>`;

      for (const user of airReport.investigation.investigationTeam) {
        const userDetails = await this.userRepository.findById(user.value)
        this.sqsService.sendMessage(userDetails, mailSubject, mailBody);
      }
    }



  }

  //

  @patch('/air-reviewer-reports-without-action/{id}')
  @response(204, {
    description: 'AirReport PATCH success',
  })
  async updateReviewerWithoutActionById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,

    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AirReport, { partial: true }),
        },
      },
    })
    airReport: AirReport,
  ): Promise<void> {

    const email = '<EMAIL>'
    const user = await this.userRepository.findOne({ where: { email: email } });

    const airReportData = await this.airReportRepository.findById(id, {
      include: [
        { relation: "locationThree" },
        { relation: "locationFour" },
        { relation: "weatherCondition" }
      ]
    })

    const locationThreeName = (airReportData as any).locationThree?.name;
    const locationFourName = (airReportData as any).locationFour?.name;
    const weatherCondition = (airReportData as any).weatherCondition?.name;

    airReport.status = 'Stage 2: Initial Assessment';
    airReport.stage = 'classified_documents'
    const formattedDate = moment().utcOffset('+05:30').format('DD/MM/YYYY HH:mm');

    if (airReport.cllInvolved) {
      airReport.custodianNotificationTime = formattedDate;
    }

    if (airReport.insuranceTeamSelected) {
      airReport.insuranceNotificationTime = formattedDate;
    }

    // airReport.investigationNotificationTime = formattedDate;

    if (airReport.insuranceTeamSelected) {

      switch (airReport.insuranceTeamSelected) {

        case 'Fair First':
          const fairFirst = await this.generalGroupRepository.findOne({ where: { name: 'Fair First' } })
          let personInvolved = airReportData.personnelImpacted?.filter((person: any) => person.selectedEmp && person.selectedEmp.name).map(
            (i: any) => {
              return (
                `<li>${i.name} - ${i.uniqueId} </li>`
              )
            }
          );

          let damagedEquipment = airReportData.damagedEquipmentNumber.map((i: any) => {
            return (
              `<li>${i.category} - ${i.number} - ${i.damageType}</li>`
            )
          });

          let mailSubject = `Insurance Notification - ${airReport.shortDescription}`;
          let mailBody = `<!DOCTYPE html>
          <html lang="en">
          <head>
              <meta charset="UTF-8">
              <meta name="viewport" content="width=device-width, initial-scale=1.0">
              <title>Insurance Notification</title>
          </head>
          <body>
            
              <p><strong>IR Ref Number: </strong>  ${airReportData.maskId}</p>
              <p><strong>Date & Time of Incident: </strong> ${airReportData.incidentDate}</p>
              <p><strong>Weather Condition: </strong> ${weatherCondition}</p>
              <p><strong>Area: </strong> ${locationThreeName}</p>
              <p><strong>Zone: </strong> ${locationFourName}</p>
              <p><strong>Did this incident involve a vessel?</strong> ${airReportData.vesselDetails ? airReport.vesselDetails : 'No'}</p>
              <p><strong>Personnel Involved</strong> </p>    
              <ol>
                  ${personInvolved}
              </ol>
              <p><strong>Description of the Incident </strong> ${airReportData.description}</p>
              <p><strong>How and Why did incident occur? </strong> ${airReportData.moreDetails}</p>
              <p><strong>Damaged Equipment: </strong> </p>
              <ol>
                ${damagedEquipment}
              </ol>
            
              <p><strong>Submitted By: </strong> ${user?.firstName}</p>
              <p><strong>Submitted Date / Time:</strong> ${formattedDate}</p>
  
          </body>
          </html>`;

          if (fairFirst && fairFirst.email) {
            for (const email of fairFirst.email) {
              if ('address' in email && email.address) {
                if (typeof email.address === 'string') {
                  this.sqsService.sendEmail(email.address, mailSubject, mailBody);
                }

              }
            }
          }


          break;

        case 'TT Club':
          const ttClub = await this.generalGroupRepository.findOne({ where: { name: 'TT Club' } })
          let personInvolved1 = airReportData.personnelImpacted?.filter((person: any) => person.selectedEmp && person.selectedEmp.name).map(
            (i: any) => {
              return (
                `<li>${i.name} - ${i.uniqueId} </li>`
              )
            }
          );

          let damagedEquipment1 = airReportData.damagedEquipmentNumber.map((i: any) => {
            return (
              `<li>${i.category} - ${i.number} - ${i.damageType}</li>`
            )
          });

          let mailSubject1 = `Insurance Notification - ${airReport.shortDescription}`;
          let mailBody1 = `<!DOCTYPE html>
          <html lang="en">
          <head>
              <meta charset="UTF-8">
              <meta name="viewport" content="width=device-width, initial-scale=1.0">
              <title>Insurance Notification</title>
          </head>
          <body>
            
              <p><strong>IR Ref Number: </strong>  ${airReportData.maskId}</p>
              <p><strong>Date & Time of Incident: </strong> ${airReportData.incidentDate}</p>
              <p><strong>Weather Condition: </strong> ${weatherCondition}</p>
              <p><strong>Area: </strong> ${locationThreeName}</p>
              <p><strong>Zone: </strong> ${locationFourName}</p>
              <p><strong>Did this incident involve a vessel?</strong> ${airReportData.vesselDetails ? airReport.vesselDetails : 'No'}</p>
              <p><strong>Personnel Involved</strong> </p>    
              <ol>
                  ${personInvolved1}
              </ol>
              <p><strong>Description of the Incident </strong> ${airReportData.description}</p>
              <p><strong>How and Why did incident occur? </strong> ${airReportData.moreDetails}</p>
              <p><strong>Damaged Equipment: </strong> </p>
              <ol>
                ${damagedEquipment1}
              </ol>
       
              <p><strong>Submitted By: </strong> ${user?.firstName}</p>
              <p><strong>Submitted Date / Time:</strong> ${formattedDate}</p>
  
          </body>
          </html>`;

          if (ttClub && ttClub.email) {
            for (const email of ttClub.email) {
              if ('address' in email && email.address) {
                if (typeof email.address === 'string') {
                  await this.sqsService.sendEmail(email.address, mailSubject1, mailBody1);
                }

              }
            }
          }
          break;
      }
    }

     if (airReport.thirdPartyForm && airReport.thirdPartyForm.length > 0) {
    
      for (const group of airReport.thirdPartyForm) {

        const percentage = group.percentage;
        console.log(percentage, 'percentage')
        const personInvolved = airReportData.personnelImpacted?.filter((person: any) => person.selectedEmp && person.selectedEmp.name).map(
          (i: any) => {
            return (
              `<li>${i.name} - ${i.uniqueId} </li>`
            )
          }
        );

        const damagedEquipment = airReportData.damagedEquipmentNumber.map((i: any) => {
          return (
            `<li>${i.category} - ${i.number} - ${i.damageType}</li>`
          )
        });

        // Generate evidence images HTML
        const evidenceImages = airReportData.evidence && airReportData.evidence.length > 0
          ? airReportData.evidence.map((filename: string) => {
              const imageUrl = `https://sagt-user-api.acuizen.com/docs/${filename}`;
              return `<div style="margin: 10px 0;"><img src="${imageUrl}" alt="Evidence Image" style="max-width: 500px; height: auto; border: 1px solid #ddd; border-radius: 4px;"></div>`;
            }).join('')
          : '';

        const formattedDates = moment().format('YY/MM/DD');
        const mailSubject = `Third Party Notification - ${airReportData.shortDescription}`;
        const mailBody = `<!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Insurance Notification</title>
        </head>
        <body>

            <p><strong>IR Ref Number: </strong>  ${airReportData.maskId}</p>
            <p><strong>Date & Time of Incident: </strong> ${airReportData.incidentDate}</p>
            <p><strong>Weather Condition: </strong> ${airReportData.weatherCondition.name}</p>
            <p><strong>Area: </strong> ${airReportData.locationThree.name}</p>
            <p><strong>Zone: </strong> ${airReportData.locationFour.name}</p>
            <p><strong>Did this incident involve a vessel?</strong> ${airReportData.vesselDetails ? airReportData.vesselDetails : 'No'}</p>
            <p><strong>Personnel Involved</strong> </p>
            <ol>
                ${personInvolved}
            </ol>
            <p><strong>Description of the Incident </strong> ${airReportData.description}</p>
            <p><strong>How and Why did incident occur? </strong> ${airReportData.moreDetails}</p>
            <p><strong>Damaged Equipment: </strong> </p>
            <ol>
              ${damagedEquipment}
            </ol>
            <p><strong>Liability: </strong> ${percentage}</p>
            <p><strong>Submitted By: </strong> ${user?.firstName}</p>
            <p><strong>Submitted Date / Time:</strong> ${formattedDates}</p>

            ${evidenceImages ? `<p><strong>Evidence Images:</strong></p>${evidenceImages}` : ''}

        </body>
        </html>`;
        console.log(group.emails, 'group.emails')
        const thirdParty = await this.generalGroupRepository.findById(group.emails)
        console.log(thirdParty, 'thirdParty')
        if (thirdParty && thirdParty.email) {
          for (const emails of thirdParty.email) {
            if ('address' in emails && emails.address) {
              if (typeof emails.address === 'string') {
                console.log(emails.address, 'email.address')
                await this.sqsService.sendEmail(emails.address, mailSubject, mailBody);
              }

            }
          }
        }

      }


    }
   

    if (airReport.immediateActionRequired) {
      //Notify concern people
      if (airReport.cllInvolved) {
        airReport.truckStatus = true
        //notify custodion | 64d5c0aca3e49d1da2e04f65
        const mailSubject = `Hold Truck: Notification to Custodian -  ${airReport.shortDescription}`;
        const mailBody = `<!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Custodian</title>
        </head>
        <body>
          
            <p><strong>IR Ref Number:</strong>  ${airReportData.maskId}</p>
            <p><strong>Date & Time of hold:</strong> ${formattedDate}</p>
            <p><strong>Truck Details</strong> </p>
           
            ${airReport.bannedTruckDetails}
           
            <p><strong>Submitted By:</strong> ${user?.firstName}</p>
            <p><strong>Submitted Date / Time:</strong> ${formattedDate}</p>
            <p>Please HOLD the vehicle under terminal custody until further notice.</p>
        </body>
        </html>`;

        const roleId = "64d5c0aca3e49d1da2e04f65";
        const allLocationId = 'all';

        let whereCondition = {
          roles: { inq: [[roleId]] },
          or: [

            {
              locationOneId: { inq: ['tier1-all'] },
              locationTwoId: '',
              locationThreeId: '',
              locationFourId: '',
            },

          ]
        };

        const userLocationRoles = await this.userLocationRoleRepository.find({
          where: whereCondition,
        });

        const userIds = userLocationRoles.map((userLocationRole) => userLocationRole.userId);
        const uniqueUserIds = Array.from(new Set(userIds));

        const users = await this.userRepository.find({
          where: { id: { inq: uniqueUserIds as string[] } },
        });

        if (users && users.length) {
          for (const user of users) {
            this.sqsService.sendMessage(user, mailSubject, mailBody);
          }
        } else {
          throw new HttpErrors.NotFound(`User not found. Try again`);
        }

      }
    }

    if (airReport.isMajorIncident) {
      //inform TT Club
    }

    //email to air group 64d610e7a3e49d1da2e04f72
    const mailSubject = `IR Circulated - ${airReportData.maskId} - ${airReport.shortDescription}`;
    const mailBody = `<!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Custodian</title>
        </head>
        <body>
          
            <p><strong>IR Ref Number:</strong>  ${airReportData.maskId}</p>
            <p><strong>Date & Time of Incident: </strong> ${airReportData.incidentDate}</p>
          
           
            <p><strong>Submitted By:</strong> ${user?.firstName}</p>
            <p><strong>Submitted Date / Time:</strong> ${formattedDate}</p>
            <p>Please login in portal to view in detail or click on the below link to view IR.</p>
            <a href="https://sagt.acuizen.com/download-pdf/${airReportData.id}" target="_blank">Click to View ${airReportData.maskId}</a>
        </body>
        </html>`;

    const roleId = "64d610e7a3e49d1da2e04f72";
    const allLocationId = 'all';

    let whereCondition = {
      roles: { inq: [[roleId]] },
      or: [

        {
          locationOneId: { inq: ['tier1-all'] },
          locationTwoId: '',
          locationThreeId: '',
          locationFourId: '',
        },

      ]
    };

    const userLocationRoles = await this.userLocationRoleRepository.find({
      where: whereCondition,
    });

    const userIds = userLocationRoles.map((userLocationRole) => userLocationRole.userId);
    const uniqueUserIds = Array.from(new Set(userIds));

    const users = await this.userRepository.find({
      where: { id: { inq: uniqueUserIds as string[] } },
    });

    if (users && users.length) {
      for (const user of users) {
        this.sqsService.sendMessage(user, mailSubject, mailBody);
      }
    } else {
      throw new HttpErrors.NotFound(`User not found. Try again`);
    }



    await this.actionRepository.updateAll(
      { status: 'completed' }, // Data to update
      { objectId: id, actionType: 'air_reviewer' } // Where clause
    );


    await this.airReportRepository.updateById(id, airReport);
  }


  @patch('/air-reports-approve/{id}/{actionId}')
  @response(204, {
    description: 'AirReport PATCH success',
  })
  async updateApproveById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @param.path.string('actionId') actionId: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AirReport, { partial: true }),
        },
      },
    })
    airReport: AirReport,
  ): Promise<void> {

    const email = '<EMAIL>'
    const user = await this.userRepository.findOne({ where: { email: email } });

    const airReportData = await this.airReportRepository.findById(id)
    airReport.status = 'Stage 3: Reported';



    let actionItem = [];
    if (airReportData.damagedEquipmentNumber && airReportData.damagedEquipmentNumber.length > 0) {
      actionItem = [{
        application: "AIR",
        actionType: "air_engineer",

        description: airReportData.description,

        dueDate: '',
        status: "open",
        createdDate: moment().utcOffset('+05:30').format('DD/MM/YYYY HH:mm'),
        objectId: airReportData.id,
        submittedById: user?.id,
        assignedToId: [airReportData.engineerId]
      }]

      if (airReportData.engineerId && user?.id) {
        const airEngineer = await this.userRepository.findById(airReportData.engineerId);
        if (airEngineer) { this.sqsService.sendMessage(airEngineer, `Inspect the damaged equipmenet incident Reported by ${user.firstName} |  ${airReportData.maskId}`, ` ${airReportData.maskId} is submitted by ${user?.firstName}. Please do the Survey.`) } else { throw new HttpErrors.NotFound(`User not found. Try again`); }
      }


    } else {
      actionItem = [{
        application: "AIR",
        actionType: "air_investigator",

        description: airReportData.description,

        dueDate: '',
        status: "open",
        createdDate: moment().utcOffset('+05:30').format('DD/MM/YYYY HH:mm'),
        objectId: airReportData.id,
        submittedById: user?.id,
        assignedToId: [airReportData.reviewerId]
      }


      ]

      if (airReportData.surveyorId || airReportData.surveyorEmail) {
        actionItem = [...actionItem,
        {
          application: "AIR",
          actionType: "air_surveyor",

          description: airReportData.description,
          dueDate: '',

          status: "open",
          createdDate: moment().utcOffset('+05:30').format('DD/MM/YYYY HH:mm'),
          objectId: airReportData.id,
          submittedById: user?.id,
          assignedToId: [airReportData.reviewerId]
        }]

        if (airReportData.reviewerId && user?.id) {
          const airSurvyeor = await this.userRepository.findById(airReportData.reviewerId);
          if (airSurvyeor) { this.sqsService.sendMessage(airSurvyeor, `Upload the Survey Document for the incident Reported by ${user.firstName} |  ${airReportData.maskId}`, ` ${airReportData.maskId} is submitted by ${user?.firstName}. Please Upload the Survey Document.`) } else { throw new HttpErrors.NotFound(`User not found. Try again`); }
        }

        if (airReportData.surveyorEmail) {
          this.sqsService.sendEmail(airReportData.surveyorEmail, `Incident to be Surveyed`, ` ${airReportData.maskId} is submitted by ${user?.firstName}. Description: ${airReportData.description}. Please carryout the survey immediately`)
        }


      }

      if (airReportData.reviewerId && user?.id) {
        const airReviewer = await this.userRepository.findById(airReportData.reviewerId);
        if (airReviewer) { this.sqsService.sendMessage(airReviewer, `Investigate incident Reported by ${user.firstName} |  ${airReportData.maskId}`, ` ${airReportData.maskId} is submitted by ${user?.firstName}. Please Investigate.`) } else { throw new HttpErrors.NotFound(`User not found. Try again`); }
      }

      if (airReportData.estimatorId && user?.id) {
        const airEstimator = await this.userRepository.findById(airReportData.estimatorId);
        if (airEstimator) { this.sqsService.sendMessage(airEstimator, `Estimate the Cost of incident Reported by ${user.firstName} |  ${airReportData.maskId}`, ` ${airReportData.maskId} is submitted by ${user?.firstName}. Please Estimate the cost of Incident.`) } else { throw new HttpErrors.NotFound(`User not found. Try again`); }
      }

    }

    if (actionItem && actionItem.length > 0)
      await this.actionRepository.createAll(actionItem)


    await this.actionRepository.updateById(actionId, { status: 'completed' })

    await this.airReportRepository.updateById(id, airReport);
  }


  @patch('/air-reports-approve-second/{id}/{actionId}')
  @response(204, {
    description: 'AirReport PATCH success',
  })
  async updateApproveSecondById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @param.path.string('actionId') actionId: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AirReport, { partial: true }),
        },
      },
    })
    airReport: AirReport,
  ): Promise<void> {

    const email = '<EMAIL>'
    const user = await this.userRepository.findOne({ where: { email: email } });

    const airReportData = await this.airReportRepository.findById(id)
    airReport.status = 'Stage 3: Reported';



    let actionItem = [];

    actionItem = [{
      application: "AIR",
      actionType: "air_investigator",

      description: airReportData.description,

      dueDate: '',
      status: "open",
      createdDate: moment().utcOffset('+05:30').format('DD/MM/YYYY HH:mm'),
      objectId: airReportData.id,
      submittedById: user?.id,
      assignedToId: [airReportData.reviewerId]
    }

    ]

    if (airReportData.surveyorId || airReportData.surveyorEmail) {
      actionItem = [...actionItem,
      {
        application: "AIR",
        actionType: "air_surveyor",

        description: airReportData.description,
        dueDate: '',

        status: "open",
        createdDate: moment().utcOffset('+05:30').format('DD/MM/YYYY HH:mm'),
        objectId: airReportData.id,
        submittedById: user?.id,
        assignedToId: [airReportData.reviewerId]
      }]

      if (airReportData.reviewerId && user?.id) {
        const airSurvyeor = await this.userRepository.findById(airReportData.reviewerId);
        if (airSurvyeor) { this.sqsService.sendMessage(airSurvyeor, `Upload the Survey Document the incident Reported by ${user.firstName} |  ${airReportData.maskId}`, ` ${airReportData.maskId} is submitted by ${user?.firstName}. Please Upload the Survey Document.`) } else { throw new HttpErrors.NotFound(`User not found. Try again`); }
      }

      if (airReportData.surveyorEmail) {
        this.sqsService.sendEmail(airReportData.surveyorEmail, `Incident to be Surveyed`, ` ${airReportData.maskId} is submitted by ${user?.firstName}. Description: ${airReportData.description}. Please carryout the survey immediately`)
      }

    }

    if (airReportData.reviewerId && user?.id) {
      const airReviewer = await this.userRepository.findById(airReportData.reviewerId);
      if (airReviewer) { this.sqsService.sendMessage(airReviewer, `Investigate incident Reported by ${user.firstName} |  ${airReportData.maskId}`, ` ${airReportData.maskId} is submitted by ${user?.firstName}. Please Investigate.`) } else { throw new HttpErrors.NotFound(`User not found. Try again`); }
    }

    if (airReportData.estimatorId && user?.id) {
      const airEstimator = await this.userRepository.findById(airReportData.estimatorId);
      if (airEstimator) { this.sqsService.sendMessage(airEstimator, `Estimate the Cost of incident Reported by ${user.firstName} |  ${airReportData.maskId}`, ` ${airReportData.maskId} is submitted by ${user?.firstName}. Please Estimate the cost of Incident.`) } else { throw new HttpErrors.NotFound(`User not found. Try again`); }
    }



    if (actionItem && actionItem.length > 0)
      await this.actionRepository.createAll(actionItem)


    await this.actionRepository.updateById(actionId, { status: 'completed' })

    await this.airReportRepository.updateById(id, airReport);
  }

  //resubmit here
  @patch('/air-resubmit-reports/{id}/{actionId}')
  @response(204, {
    description: 'AirReport PATCH success',
  })
  async updateResubmitById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @param.path.string('actionId') actionId: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AirReport, { partial: true }),
        },
      },
    })
    airReport: AirReport,
  ): Promise<void> {

    const email = '<EMAIL>'
    const user = await this.userRepository.findOne({ where: { email: email } });

    const airReportData = await this.airReportRepository.findById(id)

    if (user) {
      let medicalOfficerIds: string[] = []
      let dutyEngManagerIds: string[] = []
      airReport.reporterId = user.id;


      airReport.status = 'Stage I: Preliminary Notification'
      airReport.stage = 'review'
      const actionItem = {
        application: "AIR",
        actionType: "air_reviewer",

        description: airReport.description,
        dueDate: '',

        status: "open",
        createdDate: airReport.created,
        objectId: airReportData.id,
        submittedById: user?.id,
        assignedToId: [airReportData.reviewerId]
      }
      if (airReportData.reviewerId && user?.id) {
        const airReviewer = await this.userRepository.findById(airReportData.reviewerId);
        if (airReviewer) { this.sqsService.sendMessage(airReviewer, `Review incident Resubmitted by ${user.firstName} |  ${airReportData.maskId}`, ` ${airReportData.maskId} is re-submitted by ${user?.firstName}. Please Review.`) } else { throw new HttpErrors.NotFound(`User not found. Try again`); }
      }
      await this.actionRepository.create(actionItem)

      const hasDamagedEquipment = airReport.damagedEquipmentNumber.some((i: any) => i.category !== "");


      // Check for any injured person inside personInvolved and personnelImpacted
      const personsInvolved = airReport.personInvolved || [];
      const personsImpacted = airReport.personnelImpacted || [];



      // Filter combined array to get only injured individuals

      const isAnyPersonInjured = [...(personsInvolved as any[]), ...(personsImpacted as any[])].some(person => person.injured)
      console.log(isAnyPersonInjured, 'check here')
      let damagedEquipment = airReport.damagedEquipmentNumber.map((i: any) => {
        return (
          `<li>${i.category}${i.number} - Damage Type: ${i.damageType}</li>`
        )
      });

      if (hasDamagedEquipment) {
        const mailSubject = `Notification to Duty Engineering Manager`;
        const mailBody = `<!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Medical Report</title>
        </head>
        <body>
          
            <p><strong>IR Ref Number:</strong> ${airReport.maskId}</p>
            <p><strong>Date & Time of Incident:</strong> ${airReport.incidentDate}</p>
            <p><strong>Damaged Equipment: </strong> </p>
            <ol>
              ${damagedEquipment}
            </ol>
            
            <p><strong>Any Other Type of Damage:</strong> ${airReport.damageType}</p>
            <p><strong>How / Why did the incident occur:</strong> ${airReport.moreDetails}</p>
            <p><strong>Submitted By:</strong> ${user?.firstName}</p>
            <p><strong>Submitted Date / Time:</strong> ${airReport.created}</p>
           
        </body>
        </html>`;

        const roleId = "64d5e4b4a3e49d1da2e04f70";
        const allLocationId = 'all';


        let whereCondition = {
          roles: { inq: [[roleId]] },
          or: [

            {
              locationOneId: { inq: ['tier1-all'] },
              locationTwoId: '',
              locationThreeId: '',
              locationFourId: '',
            },

          ]
        };

        const userLocationRoles = await this.userLocationRoleRepository.find({
          where: whereCondition,
        });

        const userIds = userLocationRoles.map((userLocationRole) => userLocationRole.userId);
        const uniqueUserIds = Array.from(new Set(userIds));
        dutyEngManagerIds = uniqueUserIds;
        const users = await this.userRepository.find({
          where: { id: { inq: uniqueUserIds as string[] } },
        });

        if (users && users.length) {
          for (const user of users) {
            this.sqsService.sendMessage(user, mailSubject, mailBody);
          }
        } else {
          throw new HttpErrors.NotFound(`User not found. Try again`);
        }
      }

      if (isAnyPersonInjured) {
        const personsInvolved = airReport.personInvolved || [];
        const personsImpacted = airReport.personnelImpacted || [];
        const isAnyPersonInjured = [...personsInvolved, ...personsImpacted].filter(person => {
          if (typeof person === 'object' && person !== null) {
            return 'injured' in person && person.injured;
          }
          return false;
        });

        const formattedDetails = isAnyPersonInjured?.map((person: any, index) => {
          if (person && person?.selectedEmp && person?.selectedEmp.name && person.injured)
            return `
              <li>   
                <p>Person ${index + 1}: </p>
                <p>Name: ${person?.selectedEmp.name || 'N/A'} </p>
                <p>Employee ID: ${person?.selectedEmp.uniqueId || 'N/A'} </p>
                <p>Designation: ${person?.selectedEmp.designation || 'N/A'} </p>
                <p>Injured Parts: ${person?.injuryParts.map((i: any) => i).join(',')} </p>
               
            </li>
            `;
          else
            return ``;
        }).join('\n\n');

        const mailSubject = `Notification to Medical Officer`;
        const mailBody = `<!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Medical Report</title>
        </head>
        <body>
          
            <p><strong>IR Ref Number:</strong>  ${airReport.maskId}</p>
            <p><strong>Date & Time of Incident:</strong> ${airReport.incidentDate}</p>
            <p><strong>Person/s Impacted:</strong> </p>
            <ol>
            ${formattedDetails}
            </ol>
            <p><strong>Submitted By:</strong> ${user?.firstName}</p>
            <p><strong>Submitted Date / Time:</strong> ${airReport.created}</p>
           
        </body>
        </html>`;

        const roleId = "64f0375eb7adc567b4925e92";
        const allLocationId = 'all';

        let whereCondition = {
          roles: { inq: [[roleId]] },
          or: [

            {
              locationOneId: { inq: ['tier1-all'] },
              locationTwoId: '',
              locationThreeId: '',
              locationFourId: '',
            },

          ]
        };

        const userLocationRoles = await this.userLocationRoleRepository.find({
          where: whereCondition,
        });

        const userIds = userLocationRoles.map((userLocationRole) => userLocationRole.userId);
        const uniqueUserIds = Array.from(new Set(userIds));
        medicalOfficerIds = uniqueUserIds;
        const users = await this.userRepository.find({
          where: { id: { inq: uniqueUserIds as string[] } },
        });

        if (users && users.length) {
          for (const user of users) {
            this.sqsService.sendMessage(user, mailSubject, mailBody);
          }
        } else {
          throw new HttpErrors.NotFound(`User not found. Try again`);
        }
      }




      // if (airReport.reviewerId && user?.id) {
      //   const airReviewer = await this.userRepository.findById(airReport.reviewerId);
      //   if (airReviewer) { this.sqsService.sendMessage(airReviewer, `Review incident Reported by ${user.firstName} |  ${airReportData.maskId}`, ` ${airReportData.maskId} is submitted by ${user?.firstName}. Please Review.`) } else { throw new HttpErrors.NotFound(`User not found. Try again`); }
      // }
      // await this.actionRepository.create(actionItem)


      let actionList: any[] = []
      if (medicalOfficerIds.length > 0) {
        actionList = [...actionList, {
          application: "AIR",
          actionType: "air_medical_officer",

          description: airReportData.description,

          dueDate: '',
          status: "open",
          createdDate: moment().utcOffset('+05:30').format('DD/MM/YYYY HH:mm'),
          objectId: airReportData.id,
          submittedById: user?.id,
          assignedToId: medicalOfficerIds
        }]
      }

      if (dutyEngManagerIds.length > 0) {
        actionList = [...actionList, {
          application: "AIR",
          actionType: "air_engineer",

          description: airReportData.description,

          dueDate: '',
          status: "open",
          createdDate: moment().utcOffset('+05:30').format('DD/MM/YYYY HH:mm'),
          objectId: airReportData.id,
          submittedById: user?.id,
          assignedToId: dutyEngManagerIds
        }]
      }
      if (actionList && actionList.length > 0)
        await this.actionRepository.createAll(actionList)

    } else {
      throw new HttpErrors.Unauthorized('Unauthorized')
    }


    await this.actionRepository.updateById(actionId, { status: 'completed' })

    await this.airReportRepository.updateById(id, airReport);
  }



  @patch('/air-medical-reports/{id}/{actionId}')
  @response(204, {
    description: 'AirReport PATCH success',
  })
  async updateMedicalById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @param.path.string('actionId') actionId: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AirReport, { partial: true }),
        },
      },
    })
    airReport: AirReport,
  ): Promise<void> {


    const email = '<EMAIL>'
    const user = await this.userRepository.findOne({ where: { email: email } });
    // airReport.status = 'Stage 3: Reported';
    const actionData = await this.actionRepository.findById(actionId)

    const actionItem = {
      application: "AIR",
      actionType: "air_report_work",

      description: actionData.description,
      dueDate: '',

      status: "open",
      createdDate: `${moment().utcOffset('+05:30').format('DD/MM/YYYY HH:mm')}`,
      objectId: actionData.objectId,
      submittedById: user?.id,
      assignedToId: actionData.assignedToId
    }

    await this.actionRepository.create(actionItem)
    await this.actionRepository.updateById(actionId, { status: 'completed' })

    await this.airReportRepository.updateById(id, airReport);
  }

  @patch('/air-medical-work-reports/{id}/{actionId}')
  @response(204, {
    description: 'AirReport PATCH success',
  })
  async updateMedicalWorkReportById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @param.path.string('actionId') actionId: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AirReport, { partial: true }),
        },
      },
    })
    airReport: AirReport,
  ): Promise<void> {


    await this.actionRepository.updateById(actionId, { status: 'completed' })

    await this.airReportRepository.updateById(id, airReport);
  }


  @patch('/air-engineer-reports/{id}/{actionId}')
  @response(204, {
    description: 'AirReport PATCH success',
  })
  async updateEngineerById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @param.path.string('actionId') actionId: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AirReport, { partial: true }),
        },
      },
    })
    airReport: AirReport,
  ): Promise<void> {

    const email = '<EMAIL>'
    const user = await this.userRepository.findOne({ where: { email: email } });

    // airReport.status = 'Stage 3: Reported';
    const airReportData = await this.airReportRepository.findById(id)

    if (airReport.engineerComments.isEstimate) {
      let actionItem = [
        {
          application: "AIR",
          actionType: "air_cost_estimator",

          description: airReportData.description,

          dueDate: '',
          status: "open",
          createdDate: moment().utcOffset('+05:30').format('DD/MM/YYYY HH:mm'),
          objectId: airReportData.id,
          submittedById: user?.id,
          assignedToId: [airReport.estimatorId]
        }


      ]


      await this.actionRepository.createAll(actionItem)
    }




    await this.actionRepository.updateById(actionId, { status: 'completed' })

    await this.airReportRepository.updateById(id, airReport);
  }


  @patch('/air-review-return/{id}/{actionId}')
  @response(204, {
    description: 'AirReport PATCH success',
  })
  async updateReviewReturnById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @param.path.string('actionId') actionId: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AirReport, { partial: true }),
        },
      },
    })
    airReport: AirReport,
  ): Promise<void> {

    const email = '<EMAIL>'
    const user = await this.userRepository.findOne({ where: { email: email } });

    const airReportData = await this.airReportRepository.findById(id)
    airReport.status = 'Returned'


    let actionItem = [
      {
        application: "AIR",
        actionType: "air_reporter",

        description: airReportData.description,
        dueDate: '',

        status: "open",
        createdDate: moment().utcOffset('+05:30').format('DD/MM/YYYY HH:mm'),
        objectId: airReportData.id,
        submittedById: user?.id,
        assignedToId: [airReportData.reporterId]
      }]

    await this.actionRepository.createAll(actionItem)

    await this.actionRepository.updateById(actionId, { status: 'completed' })

    await this.airReportRepository.updateById(id, airReport);

  }


  @patch('/air-review-return-without-action/{id}')
  @response(204, {
    description: 'AirReport PATCH success',
  })
  async updateReviewReturnWithoutActionById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,

    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AirReport, { partial: true }),
        },
      },
    })
    airReport: AirReport,
  ): Promise<void> {

    const email = '<EMAIL>'
    const user = await this.userRepository.findOne({ where: { email: email } });

    const airReportData = await this.airReportRepository.findById(id)
    airReport.status = 'Returned'
    airReport.stage = 'reporter'

    let actionItem = [
      {
        application: "AIR",
        actionType: "air_reporter",

        description: airReportData.description,
        dueDate: '',

        status: "open",
        createdDate: moment().utcOffset('+05:30').format('DD/MM/YYYY HH:mm'),
        objectId: airReportData.id,
        submittedById: user?.id,
        assignedToId: [airReportData.reporterId]
      }]

    await this.actionRepository.updateAll({ status: "completed" }, { objectId: id });

    await this.actionRepository.createAll(actionItem)



    await this.airReportRepository.updateById(id, airReport);

  }

  @patch('/air-investigator-reports/{id}/{actionId}')
  @response(204, {
    description: 'AirReport PATCH success',
  })
  async updateInvestigatorById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @param.path.string('actionId') actionId: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AirReport, { partial: true }),
        },
      },
    })
    airReport: AirReport,
  ): Promise<void> {

    const email = '<EMAIL>'
    const user = await this.userRepository.findOne({ where: { email: email } });

    const airReportData = await this.airReportRepository.findById(id)
    airReport.status = 'Stage 4: Investigation Phase'

    let actionItem = [{
      application: "AIR",
      actionType: "air_gmops_review",

      description: airReportData.description,

      dueDate: '',
      status: "open",
      createdDate: moment().utcOffset('+05:30').format('DD/MM/YYYY HH:mm'),
      objectId: airReportData.id,
      submittedById: user?.id,
      assignedToId: [airReport.gmOpsId]
    }

    ]

    if (airReport.gmOpsId && user?.id) {
      const airGmOps = await this.userRepository.findById(airReport.gmOpsId);
      if (airGmOps) { this.sqsService.sendMessage(airGmOps, `Review the incident Reported by ${user.firstName} |  ${airReportData.maskId}`, ` ${airReportData.maskId} is submitted by ${user?.firstName}. Please Review.`) } else { throw new HttpErrors.NotFound(`User not found. Try again`); }
    }

    await this.actionRepository.createAll(actionItem)


    await this.actionRepository.updateById(actionId, { status: 'completed' })

    await this.airReportRepository.updateById(id, airReport);
  }


  @patch('/air-gmops-reports/{id}/{actionId}')
  @response(204, {
    description: 'AirReport PATCH success',
  })
  async updateGmOpsById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @param.path.string('actionId') actionId: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AirReport, { partial: true }),
        },
      },
    })
    airReport: AirReport,
  ): Promise<void> {

    const email = '<EMAIL>'
    const user = await this.userRepository.findOne({ where: { email: email } });

    const airReportData = await this.airReportRepository.findById(id)
    airReport.status = 'Stage 5: Action Implementation'

    let actionItem = [{
      application: "AIR",
      actionType: "air_trainee",

      description: airReportData.description,

      dueDate: '',
      status: "open",
      createdDate: moment().utcOffset('+05:30').format('DD/MM/YYYY HH:mm'),
      objectId: airReportData.id,
      submittedById: user?.id,
      assignedToId: [airReport.traineeId]
    }

    ]



    await this.actionRepository.createAll(actionItem)


    await this.actionRepository.updateById(actionId, { status: 'completed' })

    await this.airReportRepository.updateById(id, airReport);
  }


  @patch('/air-cost-estimator-reports/{id}/{actionId}')
  @response(204, {
    description: 'AirReport PATCH success',
  })
  async updateCostEstimatorById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @param.path.string('actionId') actionId: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AirReport, { partial: true }),
        },
      },
    })
    airReport: AirReport,
  ): Promise<void> {

    const email = '<EMAIL>'
    const user = await this.userRepository.findOne({ where: { email: email } });

    const airReportData = await this.airReportRepository.findById(id)

    airReport.status = 'Stage 5: Action Implementation'
    let actionItem = [{
      application: "AIR",
      actionType: "air_duty_manager",

      description: 'Modify / Submit the Cost Estimation',

      dueDate: '',
      status: "open",
      createdDate: moment().utcOffset('+05:30').format('DD/MM/YYYY HH:mm'),
      objectId: airReportData.id,
      submittedById: user?.id,
      assignedToId: [airReport.dutyEngManagerId]
    }

    ]

    if (airReport.dutyEngManagerId && user?.id) {
      const airDutyEngManager = await this.userRepository.findById(airReport.dutyEngManagerId);
      if (airDutyEngManager) { this.sqsService.sendMessage(airDutyEngManager, `Estimate the Actual Cost of incident Reported by ${user.firstName} |  ${airReportData.maskId}`, ` ${airReportData.maskId} is submitted by ${user?.firstName}. Please Estimate the actual cost of Incident.`) } else { throw new HttpErrors.NotFound(`User not found. Try again`); }
    }

    await this.actionRepository.createAll(actionItem)


    await this.actionRepository.updateById(actionId, { status: 'completed' })

    await this.airReportRepository.updateById(id, airReport);
  }


  @patch('/air-duty-manager-estimator-reports/{id}/{actionId}')
  @response(204, {
    description: 'AirReport PATCH success',
  })
  async updateDutyManagerEstimatorById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @param.path.string('actionId') actionId: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AirReport, { partial: true }),
        },
      },
    })
    airReport: AirReport,
  ): Promise<void> {

    const email = '<EMAIL>'
    const user = await this.userRepository.findOne({ where: { email: email } });

    const airReportData = await this.airReportRepository.findById(id)

    airReport.status = 'Stage 5: Action Implementation'
    let actionItem = [{
      application: "AIR",
      actionType: "air_finance",

      description: 'Review the Actual Cost Estimation',

      dueDate: '',
      status: "open",
      createdDate: moment().utcOffset('+05:30').format('DD/MM/YYYY HH:mm'),
      objectId: airReportData.id,
      submittedById: user?.id,
      assignedToId: [airReport.costReviewerId]
    }

    ]


    if (airReport.costReviewerId && user?.id) {
      const airCostReviewer = await this.userRepository.findById(airReport.costReviewerId);
      if (airCostReviewer) { this.sqsService.sendMessage(airCostReviewer, `Review the Actual Cost of incident Reported by ${user.firstName} |  ${airReportData.maskId}`, ` ${airReportData.maskId} is submitted by ${user?.firstName}. Please Review the actual cost of Incident.`) } else { throw new HttpErrors.NotFound(`User not found. Try again`); }
    }

    await this.actionRepository.createAll(actionItem)


    await this.actionRepository.updateById(actionId, { status: 'completed' })

    await this.airReportRepository.updateById(id, airReport);
  }

  @patch('/air-hod-review/{id}/{actionId}')
  @response(204, {
    description: 'AirReport PATCH success',
  })
  async updateHodById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @param.path.string('actionId') actionId: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AirReport, { partial: true }),
        },
      },
    })
    airReport: AirReport,
  ): Promise<void> {

    const email = '<EMAIL>'
    const user = await this.userRepository.findOne({ where: { email: email } });

    const airReportData = await this.airReportRepository.findById(id)

    airReport.status = 'Stage 5: Action Implementation'
    const hodComments = airReport.hodComments;
    const mergedArray = [...hodComments.personInvolved, ...hodComments.personnelImpacted];

    // Mapping over the merged array to extract and rename the keys for internal entries
    const processedData = mergedArray.map(person => {
      if (person.internal) {
        const { selectedEmp: { name, uniqueId, designation } } = person;
        // Remap 'uniqueId' to 'empId', and 'designation' to itself for clarity
        return {
          ...person, // Spread the original person object for any other needed data
          name, // This overwrites the empty 'name' field if needed
          empId: uniqueId,
          designation,
        };
      }
      return person; // Return the person unmodified if 'internal' is not true
    });

    console.log(processedData)
    const tempActions: DataObject<Action>[] | { application: string; additionalDetails: string, actionType: string; description: string; dueDate: any; status: string; actionToBeTaken: any; createdDate: string; objectId: string; submittedById: string | undefined; assignedToId: any[]; }[] = []
    processedData.forEach((person) => {
      if (person.supervisorResponsible) {
        tempActions.push({
          application: "AIR",
          actionType: "take_investigation_actions",
          description: `Supervisor`,
          additionalDetails: `${person.name} - ${person.empId} -${person.designation}`,
          dueDate: person.supervisorDueDate,
          status: 'open',
          actionToBeTaken: person.supervisorAction,
          createdDate: moment().utcOffset('+05:30').format('DD/MM/YYYY HH:mm'),
          objectId: id,
          submittedById: user?.id,
          assignedToId: [person.supervisorResponsible]
        });
      }

      if (person.trainingResponsible) {
        tempActions.push({
          application: "AIR",
          actionType: "take_investigation_actions",
          description: 'Training',
          additionalDetails: `${person.name} - ${person.empId} -${person.designation}`,
          dueDate: person.trainingDueDate,
          status: 'open',
          actionToBeTaken: person.trainingNeed,
          createdDate: moment().utcOffset('+05:30').format('DD/MM/YYYY HH:mm'),
          objectId: id,
          submittedById: user?.id,
          assignedToId: [person.trainingResponsible]
        });
      }

      if (person.hrResponsible) {
        tempActions.push({
          application: "AIR",
          actionType: "take_investigation_actions",
          description: 'HR',
          additionalDetails: `${person.name} - ${person.empId} -${person.designation}`,
          dueDate: person.hrDueDate,
          status: 'open',
          actionToBeTaken: person.hrAction,
          createdDate: moment().utcOffset('+05:30').format('DD/MM/YYYY HH:mm'),
          objectId: id,
          submittedById: user?.id,
          assignedToId: [person.hrResponsible]
        });
      }
    });



    // if (airReport.costReviewerId && user?.id) {
    //   const airCostReviewer = await this.userRepository.findById(airReport.costReviewerId);
    //   if (airCostReviewer) { this.sqsService.sendMessage(airCostReviewer, `Review the Actual Cost of incident Reported by ${user.firstName} |  ${airReportData.maskId}`, ` ${airReportData.maskId} is submitted by ${user?.firstName}. Please Review the actual cost of Incident.`) } else { throw new HttpErrors.NotFound(`User not found. Try again`); }
    // }



    await this.actionRepository.createAll(tempActions)


    await this.actionRepository.updateById(actionId, { status: 'completed' })

    await this.airReportRepository.updateById(id, airReport);
  }


  @patch('/air-cost-reviewer-reports/{id}/{actionId}')
  @response(204, {
    description: 'AirReport PATCH success',
  })
  async updateCostReviewerById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @param.path.string('actionId') actionId: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AirReport, { partial: true }),
        },
      },
    })
    airReport: AirReport,
  ): Promise<void> {

    const email = '<EMAIL>'
    const user = await this.userRepository.findOne({ where: { email: email } });

    const airReportData = await this.airReportRepository.findById(id)

    airReport.status = 'Stage 5: Action Implementation'
    let actionItem = [{
      application: "AIR",
      actionType: "air_cost_reviewer",

      description: 'Finance Settlement',

      dueDate: '',
      status: "open",
      createdDate: moment().utcOffset('+05:30').format('DD/MM/YYYY HH:mm'),
      objectId: airReportData.id,
      submittedById: user?.id,
      assignedToId: [airReport.financerId]
    }

    ]
    if (airReport.financerId && user?.id) {
      const airFinancer = await this.userRepository.findById(airReport.financerId);
      if (airFinancer) { this.sqsService.sendMessage(airFinancer, `Review the Actual Cost of incident Reported by ${user.firstName} |  ${airReportData.maskId}`, ` ${airReportData.maskId} is submitted by ${user?.firstName}. Please Review the actual cost of Incident.`) } else { throw new HttpErrors.NotFound(`User not found. Try again`); }
    }

    await this.actionRepository.createAll(actionItem)


    await this.actionRepository.updateById(actionId, { status: 'completed' })

    await this.airReportRepository.updateById(id, airReport);
  }


  @patch('/air-cost-reviewer-reports-final/{id}/{actionId}')
  @response(204, {
    description: 'AirReport PATCH success',
  })
  async updateCostReviewerFinalById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @param.path.string('actionId') actionId: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AirReport, { partial: true }),
        },
      },
    })
    airReport: AirReport,
  ): Promise<void> {

    const email = '<EMAIL>'
    const user = await this.userRepository.findOne({ where: { email: email } });

    const airReportData = await this.airReportRepository.findById(id)

    airReport.status = 'Stage 5: Action Implementation'





    await this.actionRepository.updateById(actionId, { status: 'completed' })

    await this.airReportRepository.updateById(id, airReport);
  }

  @patch('/air-cost-reviewer-return-reports/{id}/{actionId}')
  @response(204, {
    description: 'AirReport PATCH success',
  })
  async updateCostReviewerReturnById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @param.path.string('actionId') actionId: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AirReport, { partial: true }),
        },
      },
    })
    airReport: AirReport,
  ): Promise<void> {

    const email = '<EMAIL>'
    const user = await this.userRepository.findOne({ where: { email: email } });

    // const airReportData = await this.airReportRepository.findById(id)
    const actionData = await this.actionRepository.findById(actionId)

    delete actionData.id

    console.log(actionData)
    actionData.actionType = 'air_duty_manager';
    actionData.status = 'open';
    await this.actionRepository.create(actionData)


    await this.actionRepository.updateById(actionId, { status: 'completed' })

    // await this.airReportRepository.updateById(id, airReport);
  }

  @patch('/air-hod-finance-return/{id}/{actionId}')
  @response(204, {
    description: 'AirReport PATCH success',
  })
  async updateHodReturnById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @param.path.string('actionId') actionId: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AirReport, { partial: true }),
        },
      },
    })
    airReport: AirReport,
  ): Promise<void> {

    const email = '<EMAIL>'
    const user = await this.userRepository.findOne({ where: { email: email } });
    const airReportData = await this.airReportRepository.findById(id)
    const actionItem = [{
      application: "AIR",
      actionType: "air_finance",

      description: 'Review the Actual Cost Estimation',

      dueDate: '',
      status: "open",
      createdDate: moment().utcOffset('+05:30').format('DD/MM/YYYY HH:mm'),
      objectId: airReportData.id,
      submittedById: user?.id,
      assignedToId: [airReportData.costReviewerId]
    }

    ]


    if (airReportData.costReviewerId && user?.id) {
      const airCostReviewer = await this.userRepository.findById(airReportData.costReviewerId);
      if (airCostReviewer) { await this.sqsService.sendMessage(airCostReviewer, `Review & Edit the Actual Cost of incident Returned by ${user.firstName} |  ${airReportData.maskId}`, ` ${airReportData.maskId} is submitted by ${user?.firstName}. Please Review the actual cost of Incident.`) } else { throw new HttpErrors.NotFound(`User not found. Try again`); }
    }

    await this.actionRepository.createAll(actionItem)


    await this.actionRepository.updateById(actionId, { status: 'completed' })

    await this.airReportRepository.updateById(id, airReport);
  }

  //Claims
  @patch('/air-financer-reports/{id}/{actionId}')
  @response(204, {
    description: 'AirReport PATCH success',
  })
  async updateFinancerById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @param.path.string('actionId') actionId: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AirReport, { partial: true }),
        },
      },
    })
    airReport: AirReport,
  ): Promise<void> {

    const email = '<EMAIL>'
    const user = await this.userRepository.findOne({ where: { email: email } });

    const airReportData = await this.airReportRepository.findById(id, { include: [{ relation: 'locationThree' }, { relation: 'locationFour' }, { relation: 'weatherCondition' }] })

 
    if (airReport.thirdPartyForm && airReport.thirdPartyForm.length > 0) {
    
      for (const group of airReport.thirdPartyForm) {

        const percentage = group.percentage;
        console.log(percentage, 'percentage')
        const personInvolved = airReportData.personnelImpacted?.filter((person: any) => person.selectedEmp && person.selectedEmp.name).map(
          (i: any) => {
            return (
              `<li>${i.name} - ${i.uniqueId} </li>`
            )
          }
        );

        const damagedEquipment = airReportData.damagedEquipmentNumber.map((i: any) => {
          return (
            `<li>${i.category} - ${i.number} - ${i.damageType}</li>`
          )
        });

        // Generate evidence images HTML
        const evidenceImages = airReportData.evidence && airReportData.evidence.length > 0
          ? airReportData.evidence.map((filename: string) => {
              const imageUrl = `https://sagt-user-api.acuizen.com/docs/${filename}`;
              return `<div style="margin: 10px 0;"><img src="${imageUrl}" alt="Evidence Image" style="max-width: 500px; height: auto; border: 1px solid #ddd; border-radius: 4px;"></div>`;
            }).join('')
          : '';

        const formattedDate = moment().format('YY/MM/DD');
        const mailSubject = `Third Party Notification - ${airReportData.shortDescription}`;
        const mailBody = `<!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Insurance Notification</title>
        </head>
        <body>

            <p><strong>IR Ref Number: </strong>  ${airReportData.maskId}</p>
            <p><strong>Date & Time of Incident: </strong> ${airReportData.incidentDate}</p>
            <p><strong>Weather Condition: </strong> ${airReportData.weatherCondition.name}</p>
            <p><strong>Area: </strong> ${airReportData.locationThree.name}</p>
            <p><strong>Zone: </strong> ${airReportData.locationFour.name}</p>
            <p><strong>Did this incident involve a vessel?</strong> ${airReportData.vesselDetails ? airReportData.vesselDetails : 'No'}</p>
            <p><strong>Personnel Involved</strong> </p>
            <ol>
                ${personInvolved}
            </ol>
            <p><strong>Description of the Incident </strong> ${airReportData.description}</p>
            <p><strong>How and Why did incident occur? </strong> ${airReportData.moreDetails}</p>
            <p><strong>Damaged Equipment: </strong> </p>
            <ol>
              ${damagedEquipment}
            </ol>
            <p><strong>Liability: </strong> ${percentage}</p>
            <p><strong>Submitted By: </strong> ${user?.firstName}</p>
            <p><strong>Submitted Date / Time:</strong> ${formattedDate}</p>

            ${evidenceImages ? `<p><strong>Evidence Images:</strong></p>${evidenceImages}` : ''}

        </body>
        </html>`;
        console.log(group.emails, 'group.emails')
        const thirdParty = await this.generalGroupRepository.findById(group.emails)
        console.log(thirdParty, 'thirdParty')
        if (thirdParty && thirdParty.email) {
          for (const email of thirdParty.email) {
            if ('address' in email && email.address) {
              if (typeof email.address === 'string') {
                console.log(email.address, 'email.address')
                await this.sqsService.sendEmail(email.address, mailSubject, mailBody);
              }

            }
          }
        }

      }


    }

    airReport.truckStatus = false
    airReport.status = 'Stage 5: Action Implementation'

    let actionItem = [{
      application: "AIR",
      actionType: "air_hod_finance",

      description: 'Actual Cost Estimation',

      dueDate: '',
      status: "open",
      createdDate: moment().utcOffset('+05:30').format('DD/MM/YYYY HH:mm'),
      objectId: airReportData.id,
      submittedById: user?.id,
      assignedToId: [airReport.hodCheckId]
    }

    ]
    if (airReport.hodCheckId && user?.id) {
      const airFinancer = await this.userRepository.findById(airReport.hodCheckId);
      if (airFinancer) { await this.sqsService.sendMessage(airFinancer, `Review the Actual Cost of incident Reported by ${user.firstName} |  ${airReportData.maskId}`, ` ${airReportData.maskId} is submitted by ${user?.firstName}. Please Review the actual cost of Incident.`) } else { throw new HttpErrors.NotFound(`User not found. Try again`); }
    }

    await this.actionRepository.createAll(actionItem)


    await this.actionRepository.updateById(actionId, { status: 'completed' })

    await this.airReportRepository.updateById(id, airReport);
  }

  @patch('/air-surveyor-reports/{id}/{actionId}')
  @response(204, {
    description: 'AirReport PATCH success',
  })
  async updateSurveyorById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @param.path.string('actionId') actionId: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AirReport, { partial: true }),
        },
      },
    })
    airReport: AirReport,
  ): Promise<void> {


    airReport.status = 'Stage 3: Reported'
    await this.actionRepository.updateById(actionId, { status: 'completed' })

    await this.airReportRepository.updateById(id, airReport);
  }

  @patch('/air-trainee-reports/{id}/{actionId}')
  @response(204, {
    description: 'AirReport PATCH success',
  })
  async updateTraineeById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @param.path.string('actionId') actionId: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AirReport, { partial: true }),
        },
      },
    })
    airReport: AirReport,
  ): Promise<void> {

    const email = '<EMAIL>'
    const user = await this.userRepository.findOne({ where: { email: email } });

    await this.actionRepository.updateById(actionId, { status: 'completed' })

    await this.airReportRepository.updateById(id, airReport);
  }

  @put('/air-reports/{id}')
  @response(204, {
    description: 'AirReport PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() airReport: AirReport,
  ): Promise<void> {
    await this.airReportRepository.replaceById(id, airReport);
  }

  @del('/air-reports/{id}')
  @response(204, {
    description: 'AirReport DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.airReportRepository.deleteById(id);
  }

  @authenticate('jwt')
  @patch('/report-incidents-investigation/{id}')
  @response(204, {
    description: 'IR PATCH success',
  })
  async updateInvestigationById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AirReport, { partial: true }),
        },
      },
    })
    reportIncident: AirReport,
  ): Promise<void> {

    reportIncident.stage = 'Investigated'
    const email = '<EMAIL>'
    const user = await this.userRepository.findOne({ where: { email: email } });
    const reportData = await this.airReportRepository.findById(id)

    let actionItem = [
      {
        application: "AIR",
        actionType: "air_hod_review",

        description: reportData.description,

        dueDate: '',
        status: "open",
        createdDate: moment().utcOffset('+05:30').format('DD/MM/YYYY HH:mm'),
        objectId: reportData.id,
        submittedById: user?.id,
        assignedToId: [reportIncident.hodId]
      }
    ]

    await this.actionRepository.createAll(actionItem)

    // if (reportIncident.investigationStep && reportIncident.investigationStep.dynamicForm && reportIncident.investigationStep.dynamicForm.actions) {
    //   const actions = reportIncident.investigationStep.dynamicForm.actions || [];

    //   if (actions && actions.length > 0) {
    //     const modifiedActions = actions.map((i: any) => {
    //       return {
    //         application: "AIR",
    //         actionType: "take_investigation_actions",
    //         description: '',
    //         dueDate: i.date,
    //         status: 'open',
    //         actionToBeTaken: i.description,
    //         createdDate: moment(new Date()).format('DD-MM-YYYY HH:mm'),
    //         objectId: id,
    //         submittedById: user?.id,
    //         assignedToId: [i.personResponsible]
    //       }
    //     });

    //     try {
    //       const result = await this.actionRepository.createAll(modifiedActions);

    //       console.log("Created records:", result);
    //     } catch (e) {
    //       console.error("Error in creating records:", e.message);
    //       // If you need to debug further, you might want to log the entire error object
    //       console.error(e);
    //     }
    //   }



    // }

    await this.airReportRepository.updateById(id, reportIncident);
  }

  @authenticate('jwt')
  @patch('/report-incident/actions/{id}')
  @response(204, {
    description: 'Action PATCH success',
  })
  async updateActionsById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Action, { partial: true }),
        },
      },
    })
    action: Action,
  ): Promise<void> {
    const email = '<EMAIL>'
    const user = await this.userRepository.findOne({ where: { email: email } });

    const actionData = await this.actionRepository.findById(id)
    action.status = 'completed'
    if (action.actionType === 'take_actions_ra' || action.actionType === 'take_actions_control' || action.actionType === 'retake_actions' || action.actionType === 'take_investigation_actions') {
      delete action.actionType


      const actionItem = {
        application: "AIR",
        actionType: "verify_actions",
        comments: action.comments,
        actionTaken: action.actionTaken,
        actionToBeTaken: action.actionToBeTaken,
        description: action.description,
        dueDate: action.dueDate,
        uploads: action.uploads,
        status: "open",
        createdDate: moment().utcOffset('+05:30').format('DD/MM/YYYY HH:mm'),
        objectId: action.objectId,
        submittedById: user?.id,
        assignedToId: [actionData.submittedById]
      }



      await this.actionRepository.create(actionItem)

    }

    if (action.actionType === 'approve') {

      if (action.objectId) {

        const observationDetails = await this.airReportRepository.findById(action.objectId, {
          include: [
            { relation: 'locationOne' },
            { relation: 'locationTwo' },
            { relation: 'locationThree' },
            { relation: 'locationFour' },
            { relation: 'locationFive' },
            { relation: 'locationSix' },

          ]
        })



      }

    }

    if (action.actionType === 'reject') {

      const actionItem = {
        application: "AIR",
        actionType: "retake_actions",
        comments: action.comments,
        actionTaken: action.actionTaken,
        actionToBeTaken: action.actionToBeTaken,
        description: action.description,
        dueDate: action.dueDate,
        uploads: action.uploads,
        status: "open",
        createdDate: moment().utcOffset('+05:30').format('DD/MM/YYYY HH:mm'),
        objectId: action.objectId,
        submittedById: user?.id,
        assignedToId: action.assignedToId
      }

      await this.actionRepository.create(actionItem)
    }
    delete action.assignedToId
    await this.actionRepository.updateById(id, action);
  }

  @authenticate('jwt')
  @patch('/save-report-incidents-investigation/{id}')
  @response(204, {
    description: 'AIR PATCH success',
  })
  async updateSaveInvestigationById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AirReport, { partial: true }),
        },
      },
    })
    reportIncident: AirReport,
  ): Promise<void> {


    await this.airReportRepository.updateById(id, reportIncident);
  }

  async getUserLocationRolesByRoleId(roleId: string) {
    let whereCondition = {
      roles: { inq: [[roleId]] },
      or: [
        {
          locationOneId: { inq: ['tier1-all'] },
          locationTwoId: '',
          locationThreeId: '',
          locationFourId: '',
        },
      ],
    };

    const userLocationRoles = await this.userLocationRoleRepository.find({
      where: whereCondition,
    });

    const userIds = userLocationRoles.map(userLocationRole => userLocationRole.userId);
    return Array.from(new Set(userIds));
  }
}
