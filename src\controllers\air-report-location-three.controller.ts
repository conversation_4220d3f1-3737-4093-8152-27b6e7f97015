import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  AirReport,
  LocationThree,
} from '../models';
import {AirReportRepository} from '../repositories';

export class AirReportLocationThreeController {
  constructor(
    @repository(AirReportRepository)
    public airReportRepository: AirReportRepository,
  ) { }

  @get('/air-reports/{id}/location-three', {
    responses: {
      '200': {
        description: 'LocationThree belonging to AirReport',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(LocationThree)},
          },
        },
      },
    },
  })
  async getLocationThree(
    @param.path.string('id') id: typeof AirReport.prototype.id,
  ): Promise<LocationThree> {
    return this.airReportRepository.locationThree(id);
  }
}
