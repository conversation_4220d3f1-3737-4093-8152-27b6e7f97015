import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  HumanPartPrimary,
  HumanPartSecondary,
} from '../models';
import {HumanPartPrimaryRepository} from '../repositories';

export class HumanPartPrimaryHumanPartSecondaryController {
  constructor(
    @repository(HumanPartPrimaryRepository) protected humanPartPrimaryRepository: HumanPartPrimaryRepository,
  ) { }

  @get('/human-part-primaries/{id}/human-part-secondaries', {
    responses: {
      '200': {
        description: 'Array of HumanPartPrimary has many HumanPartSecondary',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(HumanPartSecondary)},
          },
        },
      },
    },
  })
  async find(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<HumanPartSecondary>,
  ): Promise<HumanPartSecondary[]> {
    return this.humanPartPrimaryRepository.humanPartSecondaries(id).find(filter);
  }

  @post('/human-part-primaries/{id}/human-part-secondaries', {
    responses: {
      '200': {
        description: 'HumanPartPrimary model instance',
        content: {'application/json': {schema: getModelSchemaRef(HumanPartSecondary)}},
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof HumanPartPrimary.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(HumanPartSecondary, {
            title: 'NewHumanPartSecondaryInHumanPartPrimary',
            exclude: ['id'],
            optional: ['humanPartPrimaryId']
          }),
        },
      },
    }) humanPartSecondary: Omit<HumanPartSecondary, 'id'>,
  ): Promise<HumanPartSecondary> {
    return this.humanPartPrimaryRepository.humanPartSecondaries(id).create(humanPartSecondary);
  }

  @patch('/human-part-primaries/{id}/human-part-secondaries', {
    responses: {
      '200': {
        description: 'HumanPartPrimary.HumanPartSecondary PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(HumanPartSecondary, {partial: true}),
        },
      },
    })
    humanPartSecondary: Partial<HumanPartSecondary>,
    @param.query.object('where', getWhereSchemaFor(HumanPartSecondary)) where?: Where<HumanPartSecondary>,
  ): Promise<Count> {
    return this.humanPartPrimaryRepository.humanPartSecondaries(id).patch(humanPartSecondary, where);
  }

  @del('/human-part-primaries/{id}/human-part-secondaries', {
    responses: {
      '200': {
        description: 'HumanPartPrimary.HumanPartSecondary DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(HumanPartSecondary)) where?: Where<HumanPartSecondary>,
  ): Promise<Count> {
    return this.humanPartPrimaryRepository.humanPartSecondaries(id).delete(where);
  }
}
