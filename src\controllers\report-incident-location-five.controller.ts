import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  ReportIncident,
  LocationFive,
} from '../models';
import {ReportIncidentRepository} from '../repositories';

export class ReportIncidentLocationFiveController {
  constructor(
    @repository(ReportIncidentRepository)
    public reportIncidentRepository: ReportIncidentRepository,
  ) { }

  @get('/report-incidents/{id}/location-five', {
    responses: {
      '200': {
        description: 'LocationFive belonging to ReportIncident',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(LocationFive)},
          },
        },
      },
    },
  })
  async getLocationFive(
    @param.path.string('id') id: typeof ReportIncident.prototype.id,
  ): Promise<LocationFive> {
    return this.reportIncidentRepository.locationFive(id);
  }
}
