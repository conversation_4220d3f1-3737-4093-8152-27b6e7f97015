import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  ReportIncident,
  IncidentRootCauseType,
} from '../models';
import {ReportIncidentRepository} from '../repositories';

export class ReportIncidentIncidentRootCauseTypeController {
  constructor(
    @repository(ReportIncidentRepository)
    public reportIncidentRepository: ReportIncidentRepository,
  ) { }

  @get('/report-incidents/{id}/incident-root-cause-type', {
    responses: {
      '200': {
        description: 'IncidentRootCauseType belonging to ReportIncident',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(IncidentRootCauseType)},
          },
        },
      },
    },
  })
  async getIncidentRootCauseType(
    @param.path.string('id') id: typeof ReportIncident.prototype.id,
  ): Promise<IncidentRootCauseType> {
    return this.reportIncidentRepository.incidentRootCauseType(id);
  }
}
