import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {Designation, DesignationRelations} from '../models';

export class DesignationRepository extends DefaultCrudRepository<
  Designation,
  typeof Designation.prototype.id,
  DesignationRelations
> {
  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource,
  ) {
    super(Designation, dataSource);
  }
}
