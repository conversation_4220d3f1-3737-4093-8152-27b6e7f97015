import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {User, UserChecklistAllocation} from '../models';
import {UserChecklistAllocationRepository,UserRepository,ChecklistRepository} from '../repositories';
import {authenticate} from '@loopback/authentication';
import {OPERATION_SECURITY_SPEC} from '@loopback/authentication-jwt';
import {SecurityBindings, UserProfile, securityId} from '@loopback/security';
import { inject } from '@loopback/core';
type Checklist = {
  checklistId: [],
  userid: string,

};
export class UserChecklistAllocationController {
  constructor(
    @repository(UserChecklistAllocationRepository)
    public userChecklistAllocationRepository : UserChecklistAllocationRepository,
    @repository(UserRepository)
    public userRepository : UserRepository,
    @repository(ChecklistRepository)
    public checklistsRepository : ChecklistRepository,
  ) {}

  @post('/user-checklist-allocations')
  @response(200, {
    description: 'UserChecklistAllocation model instance',
    content: {'application/json': {schema: getModelSchemaRef(UserChecklistAllocation)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(UserChecklistAllocation, {
            title: 'NewUserChecklistAllocation',
            exclude: ['id'],
          }),
        },
      },
    })
    userChecklistAllocation: Omit<UserChecklistAllocation, 'id'>,
  ): Promise<UserChecklistAllocation> {
    return this.userChecklistAllocationRepository.create(userChecklistAllocation);
  }

  @get('/user-checklist-allocations/count')
  @response(200, {
    description: 'UserChecklistAllocation model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(UserChecklistAllocation) where?: Where<UserChecklistAllocation>,
  ): Promise<Count> {
    return this.userChecklistAllocationRepository.count(where);
  }

  @get('/user-checklist-allocations')
  @response(200, {
    description: 'Array of UserChecklistAllocation model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(UserChecklistAllocation, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(UserChecklistAllocation) filter?: Filter<UserChecklistAllocation>,
  ): Promise<UserChecklistAllocation[]> {
    return this.userChecklistAllocationRepository.find(filter);
  }

  @patch('/user-checklist-allocations')
  @response(200, {
    description: 'UserChecklistAllocation PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(UserChecklistAllocation, {partial: true}),
        },
      },
    })
    userChecklistAllocation: UserChecklistAllocation,
    @param.where(UserChecklistAllocation) where?: Where<UserChecklistAllocation>,
  ): Promise<Count> {
    return this.userChecklistAllocationRepository.updateAll(userChecklistAllocation, where);
  }

  @get('/user-checklist-allocations/{id}')
  @response(200, {
    description: 'UserChecklistAllocation model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(UserChecklistAllocation, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(UserChecklistAllocation, {exclude: 'where'}) filter?: FilterExcludingWhere<UserChecklistAllocation>
  ): Promise<UserChecklistAllocation> {
    return this.userChecklistAllocationRepository.findById(id, filter);
  }

  @patch('/user-checklist-allocations/{id}')
  @response(204, {
    description: 'UserChecklistAllocation PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(UserChecklistAllocation, {partial: true}),
        },
      },
    })
    userChecklistAllocation: UserChecklistAllocation,
  ): Promise<void> {
    await this.userChecklistAllocationRepository.updateById(id, userChecklistAllocation);
  }

  @put('/user-checklist-allocations/{id}')
  @response(204, {
    description: 'UserChecklistAllocation PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() userChecklistAllocation: UserChecklistAllocation,
  ): Promise<void> {
    await this.userChecklistAllocationRepository.replaceById(id, userChecklistAllocation);
  }

  @del('/user-checklist-allocations/{id}')
  @response(204, {
    description: 'UserChecklistAllocation DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.userChecklistAllocationRepository.deleteById(id);
  }

  @get('/user-list-checklists')
  @response(200, {
    security: OPERATION_SECURITY_SPEC,
    description: 'Array of GroupUserSingle model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(UserChecklistAllocation, {includeRelations: false}),
        },
      },
    },
  })
  @authenticate('jwt')
  async userDocument(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
  ): Promise<any> {
    // const userId = currentUserProfile[securityId];
    const {email} = currentUserProfile;
    let id =''
    const user : User|null = await this.userRepository.findOne({ where: { email: email } })
    if (user !== null) {
      id = user.id;
    } else {
     
    }
    const risk = await this.userChecklistAllocationRepository.find({
      where: {
        userId: id
      }
    });

    const tierThreeId = risk.map(item => {
      return item.checklistId
    })
    const forms = await this.checklistsRepository.find({
      where: {
        id: {inq: tierThreeId}
      }
    });

   


    return forms;
  }
  @post('/add-checklist-user')
  @response(200, {
    description: 'UserTierThree model instance'
  })
  async userThrees(
    @requestBody() threeOne: Checklist,
  ): Promise<void> {
    if (threeOne.checklistId.length === 0) {
      // return this.groupUserRepository.create(groupUser);
      await this.userChecklistAllocationRepository.deleteAll({
        and: [
          {userId: threeOne.userid},
          {type: 'user'}
        ],

      })

    }
    else {
      const kaOut: (string | undefined)[] = threeOne.checklistId;


      const alreadyRa = await this.userChecklistAllocationRepository.find({
        where: {
          "userId": threeOne.userid
        }
      })
      const kaIn = alreadyRa.map(item => {
        return item.checklistId
      })
      const convertedIntArray: (string | undefined)[] = kaIn.map(num => String(num));


      const insertA = kaOut.filter(item => !convertedIntArray.includes(item));

      if (insertA.length !== 0) {
        insertA.map(async i => {
          await this.userChecklistAllocationRepository.create({"userId": threeOne.userid, "checklistId": i, "type": 'user'});

        })

      }
      const deleteA = convertedIntArray.filter(item => !kaOut.includes(item))

      if (deleteA.length !== 0) {
        deleteA.map(async i => {
          await this.userChecklistAllocationRepository.deleteAll({
            and: [
            
              {checklistId: i},
              {userId: threeOne.userid},
              {type: 'user'}
            ],
          });

        })

      }

    }


  }

  @get('/user-checklist-userid/{userid}')
  @response(200, {
    description: 'UserTierThree model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(UserChecklistAllocation, {includeRelations: false}),
      },
    },
  })
  async findByUserId(
    @param.path.string('userid') userid: string
  ): Promise<UserChecklistAllocation[]> {
    return this.userChecklistAllocationRepository.find({
      where: {
        userId: userid,
        type:'user'
      },

    });
  }
}
