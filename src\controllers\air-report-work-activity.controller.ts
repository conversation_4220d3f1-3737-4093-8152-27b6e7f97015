import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  AirReport,
  WorkActivity,
} from '../models';
import {AirReportRepository} from '../repositories';

export class AirReportWorkActivityController {
  constructor(
    @repository(AirReportRepository)
    public airReportRepository: AirReportRepository,
  ) { }

  @get('/air-reports/{id}/work-activity', {
    responses: {
      '200': {
        description: 'WorkActivity belonging to AirReport',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(WorkActivity)},
          },
        },
      },
    },
  })
  async getWorkActivity(
    @param.path.string('id') id: typeof AirReport.prototype.id,
  ): Promise<WorkActivity> {
    return this.airReportRepository.incidentTypeName(id);
  }
}
