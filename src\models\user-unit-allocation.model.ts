import {Entity, model, property} from '@loopback/repository';

@model({settings: {strict: false}})
export class UserUnitAllocation extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  createdAt?: string;

  @property({
    type: 'string',
  })
  userId?: string;

  @property({
    type: 'string',
  })
  unitId?: string;
  @property({
    type: 'string',
  })
  type?: string;
  @property({
    type: 'string',
    default: null
  })
  groupId?: string;
  // Define well-known properties here

  // Indexer property to allow additional data
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [prop: string]: any;

  constructor(data?: Partial<UserUnitAllocation>) {
    super(data);
  }
}

export interface UserUnitAllocationRelations {
  // describe navigational properties here
}

export type UserUnitAllocationWithRelations = UserUnitAllocation & UserUnitAllocationRelations;
