import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  AirReport,
  LocationFour,
} from '../models';
import {AirReportRepository} from '../repositories';

export class AirReportLocationFourController {
  constructor(
    @repository(AirReportRepository)
    public airReportRepository: AirReportRepository,
  ) { }

  @get('/air-reports/{id}/location-four', {
    responses: {
      '200': {
        description: 'LocationFour belonging to AirReport',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(LocationFour)},
          },
        },
      },
    },
  })
  async getLocationFour(
    @param.path.string('id') id: typeof AirReport.prototype.id,
  ): Promise<LocationFour> {
    return this.airReportRepository.locationFour(id);
  }
}
