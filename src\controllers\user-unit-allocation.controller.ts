import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {authenticate} from '@loopback/authentication';
import {OPERATION_SECURITY_SPEC} from '@loopback/authentication-jwt';
import {SecurityBindings, UserProfile, securityId} from '@loopback/security';
import {StepTitle, UserUnitAllocation} from '../models';
import {UserUnitAllocationRepository,UnitRepository,TopicRepository,AreaRepository,StepTitleRepository} from '../repositories';
import { inject } from '@loopback/core';


export type TierThree = {
  tierThreeId: [],
  userid: string

};

export class UserUnitAllocationController {
  constructor(
    @repository(UserUnitAllocationRepository)
    public userUnitAllocationRepository : UserUnitAllocationRepository,
    @repository(AreaRepository)
    public areaRepository :AreaRepository,
    @repository(TopicRepository)
    public topicRepository :TopicRepository,
    @repository(UnitRepository)
    public unitRepository :UnitRepository,
    @repository(StepTitleRepository)
    public stepRepository :StepTitleRepository
  ) {}

  @post('/user-unit-allocations')
  @response(200, {
    description: 'UserUnitAllocation model instance',
    content: {'application/json': {schema: getModelSchemaRef(UserUnitAllocation)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(UserUnitAllocation, {
            title: 'NewUserUnitAllocation',
            exclude: ['id'],
          }),
        },
      },
    })
    userUnitAllocation: Omit<UserUnitAllocation, 'id'>,
  ): Promise<UserUnitAllocation> {
    return this.userUnitAllocationRepository.create(userUnitAllocation);
  }

  @get('/user-unit-allocations/count')
  @response(200, {
    description: 'UserUnitAllocation model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(UserUnitAllocation) where?: Where<UserUnitAllocation>,
  ): Promise<Count> {
    return this.userUnitAllocationRepository.count(where);
  }

  @get('/user-unit-allocations')
  @response(200, {
    description: 'Array of UserUnitAllocation model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(UserUnitAllocation, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(UserUnitAllocation) filter?: Filter<UserUnitAllocation>,
  ): Promise<UserUnitAllocation[]> {
    return this.userUnitAllocationRepository.find(filter);
  }

  @patch('/user-unit-allocations')
  @response(200, {
    description: 'UserUnitAllocation PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(UserUnitAllocation, {partial: true}),
        },
      },
    })
    userUnitAllocation: UserUnitAllocation,
    @param.where(UserUnitAllocation) where?: Where<UserUnitAllocation>,
  ): Promise<Count> {
    return this.userUnitAllocationRepository.updateAll(userUnitAllocation, where);
  }

  @get('/user-unit-allocations/{id}')
  @response(200, {
    description: 'UserUnitAllocation model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(UserUnitAllocation, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(UserUnitAllocation, {exclude: 'where'}) filter?: FilterExcludingWhere<UserUnitAllocation>
  ): Promise<UserUnitAllocation> {
    return this.userUnitAllocationRepository.findById(id, filter);
  }

  @patch('/user-unit-allocations/{id}')
  @response(204, {
    description: 'UserUnitAllocation PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(UserUnitAllocation, {partial: true}),
        },
      },
    })
    userUnitAllocation: UserUnitAllocation,
  ): Promise<void> {
    await this.userUnitAllocationRepository.updateById(id, userUnitAllocation);
  }

  @put('/user-unit-allocations/{id}')
  @response(204, {
    description: 'UserUnitAllocation PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() userUnitAllocation: UserUnitAllocation,
  ): Promise<void> {
    await this.userUnitAllocationRepository.replaceById(id, userUnitAllocation);
  }

  @del('/user-unit-allocations/{id}')
  @response(204, {
    description: 'UserUnitAllocation DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.userUnitAllocationRepository.deleteById(id);
  }

  @post('/add-ku-user-threes')
  @response(200, {
    description: 'UserTierThree model instance'
  })
  async userThrees(
    @requestBody() threeOne: TierThree,
  ): Promise<void> {
    if (threeOne.tierThreeId.length === 0) {
      // return this.groupUserRepository.create(groupUser);
      await this.userUnitAllocationRepository.deleteAll({
        and: [
          {userId: threeOne.userid},
          {type: 'user'}
        ],

      })

    }
    else {
      const kaOut: (string | undefined)[] = threeOne.tierThreeId;


      const alreadyRa = await this.userUnitAllocationRepository.find({
        where: {
          "userId": threeOne.userid
        }
      })
      const kaIn = alreadyRa.map(item => {
        return item.unitId
      })
      const convertedIntArray: (string | undefined)[] = kaIn.map(num => String(num));


      const insertA = kaOut.filter(item => !convertedIntArray.includes(item));

      if (insertA.length !== 0) {
        insertA.map(async i => {
          await this.userUnitAllocationRepository.create({"userId": threeOne.userid, "unitId": i, "type": 'user'});

        })

      }
      const deleteA = convertedIntArray.filter(item => !kaOut.includes(item))

      if (deleteA.length !== 0) {
        deleteA.map(async i => {
          await this.userUnitAllocationRepository.deleteAll({
            and: [
              {unitId: i},
              {userId: threeOne.userid},
              {type: 'user'}
            ],
          });

        })

      }

    }


  }

  @get('/user-tier-threes-userid/{userid}')
  @response(200, {
    description: 'UserTierThree model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(UserUnitAllocation, {includeRelations: false}),
      },
    },
  })
  async findByUserId(
    @param.path.string('userid') userid: string
  ): Promise<UserUnitAllocation[]> {
    return this.userUnitAllocationRepository.find({
      where: {
        userId: userid,
        type:'user'
      },

    });
  }

  @get('/user-list-programs')
  @response(200, {
    security: OPERATION_SECURITY_SPEC,
    description: 'Array of GroupUserSingle model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(UserUnitAllocation, {includeRelations: false}),
        },
      },
    },
  })
  @authenticate('jwt')
  async userRisk(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
  ): Promise<string[]> {
    // const userId = currentUserProfile[securityId];
    const { id } = currentUserProfile;
   
    const risk = await this.userUnitAllocationRepository.find({
      where: {
        userId: id
      }
    });

   

    const tierThreeId = risk.map(item => {
      return item.unitId
    })
    const three = await this.unitRepository.find({
      where: {
        id: {inq: tierThreeId}
      }
    });

    const tierTwoId = three.map(item => {
      return item.topicId
    })
    const two = await this.topicRepository.find({
      where: {
        id: {inq: tierTwoId}
      }
    });
    const tierOneId = two.map(item => {
      return item.areaId
    })
    const one = await this.areaRepository.find({
      where: {
        id: {inq: tierOneId}
      }
    });

    // console.log(one)
    // const tierOneId = one.map(item=>{
    //   return item.id
    // })
    const resultArray: any[] = [];

    let i = 0;

    for (const productKeys of one) {
      resultArray[i] = {
        name: productKeys.title,
        models: [],
      };

      const models = await this.topicRepository.find({
        where: {areaId: productKeys.id},
      });

      let j = 0;

      for (const modelKeys of models) {
        resultArray[i].models[j] = {
          modelName: modelKeys.title,
          programs: [],
        };

        const programs = await this.unitRepository.find({
          where: {
            and: [
              {topicId: modelKeys.id},
              {id: {inq: tierThreeId}},
            ],
          },
        });

        let k = 0;

        for (const programKeys of programs) {
          resultArray[i].models[j].programs[k] = {
            programName: programKeys.title,
            programId: programKeys.id
          };

          // const steps = await this.stepTitleRepository.find({
          //   where: {tierThreeId: programKeys.id},
          // });

          // if (steps.length > 0) {
          //   resultArray[i].models[j].programs[k].steps = steps;
          //   k++;
          // } else {
          //   resultArray[i].models[j].programs.splice(k, 1); // Remove the empty program from the resultArray
          // }
          k++;
        }
      
        if (programs.length > 0) {
          j++;
        } else {
          resultArray[i].models.splice(j, 1); // Remove the empty model from the resultArray
        }
      }

      if (resultArray[i].models.length > 0) {
        i++;
      } else {
        resultArray.splice(i, 1); // Remove the empty product from the resultArray
      }
    }

    return resultArray;
  }

  @get('/user-list-steps/{id}')
  @response(200, {
    security: OPERATION_SECURITY_SPEC,
    description: 'Array of GroupUserSingle model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(UserUnitAllocation, {includeRelations: false}),
        },
      },
    },
  })
  @authenticate('jwt')
  async userstep(
    @param.path.string('id') id: string,
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
  ): Promise<StepTitle[]> {
    const userId = currentUserProfile[securityId];
    const risk = await this.userUnitAllocationRepository.findOne({
      where: {
        and: [
          {userId: userId},
          {unitId: id},
        ],
      }
    });
    let steps: any = []
    if (risk) {
      steps = await this.stepRepository.find({
        where: {unitId: id},
      });
    }

    return steps
  }
}
