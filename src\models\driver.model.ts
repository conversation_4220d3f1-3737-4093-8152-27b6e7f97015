import { Entity, model, property } from '@loopback/repository';

@model()
export class Driver extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'string',
  })
  vehicleNo?: string;

  @property({
    type: 'string',
  })
  licenseNo?: string;

  @property({
    type: 'string',
  })
  employeeId?: string;

  @property({
    type: 'boolean',
  })
  ban?: boolean;

  @property({
    type: 'string',
  })
  airReportId?: string;

  constructor(data?: Partial<Driver>) {
    super(data);
  }
}

export interface DriverRelations {
  // describe navigational properties here
}

export type DriverWithRelations = Driver & DriverRelations;
