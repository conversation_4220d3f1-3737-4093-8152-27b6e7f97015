import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {HumanPartTertiary, HumanPartTertiaryRelations} from '../models';

export class HumanPartTertiaryRepository extends DefaultCrudRepository<
  HumanPartTertiary,
  typeof HumanPartTertiary.prototype.id,
  HumanPartTertiaryRelations
> {
  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource,
  ) {
    super(HumanPartTertiary, dataSource);
  }
}
