import {Entity, model, property, hasMany, belongsTo} from '@loopback/repository';
import {ObservationReport} from './observation-report.model';

@model({settings: {strict: false}})
export class GhsTwo extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'string',
  })
  created?: string;

  @property({
    type: 'string',
  })
  modified?: string;

  @property({
    type: 'string',
  })
  ghsOneId?: string;

  @hasMany(() => ObservationReport)
  observationReports: ObservationReport[];
  // Define well-known properties here

  // Indexer property to allow additional data
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [prop: string]: any;

  constructor(data?: Partial<GhsTwo>) {
    super(data);
  }
}

export interface GhsTwoRelations {
  // describe navigational properties here
}

export type GhsTwoWithRelations = GhsTwo & GhsTwoRelations;
