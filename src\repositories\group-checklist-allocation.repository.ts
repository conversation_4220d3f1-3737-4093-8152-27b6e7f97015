import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {GroupChecklistAllocation, GroupChecklistAllocationRelations} from '../models';

export class GroupChecklistAllocationRepository extends DefaultCrudRepository<
  GroupChecklistAllocation,
  typeof GroupChecklistAllocation.prototype.id,
  GroupChecklistAllocationRelations
> {
  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource,
  ) {
    super(GroupChecklistAllocation, dataSource);
  }
}
