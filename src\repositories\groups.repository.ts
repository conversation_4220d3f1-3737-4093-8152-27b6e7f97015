import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasManyThroughRepositoryFactory} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {Groups, GroupsRelations, User, GroupUser, Unit, GroupUnit, Checklist, GroupChecklistAllocation, Documents, GroupDocumentAllocation} from '../models';
import {GroupUserRepository} from './group-user.repository';
import {UserRepository} from './user.repository';
import {GroupUnitRepository} from './group-unit.repository';
import {UnitRepository} from './unit.repository';
import {GroupChecklistAllocationRepository} from './group-checklist-allocation.repository';
import {ChecklistRepository} from './checklist.repository';
import {GroupDocumentAllocationRepository} from './group-document-allocation.repository';
import {DocumentsRepository} from './documents.repository';

export class GroupsRepository extends DefaultCrudRepository<
  Groups,
  typeof Groups.prototype.id,
  GroupsRelations
> {

  public readonly users: HasManyThroughRepositoryFactory<User, typeof User.prototype.id,
          GroupUser,
          typeof Groups.prototype.id
        >;

  public readonly units: HasManyThroughRepositoryFactory<Unit, typeof Unit.prototype.id,
          GroupUnit,
          typeof Groups.prototype.id
        >;

  public readonly checklists: HasManyThroughRepositoryFactory<Checklist, typeof Checklist.prototype.id,
          GroupChecklistAllocation,
          typeof Groups.prototype.id
        >;

  public readonly documents: HasManyThroughRepositoryFactory<Documents, typeof Documents.prototype.id,
          GroupDocumentAllocation,
          typeof Groups.prototype.id
        >;

  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource, @repository.getter('GroupUserRepository') protected groupUserRepositoryGetter: Getter<GroupUserRepository>, @repository.getter('UserRepository') protected userRepositoryGetter: Getter<UserRepository>, @repository.getter('GroupUnitRepository') protected groupUnitRepositoryGetter: Getter<GroupUnitRepository>, @repository.getter('UnitRepository') protected unitRepositoryGetter: Getter<UnitRepository>, @repository.getter('GroupChecklistAllocationRepository') protected groupChecklistAllocationRepositoryGetter: Getter<GroupChecklistAllocationRepository>, @repository.getter('ChecklistRepository') protected checklistRepositoryGetter: Getter<ChecklistRepository>, @repository.getter('GroupDocumentAllocationRepository') protected groupDocumentAllocationRepositoryGetter: Getter<GroupDocumentAllocationRepository>, @repository.getter('DocumentsRepository') protected documentsRepositoryGetter: Getter<DocumentsRepository>, 
  ) {
    super(Groups, dataSource);
    this.documents = this.createHasManyThroughRepositoryFactoryFor('documents', documentsRepositoryGetter, groupDocumentAllocationRepositoryGetter,);
    this.registerInclusionResolver('documents', this.documents.inclusionResolver);
    this.checklists = this.createHasManyThroughRepositoryFactoryFor('checklists', checklistRepositoryGetter, groupChecklistAllocationRepositoryGetter,);
    this.registerInclusionResolver('checklists', this.checklists.inclusionResolver);
    this.units = this.createHasManyThroughRepositoryFactoryFor('units', unitRepositoryGetter, groupUnitRepositoryGetter,);
    this.registerInclusionResolver('units', this.units.inclusionResolver);
    this.users = this.createHasManyThroughRepositoryFactoryFor('users', userRepositoryGetter, groupUserRepositoryGetter,);
    this.registerInclusionResolver('users', this.users.inclusionResolver);
   
  }
}
