import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  LocationOne,
  ObservationReport,
} from '../models';
import {LocationOneRepository} from '../repositories';

export class LocationOneObservationReportController {
  constructor(
    @repository(LocationOneRepository) protected locationOneRepository: LocationOneRepository,
  ) { }

  @get('/location-ones/{id}/observation-reports', {
    responses: {
      '200': {
        description: 'Array of LocationOne has many ObservationReport',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(ObservationReport)},
          },
        },
      },
    },
  })
  async find(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<ObservationReport>,
  ): Promise<ObservationReport[]> {
    return this.locationOneRepository.observationReports(id).find(filter);
  }

  @post('/location-ones/{id}/observation-reports', {
    responses: {
      '200': {
        description: 'LocationOne model instance',
        content: {'application/json': {schema: getModelSchemaRef(ObservationReport)}},
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof LocationOne.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ObservationReport, {
            title: 'NewObservationReportInLocationOne',
            exclude: ['id'],
            optional: ['locationOneId']
          }),
        },
      },
    }) observationReport: Omit<ObservationReport, 'id'>,
  ): Promise<ObservationReport> {
    return this.locationOneRepository.observationReports(id).create(observationReport);
  }

  @patch('/location-ones/{id}/observation-reports', {
    responses: {
      '200': {
        description: 'LocationOne.ObservationReport PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ObservationReport, {partial: true}),
        },
      },
    })
    observationReport: Partial<ObservationReport>,
    @param.query.object('where', getWhereSchemaFor(ObservationReport)) where?: Where<ObservationReport>,
  ): Promise<Count> {
    return this.locationOneRepository.observationReports(id).patch(observationReport, where);
  }

  @del('/location-ones/{id}/observation-reports', {
    responses: {
      '200': {
        description: 'LocationOne.ObservationReport DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(ObservationReport)) where?: Where<ObservationReport>,
  ): Promise<Count> {
    return this.locationOneRepository.observationReports(id).delete(where);
  }
}
