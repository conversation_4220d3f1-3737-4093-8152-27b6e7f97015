import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  KnowledgeSessionRecord,
  Unit,
} from '../models';
import {KnowledgeSessionRecordRepository} from '../repositories';

export class KnowledgeSessionRecordUnitController {
  constructor(
    @repository(KnowledgeSessionRecordRepository)
    public knowledgeSessionRecordRepository: KnowledgeSessionRecordRepository,
  ) { }

  @get('/knowledge-session-records/{id}/unit', {
    responses: {
      '200': {
        description: 'Unit belonging to KnowledgeSessionRecord',
        content: {
          'application/json': {
            schema: getModelSchemaRef(Unit),
          },
        },
      },
    },
  })
  async getUnit(
    @param.path.string('id') id: typeof KnowledgeSessionRecord.prototype.id,
  ): Promise<Unit> {
    return this.knowledgeSessionRecordRepository.unit(id);
  }
}
