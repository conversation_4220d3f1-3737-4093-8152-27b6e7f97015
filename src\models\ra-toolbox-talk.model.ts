import {Entity, model, property, belongsTo} from '@loopback/repository';
import {RiskAssessment} from './risk-assessment.model';
import {LocationOne} from './location-one.model';
import {LocationTwo} from './location-two.model';
import {LocationThree} from './location-three.model';
import {LocationFour} from './location-four.model';
import {LocationFive} from './location-five.model';
import {LocationSix} from './location-six.model';
import {User} from './user.model';

@model({settings: {strict: false}})
export class RaToolboxTalk extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'array',
    itemType: 'object',
  })
  sign?: object[];

  @property({
    type: 'array',
    itemType: 'object',
  })
  closeOut?: object[];

  @property({
    type: 'array',
    itemType: 'object',
  })
  checklist?: object[];

  @property({
    type: 'string',
  })
  createdAt?: string;
  @property({
    type: 'string',
  })
  participants?: string;

  @property({
    type: 'string',
  })
  image?: string;

  @property({
    type: 'string',
  })
  startTime?: string;

  @property({
    type: 'string',
  })
  endTime?: string;

  @property({
    type: 'object',
  })
  additionalDetails?: object;


  @belongsTo(() => RiskAssessment)
  riskAssessmentId: string;

  @belongsTo(() => LocationOne)
  locationOneId: string;

  @belongsTo(() => LocationTwo)
  locationTwoId: string;

  @belongsTo(() => LocationThree)
  locationThreeId: string;

  @belongsTo(() => LocationFour)
  locationFourId: string;

  @belongsTo(() => LocationFive)
  locationFiveId: string;

  @belongsTo(() => LocationSix)
  locationSixId: string;

  @belongsTo(() => User)
  userId: string;
  // Define well-known properties here

  // Indexer property to allow additional data
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [prop: string]: any;

  constructor(data?: Partial<RaToolboxTalk>) {
    super(data);
  }
}

export interface RaToolboxTalkRelations {
  // describe navigational properties here
}

export type RaToolboxTalkWithRelations = RaToolboxTalk & RaToolboxTalkRelations;
