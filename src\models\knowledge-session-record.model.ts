import {Entity, model, property, belongsTo} from '@loopback/repository';
import {User} from './user.model';
import {Area} from './area.model';
import {Topic} from './topic.model';
import {Unit} from './unit.model';

@model({settings: {strict: false}})
export class KnowledgeSessionRecord extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  sessionId?: string;

  @property({
    type: 'string',
    default: null,
  })
  stepsData?: string;

  @property({
    type: 'string',
  })
  createdAt?: string;

  @property({
    type: 'boolean',
    default: null,
  })
  hasMCQ?: boolean;

  @property({
    type: 'number',
    default: null,
  })
  insightIndex?: number;

  @property({
    type: 'number',
    default: null,
  })
  red?: number;

  @property({
    type: 'number',
    default: null,
  })
  green?: number;

  @property({
    type: 'number',
    default: null,
  })
  blue?: number;

  @property({
    type: 'number',
    default: null,
  })
  yellow?: number;

  @property({
    type: 'string',
  })
  duration?: string;

  @property({
    type: 'string',
    default: null,
  })
  rating?: string;

  @property({
    type: 'string',
    default: null,
  })
  remarks?: string;

  @property({
    type: 'string',
    default: null,
  })
  address?: string;

  @property({
    type: 'string',
  })
  status?: string;

  @property({
    type: 'string',
  })
  tick?: string;

  @property({
    type: 'number',
  })
  qCount?: number;

  @property({
    type: 'string',
  })
  updatedAt?: string;

  @belongsTo(() => User)
  userId: string;

  @belongsTo(() => Area)
  areaId: string;

  @belongsTo(() => Topic)
  topicId: string;

  @belongsTo(() => Unit)
  unitId: string;
  // Define well-known properties here

  // Indexer property to allow additional data
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [prop: string]: any;

  constructor(data?: Partial<KnowledgeSessionRecord>) {
    super(data);
  }
}

export interface KnowledgeSessionRecordRelations {
  // describe navigational properties here
}

export type KnowledgeSessionRecordWithRelations = KnowledgeSessionRecord & KnowledgeSessionRecordRelations;
