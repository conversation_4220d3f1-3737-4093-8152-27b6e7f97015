import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  KnowledgeSessionRecord,
  User,
} from '../models';
import {KnowledgeSessionRecordRepository} from '../repositories';

export class KnowledgeSessionRecordUserController {
  constructor(
    @repository(KnowledgeSessionRecordRepository)
    public knowledgeSessionRecordRepository: KnowledgeSessionRecordRepository,
  ) { }

  @get('/knowledge-session-records/{id}/user', {
    responses: {
      '200': {
        description: 'User belonging to KnowledgeSessionRecord',
        content: {
          'application/json': {
            schema: getModelSchemaRef(User),
          },
        },
      },
    },
  })
  async getUser(
    @param.path.string('id') id: typeof KnowledgeSessionRecord.prototype.id,
  ): Promise<User> {
    return this.knowledgeSessionRecordRepository.user(id);
  }
}
