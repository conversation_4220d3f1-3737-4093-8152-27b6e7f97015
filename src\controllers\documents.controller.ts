import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import { Documents } from '../models';
import { DocumentsRepository, ActionRepository, UserRepository,DocumentCategoryRepository } from '../repositories';
import moment from 'moment';
export type docUpdate ={
  status:string,
  docId:string,
  actionId:string
}
export type docReturn ={
  status:string,
  docId:string,
  actionId:string,
  reason:string,
  from:string
}
export class DocumentsController {
  constructor(
    @repository(DocumentsRepository)
    public documentsRepository: DocumentsRepository,
    @repository(ActionRepository)
    public actionRepository: ActionRepository,
    @repository(UserRepository)
    public userRepository: UserRepository,
    @repository(DocumentCategoryRepository)
    public documentCategoryRepository: DocumentCategoryRepository,
  ) { }

  @post('/documents')
  @response(200, {
    description: 'Documents model instance',
    content: { 'application/json': { schema: getModelSchemaRef(Documents) } },
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Documents, {
            title: 'NewDocuments',
            exclude: ['id'],
          }),
        },
      },
    })
    documents: Omit<Documents, 'id'>,
  ): Promise<Documents> {

    const count = await this.documentsRepository.count();
    documents.docId = `DOC-${moment().format('YYMMDD')}-${count.count + 1}`;
    documents.createdAt = moment().utcOffset('+05:30').format('DD/MM/YYYY HH:mm')

    const doc = await this.documentsRepository.create(documents);


    const actionItem = {
      application: "DOC",
      actionType: "doc_initiated",
      status: "open",
      createdDate: doc.createdAt,
      objectId: doc.id,
      submittedById: doc.initiatorId,
      assignedToId: [doc.creatorId]
    }

    await this.actionRepository.create(actionItem)


    return doc
  }

  @get('/documents/count')
  @response(200, {
    description: 'Documents model count',
    content: { 'application/json': { schema: CountSchema } },
  })
  async count(
    @param.where(Documents) where?: Where<Documents>,
  ): Promise<Count> {
    return this.documentsRepository.count(where);
  }

  @get('/documents')
  @response(200, {
    description: 'Array of Documents model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(Documents, { includeRelations: true }),
        },
      },
    },
  })
  async find(
    @param.filter(Documents) filter?: Filter<Documents>,
  ): Promise<Documents[]> {
    const doc = await this.documentsRepository.find({
      ...filter, where: {
        docStatus:'1'
      }
    });

    for (const document of doc) {

      if (document.initiatorId) {
        document.intial = await this.userRepository.findById(document.initiatorId);
      }
      if (document.creatorId) {
        document.creator = await this.userRepository.findById(document.creatorId);
      }

      if (document.reviewerId) {
        document.reviewer = await this.userRepository.findById(document.reviewerId);
      }

      if (document.approverId) {
        document.approver = await this.userRepository.findById(document.approverId);
      }
      if (document.documentCategoryId) {
        document.cate = await this.documentCategoryRepository.findById(document.documentCategoryId);
      }

    }

    return doc


  }

  @patch('/documents')
  @response(200, {
    description: 'Documents PATCH success count',
    content: { 'application/json': { schema: CountSchema } },
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Documents, { partial: true }),
        },
      },
    })
    documents: Documents,
    @param.where(Documents) where?: Where<Documents>,
  ): Promise<Count> {
    return this.documentsRepository.updateAll(documents, where);
  }

  @get('/documents/{id}')
  @response(200, {
    description: 'Documents model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(Documents, { includeRelations: true }),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(Documents, { exclude: 'where' }) filter?: FilterExcludingWhere<Documents>
  ): Promise<Documents> {
    return this.documentsRepository.findById(id, filter);
  }

  @patch('/documents/{id}')
  @response(204, {
    description: 'Documents PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Documents, { partial: true }),
        },
      },
    })
    documents: Documents,
  ): Promise<void> {
    
  //  const doc =await this.documentsRepository.findById(id)

  //  if(doc.reviewerId !== documents.reviewerId){

  //   const actionItem = {
  //     application: "DOC",
  //     actionType: "doc_initiated",
  //     status: "open",
  //     createdDate: doc.createdAt,
  //     objectId: doc.id,
  //     submittedById: doc.creatorId,
  //     assignedToId: [documents.reviewerId]
  //   }

  //   await this.actionRepository.create(actionItem)
  // }
  // if(doc.approverId !== documents.approverId){

  //   const actionItem = {
  //     application: "DOC",
  //     actionType: "doc_initiated",
  //     status: "open",
  //     createdDate: doc.createdAt,
  //     objectId: doc.id,
  //     submittedById: doc.reviewerId,
  //     assignedToId: [documents.approverId]
  //   }

  //   await this.actionRepository.create(actionItem)
  // }



    await this.documentsRepository.updateById(id, documents);
  }

  @patch('/documents-creator-assign')
  @response(204, {
    description: 'Documents PATCH success',
  })
  async updateCreatorById(
    // @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Documents, { partial: true }),
        },
      },
    })
    documents: docUpdate,
  ): Promise<void> {
    // const action = await this.actionRepository.findOne({ where: { objectId: id } });

    const doc = await this.documentsRepository.findById(documents.docId);


    await this.actionRepository.updateById(documents.actionId, {status: "completed"})

    const actionItem = {
      application: "DOC",
      actionType: "doc_created",
      status: "open",
      createdDate: moment().utcOffset('+05:30').format('DD/MM/YYYY HH:mm'),
      objectId: doc.id,
      submittedById:doc.creatorId,
      assignedToId: [doc.reviewerId]
    }

    await this.actionRepository.create(actionItem)



    await this.documentsRepository.updateById(documents.docId, {status:documents.status});
  }
  @patch('/documents-final-approver')
  @response(204, {
    description: 'Documents PATCH success',
  })
  async updateApproverById(
    
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Documents, { partial: true }),
        },
      },
    })
    documents: docUpdate,
  ): Promise<void> {
    // const action = await this.actionRepository.findById(documents.actionId);

    // const doc = await this.documentsRepository.findById(documents.docId);
    await this.actionRepository.updateById(documents.actionId, {status: "completed"})
    await this.documentsRepository.updateById(documents.docId, {status:documents.status});
  }
  @patch('/documents-reviewer-assign')
  @response(204, {
    description: 'Documents PATCH success',
  })
  async updateReviewById(
   
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Documents, { partial: true }),
        },
      },
    })
    documents: docUpdate,
  ): Promise<void> {
    // const action = await this.actionRepository.findById(documents.actionId);
    await this.actionRepository.updateById(documents.actionId, {status: "completed"})
    const doc = await this.documentsRepository.findById(documents.docId);

    const actionItem = {
      application: "DOC",
      actionType: "doc_reviewed",
      status: "open",
      objectId: doc.id,
      createdDate: moment().utcOffset('+05:30').format('DD/MM/YYYY HH:mm'),
      submittedById: doc.reviewerId,
      assignedToId: [doc.approverId]
    }

    await this.actionRepository.create(actionItem)


    await this.documentsRepository.updateById(documents.docId, {status:documents.status});
  }

  @patch('/documents-return-assign')
  @response(204, {
    description: 'Documents PATCH success',
  })
  async updateReturnById(
   
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Documents, { partial: true }),
        },
      },
    })
    documents: docReturn,
  ): Promise<void> {
    // const action = await this.actionRepository.findById(documents.actionId);
    await this.actionRepository.updateById(documents.actionId, {status: "completed"})
    const doc = await this.documentsRepository.findById(documents.docId);

    const actionItem = {
      application: "DOC",
      actionType: "doc_returned",
      status: "open",
      objectId: doc.id,
      comments:documents.reason,
      createdDate: moment().utcOffset('+05:30').format('DD/MM/YYYY HH:mm'),
      description:documents.from,
      submittedById: documents.from ==='Reviewer' ? doc.reviewerId :doc.approverId,
      assignedToId: [doc.creatorId]
    }

    await this.actionRepository.create(actionItem)


    await this.documentsRepository.updateById(documents.docId, {status:documents.status});
  }

  @put('/documents/{id}')
  @response(204, {
    description: 'Documents PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() documents: Documents,
  ): Promise<void> {
    await this.documentsRepository.replaceById(id, documents);
  }

  @del('/documents/{id}')
  @response(204, {
    description: 'Documents DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
   await this.documentsRepository.updateById(id,{docStatus:'2'})

    // await this.documentsRepository.deleteById(id);
  }
}
