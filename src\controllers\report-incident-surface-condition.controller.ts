import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  ReportIncident,
  SurfaceCondition,
} from '../models';
import {ReportIncidentRepository} from '../repositories';

export class ReportIncidentSurfaceConditionController {
  constructor(
    @repository(ReportIncidentRepository)
    public reportIncidentRepository: ReportIncidentRepository,
  ) { }

  @get('/report-incidents/{id}/surface-condition', {
    responses: {
      '200': {
        description: 'SurfaceCondition belonging to ReportIncident',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(SurfaceCondition)},
          },
        },
      },
    },
  })
  async getSurfaceCondition(
    @param.path.string('id') id: typeof ReportIncident.prototype.id,
  ): Promise<SurfaceCondition> {
    return this.reportIncidentRepository.surfaceCondition(id);
  }
}
