import { Entity, model, property } from '@loopback/repository';

@model()
export class GoodCatchRole extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'any',
  })
  permissions?: any;

  @property({
    type: 'date',
  })
  created?: string;


  constructor(data?: Partial<GoodCatchRole>) {
    super(data);
  }
}

export interface GoodCatchRoleRelations {
  // describe navigational properties here
}

export type GoodCatchRoleWithRelations = GoodCatchRole & GoodCatchRoleRelations;
