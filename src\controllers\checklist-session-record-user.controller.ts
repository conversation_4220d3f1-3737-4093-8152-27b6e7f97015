import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  ChecklistSessionRecord,
  User,
} from '../models';
import {ChecklistSessionRecordRepository} from '../repositories';

export class ChecklistSessionRecordUserController {
  constructor(
    @repository(ChecklistSessionRecordRepository)
    public checklistSessionRecordRepository: ChecklistSessionRecordRepository,
  ) { }

  @get('/checklist-session-records/{id}/user', {
    responses: {
      '200': {
        description: 'User belonging to ChecklistSessionRecord',
        content: {
          'application/json': {
            schema: getModelSchemaRef(User),
          },
        },
      },
    },
  })
  async getUser(
    @param.path.string('id') id: typeof ChecklistSessionRecord.prototype.id,
  ): Promise<User> {
    return this.checklistSessionRecordRepository.user(id);
  }
}
