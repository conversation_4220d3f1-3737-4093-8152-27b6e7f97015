import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  AirReport,
  LocationFive,
} from '../models';
import {AirReportRepository} from '../repositories';

export class AirReportLocationFiveController {
  constructor(
    @repository(AirReportRepository)
    public airReportRepository: AirReportRepository,
  ) { }

  @get('/air-reports/{id}/location-five', {
    responses: {
      '200': {
        description: 'LocationFive belonging to AirReport',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(LocationFive)},
          },
        },
      },
    },
  })
  async getLocationFive(
    @param.path.string('id') id: typeof AirReport.prototype.id,
  ): Promise<LocationFive> {
    return this.airReportRepository.locationFive(id);
  }
}
