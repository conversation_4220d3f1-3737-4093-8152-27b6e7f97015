import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasManyRepositoryFactory} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {Area, AreaRelations, Topic} from '../models';
import {TopicRepository} from './topic.repository';

export class AreaRepository extends DefaultCrudRepository<
  Area,
  typeof Area.prototype.id,
  AreaRelations
> {

  public readonly topics: HasManyRepositoryFactory<Topic, typeof Area.prototype.id>;

  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource, @repository.getter('TopicRepository') protected topicRepositoryGetter: Getter<TopicRepository>,
  ) {
    super(Area, dataSource);
    this.topics = this.createHasManyRepositoryFactoryFor('topics', topicRepositoryGetter,);
    this.registerInclusionResolver('topics', this.topics.inclusionResolver);
  }
}
