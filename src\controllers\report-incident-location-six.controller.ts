import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  ReportIncident,
  LocationSix,
} from '../models';
import {ReportIncidentRepository} from '../repositories';

export class ReportIncidentLocationSixController {
  constructor(
    @repository(ReportIncidentRepository)
    public reportIncidentRepository: ReportIncidentRepository,
  ) { }

  @get('/report-incidents/{id}/location-six', {
    responses: {
      '200': {
        description: 'LocationSix belonging to ReportIncident',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(LocationSix)},
          },
        },
      },
    },
  })
  async getLocationSix(
    @param.path.string('id') id: typeof ReportIncident.prototype.id,
  ): Promise<LocationSix> {
    return this.reportIncidentRepository.locationSix(id);
  }
}
