import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  RaToolboxTalk,
  LocationThree,
} from '../models';
import {RaToolboxTalkRepository} from '../repositories';

export class RaToolboxTalkLocationThreeController {
  constructor(
    @repository(RaToolboxTalkRepository)
    public raToolboxTalkRepository: RaToolboxTalkRepository,
  ) { }

  @get('/ra-toolbox-talks/{id}/location-three', {
    responses: {
      '200': {
        description: 'LocationThree belonging to RaToolboxTalk',
        content: {
          'application/json': {
            schema: getModelSchemaRef(LocationThree),
          },
        },
      },
    },
  })
  async getLocationThree(
    @param.path.string('id') id: typeof RaToolboxTalk.prototype.id,
  ): Promise<LocationThree> {
    return this.raToolboxTalkRepository.locationThree(id);
  }
}
