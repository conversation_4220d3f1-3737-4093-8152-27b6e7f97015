import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
  import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
User,
GroupUser,
Groups,
} from '../models';
import {UserRepository} from '../repositories';

export class UserGroupsController {
  constructor(
    @repository(UserRepository) protected userRepository: UserRepository,
  ) { }

  @get('/users/{id}/groups', {
    responses: {
      '200': {
        description: 'Array of User has many Groups through GroupUser',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(Groups)},
          },
        },
      },
    },
  })
  async find(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<Groups>,
  ): Promise<Groups[]> {
    return this.userRepository.groups(id).find(filter);
  }

  @post('/users/{id}/groups', {
    responses: {
      '200': {
        description: 'create a Groups model instance',
        content: {'application/json': {schema: getModelSchemaRef(Groups)}},
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof User.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Groups, {
            title: 'NewGroupsInUser',
            exclude: ['id'],
          }),
        },
      },
    }) groups: Omit<Groups, 'id'>,
  ): Promise<Groups> {
    return this.userRepository.groups(id).create(groups);
  }

  @patch('/users/{id}/groups', {
    responses: {
      '200': {
        description: 'User.Groups PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Groups, {partial: true}),
        },
      },
    })
    groups: Partial<Groups>,
    @param.query.object('where', getWhereSchemaFor(Groups)) where?: Where<Groups>,
  ): Promise<Count> {
    return this.userRepository.groups(id).patch(groups, where);
  }

  @del('/users/{id}/groups', {
    responses: {
      '200': {
        description: 'User.Groups DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(Groups)) where?: Where<Groups>,
  ): Promise<Count> {
    return this.userRepository.groups(id).delete(where);
  }
}
