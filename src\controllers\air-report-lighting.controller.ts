import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  AirReport,
  Lighting,
} from '../models';
import {AirReportRepository} from '../repositories';

export class AirReportLightingController {
  constructor(
    @repository(AirReportRepository)
    public airReportRepository: AirReportRepository,
  ) { }

  @get('/air-reports/{id}/lighting', {
    responses: {
      '200': {
        description: 'Lighting belonging to AirReport',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(Lighting)},
          },
        },
      },
    },
  })
  async getLighting(
    @param.path.string('id') id: typeof AirReport.prototype.id,
  ): Promise<Lighting> {
    return this.airReportRepository.lighting(id);
  }
}
