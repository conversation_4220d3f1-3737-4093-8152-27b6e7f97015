import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {GroupDocumentAllocation, GroupDocumentAllocationRelations} from '../models';

export class GroupDocumentAllocationRepository extends DefaultCrudRepository<
  GroupDocumentAllocation,
  typeof GroupDocumentAllocation.prototype.id,
  GroupDocumentAllocationRelations
> {
  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource,
  ) {
    super(GroupDocumentAllocation, dataSource);
  }
}
