import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
  import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
User,
UserDocumentAllocation,
Documents,
} from '../models';
import {UserRepository} from '../repositories';

export class UserDocumentsController {
  constructor(
    @repository(UserRepository) protected userRepository: UserRepository,
  ) { }

  @get('/users/{id}/documents', {
    responses: {
      '200': {
        description: 'Array of User has many Documents through UserDocumentAllocation',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(Documents)},
          },
        },
      },
    },
  })
  async find(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<Documents>,
  ): Promise<Documents[]> {
    return this.userRepository.documents(id).find(filter);
  }

  @post('/users/{id}/documents', {
    responses: {
      '200': {
        description: 'create a Documents model instance',
        content: {'application/json': {schema: getModelSchemaRef(Documents)}},
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof User.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Documents, {
            title: 'NewDocumentsInUser',
            exclude: ['id'],
          }),
        },
      },
    }) documents: Omit<Documents, 'id'>,
  ): Promise<Documents> {
    return this.userRepository.documents(id).create(documents);
  }

  @patch('/users/{id}/documents', {
    responses: {
      '200': {
        description: 'User.Documents PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Documents, {partial: true}),
        },
      },
    })
    documents: Partial<Documents>,
    @param.query.object('where', getWhereSchemaFor(Documents)) where?: Where<Documents>,
  ): Promise<Count> {
    return this.userRepository.documents(id).patch(documents, where);
  }

  @del('/users/{id}/documents', {
    responses: {
      '200': {
        description: 'User.Documents DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(Documents)) where?: Where<Documents>,
  ): Promise<Count> {
    return this.userRepository.documents(id).delete(where);
  }
}
