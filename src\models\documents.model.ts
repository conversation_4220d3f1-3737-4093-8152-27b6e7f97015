import { Entity, model, property, hasMany } from '@loopback/repository';
import { User } from './user.model';
import { UserDocumentAllocation } from './user-document-allocation.model';
import { Groups } from './groups.model';
import { GroupDocumentAllocation } from './group-document-allocation.model';
import {DocumentUpdate} from './document-update.model';

@model({settings: {strict: false}})
export class Documents extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',

  })
  docId?: string;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'string',
  })
  version?: string;

  @property({
    type: 'string',
  })
  category?: string;

  @property({
    type: 'string',
  })
  uniqueid?: string;

  @property({
    type: 'string',
  })
  description?: string;

  @property({
    type: 'string',
  })
  application?: string;

  @property({
    type: 'string',
  })
  files?: string;

  @property({
    type: 'string',
  })
  createdAt?: string;

  @hasMany(() => User, {through: {model: () => UserDocumentAllocation}})
  users: User[];

  @hasMany(() => Groups, {through: {model: () => GroupDocumentAllocation}})
  groups: Groups[];


  @property({
    type: 'string',
  })
  initiatorId?: string

  @property({
    type: 'string',
  })
  creatorId?: string

  @property({
    type: 'string',
  })
  reviewerId?: string;

  @property({
    type: 'string',
  })
  approverId?: string

  @property({
    type: 'string',
  })
  status?: string

  @property({
    type: 'string',
  })
  value?: string;

  @property({
    type: 'string',
  })
  docStatus?: string;

  @hasMany(() => DocumentUpdate)
  documentUpdates: DocumentUpdate[];

  @property({
    type: 'string',
  })
  documentCategoryId?: string;
  // Define well-known properties here

  // Indexer property to allow additional data
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [prop: string]: any;

  constructor(data?: Partial<Documents>) {
    super(data);
  }
}

export interface DocumentsRelations {
  // describe navigational properties here
}

export type DocumentsWithRelations = Documents & DocumentsRelations;
