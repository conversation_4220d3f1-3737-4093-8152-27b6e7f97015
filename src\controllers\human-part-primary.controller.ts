import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {HumanPartPrimary} from '../models';
import {HumanPartPrimaryRepository} from '../repositories';

export class HumanPartPrimaryController {
  constructor(
    @repository(HumanPartPrimaryRepository)
    public humanPartPrimaryRepository : HumanPartPrimaryRepository,
  ) {}

  @post('/human-part-primaries')
  @response(200, {
    description: 'HumanPartPrimary model instance',
    content: {'application/json': {schema: getModelSchemaRef(HumanPartPrimary)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(HumanPartPrimary, {
            title: 'NewHumanPartPrimary',
            exclude: ['id'],
          }),
        },
      },
    })
    humanPartPrimary: Omit<HumanPartPrimary, 'id'>,
  ): Promise<HumanPartPrimary> {
    return this.humanPartPrimaryRepository.create(humanPartPrimary);
  }

  @get('/human-part-primaries/count')
  @response(200, {
    description: 'HumanPartPrimary model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(HumanPartPrimary) where?: Where<HumanPartPrimary>,
  ): Promise<Count> {
    return this.humanPartPrimaryRepository.count(where);
  }

  @get('/human-part-primaries')
  @response(200, {
    description: 'Array of HumanPartPrimary model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(HumanPartPrimary, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(HumanPartPrimary) filter?: Filter<HumanPartPrimary>,
  ): Promise<HumanPartPrimary[]> {
    return this.humanPartPrimaryRepository.find(filter);
  }

  @patch('/human-part-primaries')
  @response(200, {
    description: 'HumanPartPrimary PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(HumanPartPrimary, {partial: true}),
        },
      },
    })
    humanPartPrimary: HumanPartPrimary,
    @param.where(HumanPartPrimary) where?: Where<HumanPartPrimary>,
  ): Promise<Count> {
    return this.humanPartPrimaryRepository.updateAll(humanPartPrimary, where);
  }

  @get('/human-part-primaries/{id}')
  @response(200, {
    description: 'HumanPartPrimary model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(HumanPartPrimary, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(HumanPartPrimary, {exclude: 'where'}) filter?: FilterExcludingWhere<HumanPartPrimary>
  ): Promise<HumanPartPrimary> {
    return this.humanPartPrimaryRepository.findById(id, filter);
  }

  @patch('/human-part-primaries/{id}')
  @response(204, {
    description: 'HumanPartPrimary PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(HumanPartPrimary, {partial: true}),
        },
      },
    })
    humanPartPrimary: HumanPartPrimary,
  ): Promise<void> {
    await this.humanPartPrimaryRepository.updateById(id, humanPartPrimary);
  }

  @put('/human-part-primaries/{id}')
  @response(204, {
    description: 'HumanPartPrimary PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() humanPartPrimary: HumanPartPrimary,
  ): Promise<void> {
    await this.humanPartPrimaryRepository.replaceById(id, humanPartPrimary);
  }

  @del('/human-part-primaries/{id}')
  @response(204, {
    description: 'HumanPartPrimary DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.humanPartPrimaryRepository.deleteById(id);
  }
}
