import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  LocationSix,
  PermitReport,
} from '../models';
import {LocationSixRepository} from '../repositories';

export class LocationSixPermitReportController {
  constructor(
    @repository(LocationSixRepository) protected locationSixRepository: LocationSixRepository,
  ) { }

  @get('/location-sixes/{id}/permit-reports', {
    responses: {
      '200': {
        description: 'Array of LocationSix has many PermitReport',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(PermitReport)},
          },
        },
      },
    },
  })
  async find(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<PermitReport>,
  ): Promise<PermitReport[]> {
    return this.locationSixRepository.permitReports(id).find(filter);
  }

  @post('/location-sixes/{id}/permit-reports', {
    responses: {
      '200': {
        description: 'LocationSix model instance',
        content: {'application/json': {schema: getModelSchemaRef(PermitReport)}},
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof LocationSix.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(PermitReport, {
            title: 'NewPermitReportInLocationSix',
            exclude: ['id'],
            optional: ['locationSixId']
          }),
        },
      },
    }) permitReport: Omit<PermitReport, 'id'>,
  ): Promise<PermitReport> {
    return this.locationSixRepository.permitReports(id).create(permitReport);
  }

  @patch('/location-sixes/{id}/permit-reports', {
    responses: {
      '200': {
        description: 'LocationSix.PermitReport PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(PermitReport, {partial: true}),
        },
      },
    })
    permitReport: Partial<PermitReport>,
    @param.query.object('where', getWhereSchemaFor(PermitReport)) where?: Where<PermitReport>,
  ): Promise<Count> {
    return this.locationSixRepository.permitReports(id).patch(permitReport, where);
  }

  @del('/location-sixes/{id}/permit-reports', {
    responses: {
      '200': {
        description: 'LocationSix.PermitReport DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(PermitReport)) where?: Where<PermitReport>,
  ): Promise<Count> {
    return this.locationSixRepository.permitReports(id).delete(where);
  }
}
