import {Entity, model, property} from '@loopback/repository';

@model()
export class General<PERSON>ser extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  uniqueId?: string;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'string',
  })
  designation?: string;

  @property({
    type: 'string',
  })
  department?: string;

  @property({
    type: 'string',
  })
  dl?: string;

  @property({
    type: 'string',
  })
  shiftGroup?: string;

  @property({
    type: 'string',
  })
  status?: string;

  @property({
    type: 'string',
  })
  type?: string;

  @property({
    type: 'string',
  })
  created?: string;


  constructor(data?: Partial<GeneralUser>) {
    super(data);
  }
}

export interface GeneralUserRelations {
  // describe navigational properties here
}

export type GeneralUserWithRelations = GeneralUser & GeneralUserRelations;
