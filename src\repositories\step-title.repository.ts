import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {StepTitle, StepTitleRelations} from '../models';

export class StepTitleRepository extends DefaultCrudRepository<
  StepTitle,
  typeof StepTitle.prototype.id,
  StepTitleRelations
> {
  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource,
  ) {
    super(StepTitle, dataSource);
  }
}
