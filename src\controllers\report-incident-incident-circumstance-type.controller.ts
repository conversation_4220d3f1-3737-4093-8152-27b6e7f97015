import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  ReportIncident,
  IncidentCircumstanceType,
} from '../models';
import {ReportIncidentRepository} from '../repositories';

export class ReportIncidentIncidentCircumstanceTypeController {
  constructor(
    @repository(ReportIncidentRepository)
    public reportIncidentRepository: ReportIncidentRepository,
  ) { }

  @get('/report-incidents/{id}/incident-circumstance-type', {
    responses: {
      '200': {
        description: 'IncidentCircumstanceType belonging to ReportIncident',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(IncidentCircumstanceType)},
          },
        },
      },
    },
  })
  async getIncidentCircumstanceType(
    @param.path.string('id') id: typeof ReportIncident.prototype.id,
  ): Promise<IncidentCircumstanceType> {
    return this.reportIncidentRepository.incidentCircumstanceType(id);
  }
}
