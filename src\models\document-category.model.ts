import {Entity, model, property, hasMany} from '@loopback/repository';
import {Documents} from './documents.model';

@model({settings: {strict: false}})
export class DocumentCategory extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  name?: string;

  @hasMany(() => Documents)
  documents: Documents[];
  // Define well-known properties here

  // Indexer property to allow additional data
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [prop: string]: any;

  constructor(data?: Partial<DocumentCategory>) {
    super(data);
  }
}

export interface DocumentCategoryRelations {
  // describe navigational properties here
}

export type DocumentCategoryWithRelations = DocumentCategory & DocumentCategoryRelations;
