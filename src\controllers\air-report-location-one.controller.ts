import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  AirReport,
  LocationOne,
} from '../models';
import {AirReportRepository} from '../repositories';

export class AirReportLocationOneController {
  constructor(
    @repository(AirReportRepository)
    public airReportRepository: AirReportRepository,
  ) { }

  @get('/air-reports/{id}/location-one', {
    responses: {
      '200': {
        description: 'LocationOne belonging to AirReport',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(LocationOne)},
          },
        },
      },
    },
  })
  async getLocationOne(
    @param.path.string('id') id: typeof AirReport.prototype.id,
  ): Promise<LocationOne> {
    return this.airReportRepository.locationOne(id);
  }
}
