import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {TierOne} from '../models';
import {TierOneRepository} from '../repositories';

export class TierOneController {
  constructor(
    @repository(TierOneRepository)
    public tierOneRepository : TierOneRepository,
  ) {}

  @post('/tier-ones')
  @response(200, {
    description: 'TierOne model instance',
    content: {'application/json': {schema: getModelSchemaRef(TierOne)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(TierOne, {
            title: 'NewTierOne',
            exclude: ['id'],
          }),
        },
      },
    })
    tierOne: Omit<TierOne, 'id'>,
  ): Promise<TierOne> {
    return this.tierOneRepository.create(tierOne);
  }

  @get('/tier-ones/count')
  @response(200, {
    description: 'TierOne model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(TierOne) where?: Where<TierOne>,
  ): Promise<Count> {
    return this.tierOneRepository.count(where);
  }

  @get('/tier-ones')
  @response(200, {
    description: 'Array of TierOne model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(TierOne, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(TierOne) filter?: Filter<TierOne>,
  ): Promise<TierOne[]> {
    return this.tierOneRepository.find(filter);
  }

  @patch('/tier-ones')
  @response(200, {
    description: 'TierOne PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(TierOne, {partial: true}),
        },
      },
    })
    tierOne: TierOne,
    @param.where(TierOne) where?: Where<TierOne>,
  ): Promise<Count> {
    return this.tierOneRepository.updateAll(tierOne, where);
  }

  @get('/tier-ones/{id}')
  @response(200, {
    description: 'TierOne model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(TierOne, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(TierOne, {exclude: 'where'}) filter?: FilterExcludingWhere<TierOne>
  ): Promise<TierOne> {
    return this.tierOneRepository.findById(id, filter);
  }

  @patch('/tier-ones/{id}')
  @response(204, {
    description: 'TierOne PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(TierOne, {partial: true}),
        },
      },
    })
    tierOne: TierOne,
  ): Promise<void> {
    await this.tierOneRepository.updateById(id, tierOne);
  }

  @put('/tier-ones/{id}')
  @response(204, {
    description: 'TierOne PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() tierOne: TierOne,
  ): Promise<void> {
    await this.tierOneRepository.replaceById(id, tierOne);
  }

  @del('/tier-ones/{id}')
  @response(204, {
    description: 'TierOne DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.tierOneRepository.deleteById(id);
  }
}
