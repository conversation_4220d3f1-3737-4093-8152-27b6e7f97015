import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  AirReport,
  Driver,
} from '../models';
import {AirReportRepository} from '../repositories';

export class AirReportDriverController {
  constructor(
    @repository(AirReportRepository) protected airReportRepository: AirReportRepository,
  ) { }

  @get('/air-reports/{id}/drivers', {
    responses: {
      '200': {
        description: 'Array of AirReport has many Driver',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(Driver)},
          },
        },
      },
    },
  })
  async find(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<Driver>,
  ): Promise<Driver[]> {
    return this.airReportRepository.drivers(id).find(filter);
  }

  @post('/air-reports/{id}/drivers', {
    responses: {
      '200': {
        description: 'AirReport model instance',
        content: {'application/json': {schema: getModelSchemaRef(Driver)}},
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof AirReport.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Driver, {
            title: 'NewDriverInAirReport',
            exclude: ['id'],
            optional: ['airReportId']
          }),
        },
      },
    }) driver: Omit<Driver, 'id'>,
  ): Promise<Driver> {
    return this.airReportRepository.drivers(id).create(driver);
  }

  @patch('/air-reports/{id}/drivers', {
    responses: {
      '200': {
        description: 'AirReport.Driver PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Driver, {partial: true}),
        },
      },
    })
    driver: Partial<Driver>,
    @param.query.object('where', getWhereSchemaFor(Driver)) where?: Where<Driver>,
  ): Promise<Count> {
    return this.airReportRepository.drivers(id).patch(driver, where);
  }

  @del('/air-reports/{id}/drivers', {
    responses: {
      '200': {
        description: 'AirReport.Driver DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(Driver)) where?: Where<Driver>,
  ): Promise<Count> {
    return this.airReportRepository.drivers(id).delete(where);
  }
}
