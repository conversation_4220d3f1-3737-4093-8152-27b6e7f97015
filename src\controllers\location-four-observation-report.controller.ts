import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  LocationFour,
  ObservationReport,
} from '../models';
import {LocationFourRepository} from '../repositories';

export class LocationFourObservationReportController {
  constructor(
    @repository(LocationFourRepository) protected locationFourRepository: LocationFourRepository,
  ) { }

  @get('/location-fours/{id}/observation-reports', {
    responses: {
      '200': {
        description: 'Array of LocationFour has many ObservationReport',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(ObservationReport)},
          },
        },
      },
    },
  })
  async find(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<ObservationReport>,
  ): Promise<ObservationReport[]> {
    return this.locationFourRepository.observationReports(id).find(filter);
  }

  @post('/location-fours/{id}/observation-reports', {
    responses: {
      '200': {
        description: 'LocationFour model instance',
        content: {'application/json': {schema: getModelSchemaRef(ObservationReport)}},
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof LocationFour.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ObservationReport, {
            title: 'NewObservationReportInLocationFour',
            exclude: ['id'],
            optional: ['locationFourId']
          }),
        },
      },
    }) observationReport: Omit<ObservationReport, 'id'>,
  ): Promise<ObservationReport> {
    return this.locationFourRepository.observationReports(id).create(observationReport);
  }

  @patch('/location-fours/{id}/observation-reports', {
    responses: {
      '200': {
        description: 'LocationFour.ObservationReport PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ObservationReport, {partial: true}),
        },
      },
    })
    observationReport: Partial<ObservationReport>,
    @param.query.object('where', getWhereSchemaFor(ObservationReport)) where?: Where<ObservationReport>,
  ): Promise<Count> {
    return this.locationFourRepository.observationReports(id).patch(observationReport, where);
  }

  @del('/location-fours/{id}/observation-reports', {
    responses: {
      '200': {
        description: 'LocationFour.ObservationReport DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(ObservationReport)) where?: Where<ObservationReport>,
  ): Promise<Count> {
    return this.locationFourRepository.observationReports(id).delete(where);
  }
}
