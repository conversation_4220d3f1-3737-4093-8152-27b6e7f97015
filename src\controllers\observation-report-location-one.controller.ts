import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  ObservationReport,
  LocationOne,
} from '../models';
import {ObservationReportRepository} from '../repositories';

export class ObservationReportLocationOneController {
  constructor(
    @repository(ObservationReportRepository)
    public observationReportRepository: ObservationReportRepository,
  ) { }

  @get('/observation-reports/{id}/location-one', {
    responses: {
      '200': {
        description: 'LocationOne belonging to ObservationReport',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(LocationOne)},
          },
        },
      },
    },
  })
  async getLocationOne(
    @param.path.string('id') id: typeof ObservationReport.prototype.id,
  ): Promise<LocationOne> {
    return this.observationReportRepository.locationOne(id);
  }
}
