import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  AirReport,
  WorkingGroup,
} from '../models';
import {AirReportRepository} from '../repositories';

export class AirReportWorkingGroupController {
  constructor(
    @repository(AirReportRepository)
    public airReportRepository: AirReportRepository,
  ) { }

  @get('/air-reports/{id}/working-group', {
    responses: {
      '200': {
        description: 'WorkingGroup belonging to AirReport',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(WorkingGroup)},
          },
        },
      },
    },
  })
  async getWorkingGroup(
    @param.path.string('id') id: typeof AirReport.prototype.id,
  ): Promise<WorkingGroup> {
    return this.airReportRepository.workingGroup(id);
  }
}
