import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  ReportIncident,
  IncidentUnderlyingCauseType,
} from '../models';
import {ReportIncidentRepository} from '../repositories';

export class ReportIncidentIncidentUnderlyingCauseTypeController {
  constructor(
    @repository(ReportIncidentRepository)
    public reportIncidentRepository: ReportIncidentRepository,
  ) { }

  @get('/report-incidents/{id}/incident-underlying-cause-type', {
    responses: {
      '200': {
        description: 'IncidentUnderlyingCauseType belonging to ReportIncident',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(IncidentUnderlyingCauseType)},
          },
        },
      },
    },
  })
  async getIncidentUnderlyingCauseType(
    @param.path.string('id') id: typeof ReportIncident.prototype.id,
  ): Promise<IncidentUnderlyingCauseType> {
    return this.reportIncidentRepository.incidentUnderlyingCauseType(id);
  }
}
