import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, BelongsToAccessor} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {UserLocationRole, UserLocationRoleRelations, User, LocationOne, LocationTwo, LocationThree, LocationFour} from '../models';
import {UserRepository} from './user.repository';
import {LocationOneRepository} from './location-one.repository';
import {LocationTwoRepository} from './location-two.repository';
import {LocationThreeRepository} from './location-three.repository';
import {LocationFourRepository} from './location-four.repository';

export class UserLocationRoleRepository extends DefaultCrudRepository<
  UserLocationRole,
  typeof UserLocationRole.prototype.id,
  UserLocationRoleRelations
> {

  public readonly user: BelongsToAccessor<User, typeof UserLocationRole.prototype.id>;

  public readonly locationOne: BelongsToAccessor<LocationOne, typeof UserLocationRole.prototype.id>;

  public readonly locationTwo: BelongsToAccessor<LocationTwo, typeof UserLocationRole.prototype.id>;

  public readonly locationThree: BelongsToAccessor<LocationThree, typeof UserLocationRole.prototype.id>;

  public readonly locationFour: BelongsToAccessor<LocationFour, typeof UserLocationRole.prototype.id>;

  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource, @repository.getter('UserRepository') protected userRepositoryGetter: Getter<UserRepository>, @repository.getter('LocationOneRepository') protected locationOneRepositoryGetter: Getter<LocationOneRepository>, @repository.getter('LocationTwoRepository') protected locationTwoRepositoryGetter: Getter<LocationTwoRepository>, @repository.getter('LocationThreeRepository') protected locationThreeRepositoryGetter: Getter<LocationThreeRepository>, @repository.getter('LocationFourRepository') protected locationFourRepositoryGetter: Getter<LocationFourRepository>,
  ) {
    super(UserLocationRole, dataSource);
    this.locationFour = this.createBelongsToAccessorFor('locationFour', locationFourRepositoryGetter,);
    this.registerInclusionResolver('locationFour', this.locationFour.inclusionResolver);
    this.locationThree = this.createBelongsToAccessorFor('locationThree', locationThreeRepositoryGetter,);
    this.registerInclusionResolver('locationThree', this.locationThree.inclusionResolver);
    this.locationTwo = this.createBelongsToAccessorFor('locationTwo', locationTwoRepositoryGetter,);
    this.registerInclusionResolver('locationTwo', this.locationTwo.inclusionResolver);
    this.locationOne = this.createBelongsToAccessorFor('locationOne', locationOneRepositoryGetter,);
    this.registerInclusionResolver('locationOne', this.locationOne.inclusionResolver);
    this.user = this.createBelongsToAccessorFor('user', userRepositoryGetter,);
    this.registerInclusionResolver('user', this.user.inclusionResolver);
  }
}
