import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  GhsOne,
  ObservationReport,
} from '../models';
import {GhsOneRepository} from '../repositories';

export class GhsOneObservationReportController {
  constructor(
    @repository(GhsOneRepository) protected ghsOneRepository: GhsOneRepository,
  ) { }

  @get('/ghs-ones/{id}/observation-reports', {
    responses: {
      '200': {
        description: 'Array of GhsOne has many ObservationReport',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(ObservationReport)},
          },
        },
      },
    },
  })
  async find(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<ObservationReport>,
  ): Promise<ObservationReport[]> {
    return this.ghsOneRepository.observationReports(id).find(filter);
  }

  @post('/ghs-ones/{id}/observation-reports', {
    responses: {
      '200': {
        description: 'GhsOne model instance',
        content: {'application/json': {schema: getModelSchemaRef(ObservationReport)}},
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof GhsOne.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ObservationReport, {
            title: 'NewObservationReportInGhsOne',
            exclude: ['id'],
            optional: ['ghsOneId']
          }),
        },
      },
    }) observationReport: Omit<ObservationReport, 'id'>,
  ): Promise<ObservationReport> {
    return this.ghsOneRepository.observationReports(id).create(observationReport);
  }

  @patch('/ghs-ones/{id}/observation-reports', {
    responses: {
      '200': {
        description: 'GhsOne.ObservationReport PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ObservationReport, {partial: true}),
        },
      },
    })
    observationReport: Partial<ObservationReport>,
    @param.query.object('where', getWhereSchemaFor(ObservationReport)) where?: Where<ObservationReport>,
  ): Promise<Count> {
    return this.ghsOneRepository.observationReports(id).patch(observationReport, where);
  }

  @del('/ghs-ones/{id}/observation-reports', {
    responses: {
      '200': {
        description: 'GhsOne.ObservationReport DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(ObservationReport)) where?: Where<ObservationReport>,
  ): Promise<Count> {
    return this.ghsOneRepository.observationReports(id).delete(where);
  }
}
