import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {TierTwo} from '../models';
import {TierTwoRepository} from '../repositories';

export class TierTwoController {
  constructor(
    @repository(TierTwoRepository)
    public tierTwoRepository : TierTwoRepository,
  ) {}

  @post('/tier-twos')
  @response(200, {
    description: 'TierTwo model instance',
    content: {'application/json': {schema: getModelSchemaRef(TierTwo)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(TierTwo, {
            title: 'NewTierTwo',
            exclude: ['id'],
          }),
        },
      },
    })
    tierTwo: Omit<TierTwo, 'id'>,
  ): Promise<TierTwo> {
    return this.tierTwoRepository.create(tierTwo);
  }

  @get('/tier-twos/count')
  @response(200, {
    description: 'TierTwo model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(TierTwo) where?: Where<TierTwo>,
  ): Promise<Count> {
    return this.tierTwoRepository.count(where);
  }

  @get('/tier-twos')
  @response(200, {
    description: 'Array of TierTwo model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(TierTwo, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(TierTwo) filter?: Filter<TierTwo>,
  ): Promise<TierTwo[]> {
    return this.tierTwoRepository.find(filter);
  }

  @patch('/tier-twos')
  @response(200, {
    description: 'TierTwo PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(TierTwo, {partial: true}),
        },
      },
    })
    tierTwo: TierTwo,
    @param.where(TierTwo) where?: Where<TierTwo>,
  ): Promise<Count> {
    return this.tierTwoRepository.updateAll(tierTwo, where);
  }

  @get('/tier-twos/{id}')
  @response(200, {
    description: 'TierTwo model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(TierTwo, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(TierTwo, {exclude: 'where'}) filter?: FilterExcludingWhere<TierTwo>
  ): Promise<TierTwo> {
    return this.tierTwoRepository.findById(id, filter);
  }

  @patch('/tier-twos/{id}')
  @response(204, {
    description: 'TierTwo PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(TierTwo, {partial: true}),
        },
      },
    })
    tierTwo: TierTwo,
  ): Promise<void> {
    await this.tierTwoRepository.updateById(id, tierTwo);
  }

  @put('/tier-twos/{id}')
  @response(204, {
    description: 'TierTwo PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() tierTwo: TierTwo,
  ): Promise<void> {
    await this.tierTwoRepository.replaceById(id, tierTwo);
  }

  @del('/tier-twos/{id}')
  @response(204, {
    description: 'TierTwo DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.tierTwoRepository.deleteById(id);
  }
}
