import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  AirReport,
  SurfaceCondition,
} from '../models';
import {AirReportRepository} from '../repositories';

export class AirReportSurfaceConditionController {
  constructor(
    @repository(AirReportRepository)
    public airReportRepository: AirReportRepository,
  ) { }

  @get('/air-reports/{id}/surface-condition', {
    responses: {
      '200': {
        description: 'SurfaceCondition belonging to AirReport',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(SurfaceCondition)},
          },
        },
      },
    },
  })
  async getSurfaceCondition(
    @param.path.string('id') id: typeof AirReport.prototype.id,
  ): Promise<SurfaceCondition> {
    return this.airReportRepository.surfaceCondition(id);
  }
}
