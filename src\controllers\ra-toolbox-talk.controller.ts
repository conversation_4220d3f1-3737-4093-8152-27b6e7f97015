import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import { OPERATION_SECURITY_SPEC } from '@loopback/authentication-jwt';
import { SecurityBindings, UserProfile, securityId } from '@loopback/security';
import { RaToolboxTalk, User } from '../models';
import { RaToolboxTalkRepository, UserRepository } from '../repositories';
import { inject } from '@loopback/core';
import { authenticate } from '@loopback/authentication';
@authenticate('jwt')
export class RaToolboxTalkController {
  constructor(
    @repository(RaToolboxTalkRepository)
    public raToolboxTalkRepository: RaToolboxTalkRepository,
    @repository(UserRepository)
    public userRepository: UserRepository,
  ) { }

  @post('/ra-toolbox-talks')
  @response(200, {
    security: OPERATION_SECURITY_SPEC,
    description: 'RaToolboxTalk model instance',
    content: { 'application/json': { schema: getModelSchemaRef(RaToolboxTalk) } },
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(RaToolboxTalk, {
            title: 'NewRaToolboxTalk',
            exclude: ['id'],
          }),
        },
      },
    })
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    raToolboxTalk: Omit<RaToolboxTalk, 'id'>,
  ): Promise<RaToolboxTalk> {
    const email = '<EMAIL>'
    const user: User | null = await this.userRepository.findOne({ where: { email: email } })
    if (user !== null) {
      raToolboxTalk.userId = user.id;
    } else {
      raToolboxTalk.userId = '';
      // Handle the case when user is null
    }



    return this.raToolboxTalkRepository.create(raToolboxTalk);
  }

  @get('/ra-toolbox-talks/count')
  @response(200, {
    description: 'RaToolboxTalk model count',
    content: { 'application/json': { schema: CountSchema } },
  })
  async count(
    @param.where(RaToolboxTalk) where?: Where<RaToolboxTalk>,
  ): Promise<Count> {
    return this.raToolboxTalkRepository.count(where);
  }

  @get('/ra-toolbox-talks')
  @response(200, {
    description: 'Array of RaToolboxTalk model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(RaToolboxTalk, { includeRelations: true }),
        },
      },
    },
  })
  async find(
    @param.filter(RaToolboxTalk) filter?: Filter<RaToolboxTalk>,
  ): Promise<RaToolboxTalk[]> {
    return this.raToolboxTalkRepository.find(filter);
  }

  @get('/ra-toolbox-talks-mobile')
  @response(200, {
    description: 'Array of RaToolboxTalk model instances',
    security: OPERATION_SECURITY_SPEC,
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(RaToolboxTalk, { includeRelations: true }),
        },
      },
    },
  })
  async findmobile(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.filter(RaToolboxTalk) filter?: Filter<RaToolboxTalk>,
  ): Promise<RaToolboxTalk[]> {

    const email = '<EMAIL>'
    const user: User | null = await this.userRepository.findOne({ where: { email: email } })
    let userId =''
    if (user !== null) {
      userId = user.id;
    } else {
      userId = '';
      // Handle the case when user is null
    }
  
    return this.raToolboxTalkRepository.find({where:{userId:userId},...filter});
  }

  @patch('/ra-toolbox-talks')
  @response(200, {
    description: 'RaToolboxTalk PATCH success count',
    content: { 'application/json': { schema: CountSchema } },
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(RaToolboxTalk, { partial: true }),
        },
      },
    })
    raToolboxTalk: RaToolboxTalk,
    @param.where(RaToolboxTalk) where?: Where<RaToolboxTalk>,
  ): Promise<Count> {
    return this.raToolboxTalkRepository.updateAll(raToolboxTalk, where);
  }

  @get('/ra-toolbox-talks/{id}')
  @response(200, {
    description: 'RaToolboxTalk model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(RaToolboxTalk, { includeRelations: true }),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(RaToolboxTalk, { exclude: 'where' }) filter?: FilterExcludingWhere<RaToolboxTalk>
  ): Promise<RaToolboxTalk> {
    return this.raToolboxTalkRepository.findById(id, filter);
  }

  @patch('/ra-toolbox-talks/{id}')
  @response(204, {
    description: 'RaToolboxTalk PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(RaToolboxTalk, { partial: true }),
        },
      },
    })
    raToolboxTalk: RaToolboxTalk,
  ): Promise<void> {
    await this.raToolboxTalkRepository.updateById(id, raToolboxTalk);
  }

  @put('/ra-toolbox-talks/{id}')
  @response(204, {
    description: 'RaToolboxTalk PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() raToolboxTalk: RaToolboxTalk,
  ): Promise<void> {
    await this.raToolboxTalkRepository.replaceById(id, raToolboxTalk);
  }

  @del('/ra-toolbox-talks/{id}')
  @response(204, {
    description: 'RaToolboxTalk DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.raToolboxTalkRepository.deleteById(id);
  }
}
