import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {ContractorRole, ContractorRoleRelations} from '../models';

export class ContractorRoleRepository extends DefaultCrudRepository<
  ContractorRole,
  typeof ContractorRole.prototype.id,
  ContractorRoleRelations
> {
  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource,
  ) {
    super(ContractorRole, dataSource);
  }
}
