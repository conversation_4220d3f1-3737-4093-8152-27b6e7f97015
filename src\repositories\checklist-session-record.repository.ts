import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, BelongsToAccessor} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {ChecklistSessionRecord, ChecklistSessionRecordRelations, Checklist, User} from '../models';
import {ChecklistRepository} from './checklist.repository';
import {UserRepository} from './user.repository';

export class ChecklistSessionRecordRepository extends DefaultCrudRepository<
  ChecklistSessionRecord,
  typeof ChecklistSessionRecord.prototype.id,
  ChecklistSessionRecordRelations
> {

  public readonly checklist: BelongsToAccessor<Checklist, typeof ChecklistSessionRecord.prototype.id>;

  public readonly user: BelongsToAccessor<User, typeof ChecklistSessionRecord.prototype.id>;

  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource, @repository.getter('ChecklistRepository') protected checklistRepositoryGetter: Getter<ChecklistRepository>, @repository.getter('UserRepository') protected userRepositoryGetter: Getter<UserRepository>,
  ) {
    super(ChecklistSessionRecord, dataSource);
    this.user = this.createBelongsToAccessorFor('user', userRepositoryGetter,);
    this.registerInclusionResolver('user', this.user.inclusionResolver);
    this.checklist = this.createBelongsToAccessorFor('checklist', checklistRepositoryGetter,);
    this.registerInclusionResolver('checklist', this.checklist.inclusionResolver);
  }
}
