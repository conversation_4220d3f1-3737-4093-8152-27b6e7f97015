import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {GroupUser} from '../models';
import {GroupUserRepository,UserUnitAllocationRepository,GroupUnitRepository,UserChecklistAllocationRepository,UserDocumentAllocationRepository,GroupChecklistAllocationRepository,GroupDocumentAllocationRepository} from '../repositories';

export type UserID = {
  groupsId: string,
  userId: [],
  

};
export class GroupUserController {
  constructor(
    @repository(GroupUserRepository)
    public groupUserRepository : GroupUserRepository,
    @repository(UserUnitAllocationRepository)
    public userUnitAllocationRepository :UserUnitAllocationRepository,
    @repository(UserChecklistAllocationRepository)
    public userChecklistAllocationRepository :UserChecklistAllocationRepository,
    @repository(UserDocumentAllocationRepository)
    public userDocumentAllocationRepository :UserDocumentAllocationRepository,
    @repository(GroupUnitRepository)
    public groupUnitRepository :GroupUnitRepository,
    @repository(GroupChecklistAllocationRepository)
    public groupChecklistRepository :GroupChecklistAllocationRepository,
    @repository(GroupDocumentAllocationRepository)
    public groupDocumentRepository :GroupDocumentAllocationRepository
  ) {}

  @post('/group-users')
  @response(200, {
    description: 'GroupUser model instance',
    content: {'application/json': {schema: getModelSchemaRef(GroupUser)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(GroupUser, {
            title: 'NewGroupUser',
            exclude: ['id'],
          }),
        },
      },
    })
    groupUser: Omit<GroupUser, 'id'>,
  ): Promise<GroupUser> {
    return this.groupUserRepository.create(groupUser);
  }

  @get('/group-users/count')
  @response(200, {
    description: 'GroupUser model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(GroupUser) where?: Where<GroupUser>,
  ): Promise<Count> {
    return this.groupUserRepository.count(where);
  }

  @get('/group-users')
  @response(200, {
    description: 'Array of GroupUser model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(GroupUser, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(GroupUser) filter?: Filter<GroupUser>,
  ): Promise<GroupUser[]> {
    return this.groupUserRepository.find(filter);
  }

  @patch('/group-users')
  @response(200, {
    description: 'GroupUser PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(GroupUser, {partial: true}),
        },
      },
    })
    groupUser: GroupUser,
    @param.where(GroupUser) where?: Where<GroupUser>,
  ): Promise<Count> {
    return this.groupUserRepository.updateAll(groupUser, where);
  }

  @get('/group-users/{id}')
  @response(200, {
    description: 'GroupUser model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(GroupUser, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(GroupUser, {exclude: 'where'}) filter?: FilterExcludingWhere<GroupUser>
  ): Promise<GroupUser> {
    return this.groupUserRepository.findById(id, filter);
  }

  @patch('/group-users/{id}')
  @response(204, {
    description: 'GroupUser PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(GroupUser, {partial: true}),
        },
      },
    })
    groupUser: GroupUser,
  ): Promise<void> {
    await this.groupUserRepository.updateById(id, groupUser);
  }

  @put('/group-users/{id}')
  @response(204, {
    description: 'GroupUser PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() groupUser: GroupUser,
  ): Promise<void> {
    await this.groupUserRepository.replaceById(id, groupUser);
  }

  @del('/group-users/{id}')
  @response(204, {
    description: 'GroupUser DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.groupUserRepository.deleteById(id);
  }
  @post('/group-users-add')
  @response(200, {
    description: 'UserTierThree model instance'
  })

  async groupuseradd(
    @requestBody()
    user: UserID,
  ): Promise<void> {
    if (user.userId.length === 0) {
      // return this.groupUserRepository.create(groupUser);
      await this.groupUserRepository.deleteAll({
        and: [

          {groupsId: user.groupsId},
        ],

      })
      await this.userUnitAllocationRepository.deleteAll({
        and: [
          {groupId: user.groupsId},
          {type: 'group'}
        ],
      });
      await this.userChecklistAllocationRepository.deleteAll({
        and: [
          {groupId: user.groupsId},
          {type: 'group'}
        ],
      })
      await this.userDocumentAllocationRepository.deleteAll({
        and: [
          {groupId: user.groupsId},
          {type: 'group'}
        ],
      })
    }
    else {
      const userOut: (string | undefined)[] = user.userId;
      const alreadyuser = await this.groupUserRepository.find({
        where: {
          groupsId: user.groupsId
        },
      });

      const userIn: (string | undefined)[] = alreadyuser.map(item => {
        return item.userId;
      });




      const insertA = userOut.filter(item => !userIn.includes(item));


      if (insertA.length !== 0) {
        insertA.map(async i => {
          await this.groupUserRepository.create({"userId": i, "groupsId": user.groupsId});

        })


      }

      const deleteA = userIn.filter(item => !userOut.includes(item))
      if (deleteA.length !== 0) {
        deleteA.map(async i => {
          await this.groupUserRepository.deleteAll({
            and: [
            
              {groupsId: user.groupsId},
              {userId: i}
            ],
          });

        })

      }


      //Group user assign


      userOut.map(async id => {
//Unit Removing ...
        const alreadyKu = await this.userUnitAllocationRepository.find({
          where: {
            userId: id
          }
        })
        const kaUserIn = alreadyKu.map(k => {
          return k.unitId
        })
        const convertedInt: (string | undefined)[] = kaUserIn.map(num => String(num));

        const raid = await this.groupUnitRepository.find({
          where: {
            groupsId: user.groupsId
          },
        });
        const kaId: (string | undefined)[] = raid.map(item => {
          return item.unitId;
        });

        const convertedKaIn: (string | undefined)[] = kaId.map(num => String(num));
        const insertKaUser = convertedKaIn.filter(it => !convertedInt.includes(it));

        if (insertKaUser.length !== 0) {
          insertKaUser.map(async k => {
            await this.userUnitAllocationRepository.create({"groupId": user.groupsId, "userId": id, "unitId": k, "type": 'group'})
          })

        }

      

        const recordsToDelete = await this.userUnitAllocationRepository.find({
          where: {
           
            groupId: user.groupsId,
            type: 'group',
            userId: {nin: userOut}

          }
        });
        const idsToDelete = recordsToDelete.map(record => record.id);

        // Perform the delete operation
        await this.userUnitAllocationRepository.deleteAll({id: {inq: idsToDelete}});
        

      //Checklist Removing....


      const alreadyKu1 = await this.userChecklistAllocationRepository.find({
        where: {
          userId: id
        }
      })
      const kaUserIn1 = alreadyKu1.map(k => {
        return k.checklistId
      })
      const convertedInt1: (string | undefined)[] = kaUserIn1.map(num => String(num));

      const raid1 = await this.groupChecklistRepository.find({
        where: {
          groupsId: user.groupsId
        },
      });
      const kaId1: (string | undefined)[] = raid1.map(item => {
        return item.checklistId;
      });

      const convertedKaIn1: (string | undefined)[] = kaId1.map(num => String(num));
      const insertKaUser1 = convertedKaIn1.filter(it => !convertedInt1.includes(it));

      if (insertKaUser.length !== 0) {
        insertKaUser.map(async k => {
          await this.userChecklistAllocationRepository.create({"groupId": user.groupsId, "userId": id, "checklistId": k, "type": 'group'})
        })

      }

    

      const recordsToDelete1 = await this.userChecklistAllocationRepository.find({
        where: {
         
          groupId: user.groupsId,
          type: 'group',
          userId: {nin: userOut}

        }
      });
      const idsToDelete1 = recordsToDelete1.map(record => record.id);

      // Perform the delete operation
      await this.userChecklistAllocationRepository.deleteAll({id: {inq: idsToDelete1}});

     //Document removing

     
     const alreadyKu12 = await this.userDocumentAllocationRepository.find({
      where: {
        userId: id
      }
    })
    const kaUserIn12 = alreadyKu12.map(k => {
      return k.documentsId
    })
    const convertedInt12: (string | undefined)[] = kaUserIn12.map(num => String(num));

    const raid12 = await this.groupDocumentRepository.find({
      where: {
        groupsId: user.groupsId
      },
    });
    const kaId12: (string | undefined)[] = raid12.map(item => {
      return item.documentsId;
    });

    const convertedKaIn12: (string | undefined)[] = kaId12.map(num => String(num));
    const insertKaUser12 = convertedKaIn12.filter(it => !convertedInt12.includes(it));

    if (insertKaUser.length !== 0) {
      insertKaUser.map(async k => {
        await this.userDocumentAllocationRepository.create({"groupId": user.groupsId, "userId": id, "documentsId": k, "type": 'group'})
      })

    }

  

    const recordsToDelete12 = await this.userDocumentAllocationRepository.find({
      where: {
       
        groupId: user.groupsId,
        type: 'group',
        userId: {nin: userOut}

      }
    });
    const idsToDelete12 = recordsToDelete12.map(record => record.id);

    // Perform the delete operation
    await this.userDocumentAllocationRepository.deleteAll({id: {inq: idsToDelete12}});




      })




    }
  }
  @get('/group-user-group/{id}')
  @response(200, {
    description: 'GroupUser model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(GroupUser, {includeRelations: true}),
      },
    },
  })
  async groupusergroup(
    @param.path.string('id') id: string,
  ): Promise<GroupUser[]> {

    const data = await this.groupUserRepository.find({
      where: {
        groupsId: id
      }
    });

    return data;
  }

}
