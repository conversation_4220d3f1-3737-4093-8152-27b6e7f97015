import { inject } from '@loopback/context';
import { Request, Response, RestBindings, post, requestBody, HttpErrors } from '@loopback/rest';
import multer from 'multer';
import AWS from 'aws-sdk';
import { authenticate } from '@loopback/authentication';
import { SecurityBindings, securityId, UserProfile } from '@loopback/security';

// ... AWS configuration and multer setup ...
AWS.config.update({
  accessKeyId: process.env.AWS_S3_ACCESS_KEY_ID,
  secretAccessKey: process.env.AWS_S3_SECRET_ACCESS_KEY,
  region: process.env.AWS_REGION,
});

const s3 = new AWS.S3();
const storage = multer.memoryStorage();
const upload = multer({ storage: storage });

@authenticate('jwt')
export class S3UploadController {
  constructor() { }

  @post('/upload-pdf', {
    responses: {
      '200': {
        description: 'PDF Upload',
        content: { 'application/json': { schema: { type: 'object' } } },
      },
    },
  })
  async uploadPdf(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @inject(RestBindings.Http.REQUEST) request: Request,
    @inject(RestBindings.Http.RESPONSE) response: Response,
  ): Promise<object> {
    return new Promise<object>((resolve, reject) => {
      upload.single('file')(request, response, async (err) => {
        if (err) return reject(err);

        if (!request.file || !request.file.buffer) {
          throw new HttpErrors.BadRequest('No file uploaded');
        }

        const pdfBlob = request.file.buffer;
        const fileName = request.body.filename; // Extract filename from the request body

        const bucketName = `${process.env.AWS_S3_BUCKET_NAME}`;

        try {
          const params = {
            Bucket: bucketName,
            Key: fileName,
            Body: pdfBlob,
            ContentType: 'application/pdf',
          };

          const uploadResult = await s3.upload(params).promise();
          resolve({ url: uploadResult.Location });
        } catch (error) {
          reject(new HttpErrors.InternalServerError('Error uploading file to S3'));
        }
      });
    });
  }
}
