import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasManyRepositoryFactory} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {TierOne, TierOneRelations, TierTwo} from '../models';
import {TierTwoRepository} from './tier-two.repository';

export class TierOneRepository extends DefaultCrudRepository<
  TierOne,
  typeof TierOne.prototype.id,
  TierOneRelations
> {

  public readonly tierTwos: HasManyRepositoryFactory<TierTwo, typeof TierOne.prototype.id>;

  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource, @repository.getter('TierTwoRepository') protected tierTwoRepositoryGetter: Getter<TierTwoRepository>,
  ) {
    super(TierOne, dataSource);
    this.tierTwos = this.createHasManyRepositoryFactoryFor('tierTwos', tierTwoRepositoryGetter,);
    this.registerInclusionResolver('tierTwos', this.tierTwos.inclusionResolver);
  }
}
