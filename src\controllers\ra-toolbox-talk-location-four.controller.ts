import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  RaToolboxTalk,
  LocationFour,
} from '../models';
import {RaToolboxTalkRepository} from '../repositories';

export class RaToolboxTalkLocationFourController {
  constructor(
    @repository(RaToolboxTalkRepository)
    public raToolboxTalkRepository: RaToolboxTalkRepository,
  ) { }

  @get('/ra-toolbox-talks/{id}/location-four', {
    responses: {
      '200': {
        description: 'LocationFour belonging to RaToolboxTalk',
        content: {
          'application/json': {
            schema: getModelSchemaRef(LocationFour),
          },
        },
      },
    },
  })
  async getLocationFour(
    @param.path.string('id') id: typeof RaToolboxTalk.prototype.id,
  ): Promise<LocationFour> {
    return this.raToolboxTalkRepository.locationFour(id);
  }
}
