import {Entity, model, property} from '@loopback/repository';

@model({settings: {strict: false}})
export class RiskUpdate extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  date?: string;

  @property({
    type: 'string',
  })
  reasonreview?: string;

  @property({
    type: 'string',
  })
  changes?: string;

  @property({
    type: 'string',
  })
  reasonchange?: string;

  @property({
    type: 'string',
  })
  initiatedBy?: string;

  @property({
    type: 'string',
  })
  approvedBy?: string;

  @property({
    type: 'string',
  })
  reference?: string;

  @property({
    type:'array',
    itemType: 'any',
  })
  attachment?: any[];

  @property({
    type: 'string',
  })
  sign?: string;

  @property({
    type: 'string',
  })
  riskAssessmentId?: string;
  // Define well-known properties here

  // Indexer property to allow additional data
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [prop: string]: any;

  constructor(data?: Partial<RiskUpdate>) {
    super(data);
  }
}

export interface RiskUpdateRelations {
  // describe navigational properties here
}

export type RiskUpdateWithRelations = RiskUpdate & RiskUpdateRelations;
