import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {GroupUnit, GroupUnitRelations} from '../models';

export class GroupUnitRepository extends DefaultCrudRepository<
  GroupUnit,
  typeof GroupUnit.prototype.id,
  GroupUnitRelations
> {
  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource,
  ) {
    super(GroupUnit, dataSource);
  }
}
