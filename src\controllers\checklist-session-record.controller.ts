import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import { ChecklistSessionRecord, User } from '../models';
import { ChecklistSessionRecordRepository, UserRepository, ChecklistRepository } from '../repositories';
import { authenticate } from '@loopback/authentication';
import { OPERATION_SECURITY_SPEC } from '@loopback/authentication-jwt';
import { SecurityBindings, UserProfile, securityId } from '@loopback/security';
import { inject } from '@loopback/core';
import { UUIDGeneratorService } from '../services';
type ChecklistSessionStart = {
  checklistId: string,

}
type ChecklistSessionEnd = {
  sessionId: string,
  stepsData: string,

}
export class ChecklistSessionRecordController {
  constructor(
    @repository(ChecklistSessionRecordRepository)
    public checklistSessionRecordRepository: ChecklistSessionRecordRepository,
    @repository(ChecklistRepository)
    public checklistRepository: ChecklistRepository,
    @inject('services.UUIDGeneratorService')
    protected uuidGeneratorService: UUIDGeneratorService,
    @repository(UserRepository)
    public userRepository: UserRepository,
  ) { }

  @post('/checklist-session-records')
  @response(200, {
    description: 'ChecklistSessionRecord model instance',
    content: { 'application/json': { schema: getModelSchemaRef(ChecklistSessionRecord) } },
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ChecklistSessionRecord, {
            title: 'NewChecklistSessionRecord',
            exclude: ['id'],
          }),
        },
      },
    })
    checklistSessionRecord: Omit<ChecklistSessionRecord, 'id'>,
  ): Promise<ChecklistSessionRecord> {
    return this.checklistSessionRecordRepository.create(checklistSessionRecord);
  }

  @get('/checklist-session-records/count')
  @response(200, {
    description: 'ChecklistSessionRecord model count',
    content: { 'application/json': { schema: CountSchema } },
  })
  async count(
    @param.where(ChecklistSessionRecord) where?: Where<ChecklistSessionRecord>,
  ): Promise<Count> {
    return this.checklistSessionRecordRepository.count(where);
  }

  @get('/checklist-session-records')
  @response(200, {
    description: 'Array of ChecklistSessionRecord model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(ChecklistSessionRecord, { includeRelations: true }),
        },
      },
    },
  })
  async find(
    @param.filter(ChecklistSessionRecord) filter?: Filter<ChecklistSessionRecord>,
  ): Promise<ChecklistSessionRecord[]> {
    return this.checklistSessionRecordRepository.find(filter);
  }

  @post('/checklist-session-start')
  @response(200, {
    security: OPERATION_SECURITY_SPEC,
    description: 'ChecklistSessionRecord model instance',
    content: { 'application/json': { schema: getModelSchemaRef(ChecklistSessionRecord) } },
  })
  @authenticate('jwt')
  async knowledgeSessionStart(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ChecklistSessionRecord, {
            title: 'NewChecklistSessionRecord',
            exclude: ['id'],
          }),
        },
      },
    })
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    knowledgeSessionRecord: ChecklistSessionStart,
  ): Promise<object> {
    const uniqueId = this.uuidGeneratorService.generateUUID();
    const email = '<EMAIL>'

    let id = ''
    const user: User | null = await this.userRepository.findOne({ where: { email: email } })
    if (user !== null) {
      id = user.id;
    } else {

    }

    const session = await this.checklistSessionRecordRepository.create({ userId: user?.id, sessionId: uniqueId, checklistId: knowledgeSessionRecord.checklistId, status: 'started' });

    return { sessionId: session.sessionId, status: session.status }


  }
  @get('/checklist-session-userid/{id}')
  @response(200, {
    security: OPERATION_SECURITY_SPEC,
    description: 'KnowledgeSessionRecord model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(ChecklistSessionRecord, { includeRelations: true }),
      },
    },
  })
  @authenticate('jwt')
  async findByUserId(
    @param.path.string('id') id: string,
    @param.filter(ChecklistSessionRecord, { exclude: 'where' }) filter?: FilterExcludingWhere<ChecklistSessionRecord>
  ): Promise<any> {
    return  this.checklistSessionRecordRepository.find({
      where: {
        userId: id
      },
      include: [
        { relation: 'user' },
        { relation: 'checklist' },
      ]
    });

   
  }

  @get('/checklist-session-threeid/{id}')
  @response(200, {
    security: OPERATION_SECURITY_SPEC,
    description: 'KnowledgeSessionRecord model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(ChecklistSessionRecord, { includeRelations: true }),
      },
    },
  })
  @authenticate('jwt')
  async findByKUId(
    @param.path.string('id') id: string,
    @param.filter(ChecklistSessionRecord, { exclude: 'where' }) filter?: FilterExcludingWhere<ChecklistSessionRecord>
  ): Promise<any> {
    return this.checklistSessionRecordRepository.find({
      where: {
        checklistId: id
      },
      include: [
        { relation: 'user' },
        { relation: 'checklist' },
      ]
    });

 
  }
  @patch('/checklist-session-end')
  @response(200, {
    security: OPERATION_SECURITY_SPEC,
    description: 'KnowledgeSessionRecord model instance',
    content: { 'application/json': { schema: getModelSchemaRef(ChecklistSessionRecord) } },
  })
  @authenticate('jwt')
  async knowledgeSessionEnd(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ChecklistSessionRecord, {
            title: 'NewKnowledgeSessionRecord',
            exclude: ['id'],
          }),
        },
      },
    })
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    knowledgeSessionRecord: ChecklistSessionEnd,
  ): Promise<Count> {

    const email = '<EMAIL>'

    let id = ''
    const user: User | null = await this.userRepository.findOne({ where: { email: email } })
    if (user !== null) {
      id = user.id;
    } else {

    }
    // const userId = '5f17349a-2198-4900-a4f1-be69107245d8'


    const where: Where<ChecklistSessionRecord> = { sessionId: knowledgeSessionRecord.sessionId, userId: id }





    return this.checklistSessionRecordRepository.updateAll({ 'stepsData': knowledgeSessionRecord.stepsData, 'status': 'end' }, where);


  }

  @patch('/checklist-session-records')
  @response(200, {
    description: 'ChecklistSessionRecord PATCH success count',
    content: { 'application/json': { schema: CountSchema } },
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ChecklistSessionRecord, { partial: true }),
        },
      },
    })
    checklistSessionRecord: ChecklistSessionRecord,
    @param.where(ChecklistSessionRecord) where?: Where<ChecklistSessionRecord>,
  ): Promise<Count> {
    return this.checklistSessionRecordRepository.updateAll(checklistSessionRecord, where);
  }

  @get('/checklist-session-records/{id}')
  @response(200, {
    description: 'ChecklistSessionRecord model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(ChecklistSessionRecord, { includeRelations: true }),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(ChecklistSessionRecord, { exclude: 'where' }) filter?: FilterExcludingWhere<ChecklistSessionRecord>
  ): Promise<ChecklistSessionRecord> {
    return this.checklistSessionRecordRepository.findById(id, filter);
  }

  @patch('/checklist-session-records/{id}')
  @response(204, {
    description: 'ChecklistSessionRecord PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ChecklistSessionRecord, { partial: true }),
        },
      },
    })
    checklistSessionRecord: ChecklistSessionRecord,
  ): Promise<void> {
    await this.checklistSessionRecordRepository.updateById(id, checklistSessionRecord);
  }

  @put('/checklist-session-records/{id}')
  @response(204, {
    description: 'ChecklistSessionRecord PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() checklistSessionRecord: ChecklistSessionRecord,
  ): Promise<void> {
    await this.checklistSessionRecordRepository.replaceById(id, checklistSessionRecord);
  }

  @del('/checklist-session-records/{id}')
  @response(204, {
    description: 'ChecklistSessionRecord DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.checklistSessionRecordRepository.deleteById(id);
  }
}
