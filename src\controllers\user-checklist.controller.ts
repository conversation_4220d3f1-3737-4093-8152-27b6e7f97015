import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
  import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
User,
UserChecklistAllocation,
Checklist,
} from '../models';
import {UserRepository} from '../repositories';

export class UserChecklistController {
  constructor(
    @repository(UserRepository) protected userRepository: UserRepository,
  ) { }

  @get('/users/{id}/checklists', {
    responses: {
      '200': {
        description: 'Array of User has many Checklist through UserChecklistAllocation',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(Checklist)},
          },
        },
      },
    },
  })
  async find(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<Checklist>,
  ): Promise<Checklist[]> {
    return this.userRepository.checklists(id).find(filter);
  }

  @post('/users/{id}/checklists', {
    responses: {
      '200': {
        description: 'create a Checklist model instance',
        content: {'application/json': {schema: getModelSchemaRef(Checklist)}},
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof User.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Checklist, {
            title: 'NewChecklistInUser',
            exclude: ['id'],
          }),
        },
      },
    }) checklist: Omit<Checklist, 'id'>,
  ): Promise<Checklist> {
    return this.userRepository.checklists(id).create(checklist);
  }

  @patch('/users/{id}/checklists', {
    responses: {
      '200': {
        description: 'User.Checklist PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Checklist, {partial: true}),
        },
      },
    })
    checklist: Partial<Checklist>,
    @param.query.object('where', getWhereSchemaFor(Checklist)) where?: Where<Checklist>,
  ): Promise<Count> {
    return this.userRepository.checklists(id).patch(checklist, where);
  }

  @del('/users/{id}/checklists', {
    responses: {
      '200': {
        description: 'User.Checklist DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(Checklist)) where?: Where<Checklist>,
  ): Promise<Count> {
    return this.userRepository.checklists(id).delete(where);
  }
}
