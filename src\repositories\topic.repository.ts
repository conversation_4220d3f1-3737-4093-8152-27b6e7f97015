import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasManyRepositoryFactory} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {Topic, TopicRelations, Unit} from '../models';
import {UnitRepository} from './unit.repository';

export class TopicRepository extends DefaultCrudRepository<
  Topic,
  typeof Topic.prototype.id,
  TopicRelations
> {

  public readonly units: HasManyRepositoryFactory<Unit, typeof Topic.prototype.id>;

  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource, @repository.getter('UnitRepository') protected unitRepositoryGetter: Getter<UnitRepository>,
  ) {
    super(Topic, dataSource);
    this.units = this.createHasManyRepositoryFactoryFor('units', unitRepositoryGetter,);
    this.registerInclusionResolver('units', this.units.inclusionResolver);
  }
}
