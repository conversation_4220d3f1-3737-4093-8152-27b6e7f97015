import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {ObservationType, ObservationTypeRelations} from '../models';

export class ObservationTypeRepository extends DefaultCrudRepository<
  ObservationType,
  typeof ObservationType.prototype.id,
  ObservationTypeRelations
> {
  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource,
  ) {
    super(ObservationType, dataSource);
  }
}
