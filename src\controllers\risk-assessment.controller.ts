import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import { RiskAssessment } from '../models';
import { RiskAssessmentRepository, UserRepository, ActionRepository } from '../repositories';
import { SqsService } from '../services/sqs-service.service';
import moment from 'moment';
import { inject } from '@loopback/core';
import _ from 'lodash';
export type notification = {
  depart: { label: string, value: string },
  activity: { label: string, value: string },
  member: any[],
  leader: string
}
export type RiskAssessmentAction = {
  teamMemberInvolved: any[],
  actionId: string
}
export class RiskAssessmentController {
  constructor(
    @repository(RiskAssessmentRepository)
    public riskAssessmentRepository: RiskAssessmentRepository,
    @repository(UserRepository)
    public userRepository: UserRepository,
    @repository(ActionRepository)
    public actionRepository: ActionRepository,
    @inject('services.SqsService')
    public sqsService: SqsService,
  ) { }
  @post('/risk-member-notification')
  @response(200, {
    description: 'RiskAssessment model instance',
    content: { 'application/json': { schema: getModelSchemaRef(RiskAssessment) } },
  })
  async riskMemberNotification(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(RiskAssessment, {
            title: 'NewRiskAssessment',
            exclude: ['id'],
          }),
        },
      },
    })
    riskAssessment: notification,
  ): Promise<boolean> {
    for (let i = 0; i < riskAssessment.member.length; i++) {
      let mailSubject = `Notification - Risk Assessment Team`;
      let mailBody = `<h4>Dear ${riskAssessment.member[i].label},</h4>
    <p>Congratulations! You've been selected for our Risk Assessment team for <b>${riskAssessment.activity.label}</b> performed by the <b>${riskAssessment.depart.label}</b> Department with the undersigned as a Team Leader.  </p>
    <h5>Other team members for this are:</h5>`;
      mailBody += '<ul>'
      for (let j = 0; j < riskAssessment.member.length; j++) {
        if (riskAssessment.member[i].label !== riskAssessment.member[j].label) {
          mailBody += `<li>${riskAssessment.member[j].label} ${riskAssessment.member[j].department}</li>`
        }

      }
      mailBody += '</ul>'
      mailBody += `<p> A comprehensive Risk Assessment with identification of all controls is of utmost importance in ensuring safe work activity at SAGT.  At SAGT we value the professional insight of team members whose collaboration and consensus contributes to the development of professional Risk Assessments.   </p>`;

      mailBody += ` <p> We look forward to your participation. Thank you for your efforts in advancing safety in SAGT. 

   </p>

<h4>${riskAssessment.leader}</h4>
<h4>RA Team Leader. </h4>
     <i>This email is an automated notification. Please do not reply to this message.</i>

    <i> If you need any assistance kindly reach <NAME_EMAIL></i>
     
   `;
      const users = await this.userRepository.findById(riskAssessment.member[i].value)

      this.sqsService.sendEmail(users.email, mailSubject, mailBody);
    }

    return true;

  }

  @post('/risk-assessments_draft')
  @response(200, {
    description: 'RiskAssessment model instance',
    content: { 'application/json': { schema: getModelSchemaRef(RiskAssessment) } },
  })
  async createDraft(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(RiskAssessment, {
            title: 'NewRiskAssessment',
            exclude: ['id'],
          }),
        },
      },
    })
    riskAssessment: Omit<RiskAssessment, 'id'>,
  ): Promise<RiskAssessment> {


    return this.riskAssessmentRepository.create(riskAssessment);
  }

  @post('/risk-assessments')
  @response(200, {
    description: 'RiskAssessment model instance',
    content: { 'application/json': { schema: getModelSchemaRef(RiskAssessment) } },
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(RiskAssessment, {
            title: 'NewRiskAssessment',
            exclude: ['id'],
          }),
        },
      },
    })
    riskAssessment: Omit<RiskAssessment, 'id'>,
  ): Promise<RiskAssessment> {
    let count = await (await this.riskAssessmentRepository.count()).count;
    // let vessel = await this.vesselsRepository.findById(meeting.vesselsId);
    //  let last = await this.riskAssessmentRepository.findOne({}, {sort: {id: -1}});

    //  console.log(last)



    const today = moment();
    const nextYear = today.add(1, 'year').format('YYYY-MM-DD');

    // console.log(nextYear);

    riskAssessment.meetid = 'RA ' + `${moment().format('YYYYMMDD')}-${count + 1}`
    riskAssessment.date = moment().utcOffset('+05:30').format('YYYY-MM-DD HH:mm');
    riskAssessment.nextdate = nextYear

    if (riskAssessment.type.label !== 'Topical') {
      for (const record of riskAssessment.teamMemberInvolved) {
        const users = await this.userRepository.findById(record.id)

        let mailSubject = `Notification - Publication of Draft - ${riskAssessment.meetid}`;
        let mailBody = `<h4>Dear ${users.firstName},</h4>
      <p>Thank you for your participation in the above RA for <b>${riskAssessment.activity}</b> in <b>${riskAssessment.department}</b> Department. </p>
      
     <p> The RA Team Leader has now published the Draft Risk Assessment based on the discussions. Please review and affirm your agreement with the same so that the RA can be formalized and released. If you are not in agreement with the Draft RA, please communicate directly with the RA Team Leader and ensure conensus.  </p>
      
     <p> Note: The RA will not be considered as "Active" till such time all RA Team Members affirm their agreement to this draft. Please do so at the earliest. </p>
       <i>This email is an automated notification. Please do not reply to this message.</i>

      <i> If you need any assistance kindly reach <NAME_EMAIL></i>
       
     `;

        this.sqsService.sendEmail(users.email, mailSubject, mailBody);



      }
      const users1 = await this.userRepository.findById(riskAssessment.userId)
      let mailSubject1 = `Risk Assessment Published  - ${riskAssessment.meetid}`;
      let mailBody1 = `Please be informed that  ${riskAssessment.captain} has signed off on  Risk Assessment ${riskAssessment.meetid} for ${riskAssessment.activity}. As per Company procedure all the RA Team Members need to confirm their agreement by signing off on the RA within 24 hours. Please ensure you do so by logging in to the Digital Portal. 
    `;

      this.sqsService.sendEmail(users1.email, mailSubject1, mailBody1);

      let mailSubject2 = `Implement Controls:  - ${riskAssessment.meetid}`;
      let mailBody2 = `The team conducting the above Risk Assessment on <Work Activity> have identified the need for addtional controls to ensure safety of the operating process and personnel invovled. Please review and take the actions as identified and within the specified timeline. `;

      for (const add of riskAssessment.additionalDates) {
        for (const date of add) {

          if (date.date !== '') {
            mailBody2 += ` Additional Controls: ${date.value}`;

            mailBody2 += ` Due Date:${moment(date.date).format('YYYY-MM-DD')}`
          }
          mailBody2 += ` Upon taking actions, please update the action card on SAGT SMS. This will send out a notification to the RA Team Leader who can verify the actions and update the RA accordingly. `

          this.sqsService.sendEmail(date.name.email, mailSubject2, mailBody2);

        }

      }


      const ra = await this.riskAssessmentRepository.create(riskAssessment);
      for (const record of riskAssessment.teamMemberInvolved) {

        const actionItem = {
          application: "RA",
          actionType: "ra_confirm",
          status: "open",
          createdDate: moment().utcOffset('+05:30').format('DD/MM/YYYY HH:mm'),
          objectId: ra.id,
          submittedById: ra.userId,
          assignedToId: [record.id]
        }

        await this.actionRepository.create(actionItem)
      }

      return ra;
    } else {
      return await this.riskAssessmentRepository.create(riskAssessment);
    }
  }

  @get('/risk-assessments/count')
  @response(200, {
    description: 'RiskAssessment model count',
    content: { 'application/json': { schema: CountSchema } },
  })
  async count(
    @param.where(RiskAssessment) where?: Where<RiskAssessment>,
  ): Promise<Count> {
    return this.riskAssessmentRepository.count(where);
  }

  @get('/risk-assessments')
  @response(200, {
    description: 'Array of RiskAssessment model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(RiskAssessment, { includeRelations: true }),
        },
      },
    },
  })
  async find(
    @param.filter(RiskAssessment) filter?: Filter<RiskAssessment>,
  ): Promise<RiskAssessment[]> {

    let risk = await this.riskAssessmentRepository.find({ ...filter, where: { statusRa: '1' } });

    risk.map(async item => {


      // if (item.type.label === 'Hazard-Based') {

      //   item.task?.map(ta => {
      //     item.hazard_name = ta[0].option[0].value !== "" ? ta[0].option[0].value : item.hazard_name
      //   })
      // }

      const check = item.teamMemberInvolved.map((item1: { sign: string }) => {
        return item1 && item1.sign === 'No';
      });
      if (item.status !== '3') {
        if (check.includes(true)) {
          item.status = 'Pending'
        } else {
          item.status = 'Published'
        }
      } else {
        item.status = 'Draft'
      }

    })
    return risk;

  }

  @get('/risk-assessments-archived')
  @response(200, {
    description: 'Array of RiskAssessment model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(RiskAssessment, { includeRelations: true }),
        },
      },
    },
  })
  async findArchied(
    @param.filter(RiskAssessment) filter?: Filter<RiskAssessment>,
  ): Promise<RiskAssessment[]> {

    let risk = await this.riskAssessmentRepository.find({ ...filter, where: { statusRa: '2' } });

    risk.map(async item => {


      // if (item.type.label === 'Hazard-Based') {

      //   item.task?.map(ta => {
      //     item.hazard_name = ta[0].option[0].value !== "" ? ta[0].option[0].value : item.hazard_name
      //   })
      // }

      const check = item.teamMemberInvolved.map((item1: { sign: string }) => {
        return item1 && item1.sign === 'No';
      });
      if (item.status !== '3') {
        if (check.includes(true)) {
          item.status = 'Pending'
        } else {
          item.status = 'Published'
        }
      } else {
        item.status = 'Draft'
      }

    })
    return risk;

  }

  @patch('/risk-assessments')
  @response(200, {
    description: 'RiskAssessment PATCH success count',
    content: { 'application/json': { schema: CountSchema } },
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(RiskAssessment, { partial: true }),
        },
      },
    })
    riskAssessment: RiskAssessment,
    @param.where(RiskAssessment) where?: Where<RiskAssessment>,
  ): Promise<Count> {

    return this.riskAssessmentRepository.updateAll(riskAssessment, where);
  }

  @get('/risk-assessments/{id}')
  @response(200, {
    description: 'RiskAssessment model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(RiskAssessment, { includeRelations: true }),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(RiskAssessment, { exclude: 'where' }) filter?: FilterExcludingWhere<RiskAssessment>
  ): Promise<RiskAssessment> {
    return this.riskAssessmentRepository.findById(id, filter);
  }

  @patch('/risk-assessments/{id}')
  @response(204, {
    description: 'RiskAssessment PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(RiskAssessment, { partial: true }),
        },
      },
    })
    riskAssessment: RiskAssessment,
  ): Promise<void> {
    riskAssessment.updatedDate = moment().utcOffset('+05:30').format('YYYY-MM-DD HH:mm');

    //   for (const record of riskAssessment.teamMemberInvolved) {
    //     const users = await this.userRepository.findById(record.id)

    //     let mailSubject = `Notification - Publication of Draft - ${riskAssessment.meetid}`;
    //     let mailBody = `<h4>Dear ${users.firstName},</h4>
    //   <p>Thank you for your participation in the above RA for <b>${riskAssessment.activity}</b> in <b>${riskAssessment.department}</b> Department. </p>

    //  <p> The RA Team Leader has now published the Draft Risk Assessment based on the discussions. Please review and affirm your agreement with the same so that the RA can be formalized and released. If you are not in agreement with the Draft RA, please communicate directly with the RA Team Leader and ensure conensus.  </p>

    //  <p> Note: The RA will not be considered as "Active" till such time all RA Team Members affirm their agreement to this draft. Please do so at the earliest. </p>
    //    <i>This email is an automated notification. Please do not reply to this message.</i>

    //   <i> If you need any assistance kindly reach <NAME_EMAIL></i>

    //  `;

    //     this.sqsService.sendEmail(users.email, mailSubject, mailBody);



    //   }
    //   const users1 = await this.userRepository.findById(riskAssessment.userId)
    //   let mailSubject1 = `Risk Assessment Published  - ${riskAssessment.meetid}`;
    //   let mailBody1 = `Please be informed that  ${riskAssessment.captain} has signed off on  Risk Assessment ${riskAssessment.meetid} for ${riskAssessment.activity}. As per Company procedure all the RA Team Members need to confirm their agreement by signing off on the RA within 24 hours. Please ensure you do so by logging in to the Digital Portal. 
    // `;

    //   this.sqsService.sendEmail(users1.email, mailSubject1, mailBody1);

    riskAssessment.statusRa = '1';


    if (riskAssessment.teamMemberInvolved) {
      //  console.log( await this.actionRepository.find({where:{objectId:id}}))

      for (const record of riskAssessment.teamMemberInvolved) {

        const already = await this.actionRepository.find({
          where: {
            and: [
              { objectId: id },
              { status: "open" },
              { assignedToId: { inq: [record.id] } },
            ],
          },
        });



        if (already.length === 0) {
          const actionItem = {
            application: "RA",
            actionType: "ra_confirm",
            status: "open",
            createdDate: moment().utcOffset('+05:30').format('DD/MM/YYYY HH:mm'),
            objectId: id,
            submittedById: riskAssessment.userId,
            assignedToId: [record.id]
          }

          await this.actionRepository.create(actionItem)
        }


      }
    }
    await this.riskAssessmentRepository.updateById(id, riskAssessment);
  }

  @patch('/risk-assessments_action/{id}')
  @response(204, {
    description: 'RiskAssessment PATCH success',
  })
  async updateActionById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(RiskAssessment, { partial: true }),
        },
      },
    })
    riskAssessment: RiskAssessmentAction,
  ): Promise<void> {
    // riskAssessment.updatedDate = moment().utcOffset('+05:30').format('YYYY-MM-DD HH:mm');
    const final = _.omit(riskAssessment, ['actionId']);

    const action = await this.actionRepository.findById(riskAssessment.actionId);
    const actionAssigneeId = action?.assignedToId?.[0];
    let commingObject = ''
    if (action?.assignedToId?.length) {
      commingObject = final.teamMemberInvolved.find(item => item.id === actionAssigneeId);
    }
    const ra = await this.riskAssessmentRepository.findById(id);
    const teamMemberInvovled = ra.teamMemberInvolved.filter((item: any) => item.id !== actionAssigneeId);
    const updated = [...teamMemberInvovled, commingObject]


    await this.riskAssessmentRepository.updateById(id, { 'teamMemberInvolved': updated });
    await this.actionRepository.updateById(riskAssessment.actionId, { status: 'completed' });
  }

  @patch('/risk-assessments_draft/{id}')
  @response(204, {
    description: 'RiskAssessment PATCH success',
  })
  async updateByIdDraft(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(RiskAssessment, { partial: true }),
        },
      },
    })
    riskAssessment: RiskAssessment,
  ): Promise<void> {
    let count = await (await this.riskAssessmentRepository.count()).count;

    const risk = await this.riskAssessmentRepository.findById(id);

    const today = moment();
    const nextYear = today.add(1, 'year').format('YYYY-MM-DD');

    // console.log(nextYear);

    riskAssessment.meetid = 'RA ' + `${moment().format('YYYYMMDD')}-${count + 1}`
    riskAssessment.date = moment().utcOffset('+05:30').format('YYYY-MM-DD HH:mm');
    riskAssessment.nextdate = nextYear

    for (const record of riskAssessment.teamMemberInvolved) {
      const users = await this.userRepository.findById(record.id)

      let mailSubject = `Notification - Publication of Draft - ${riskAssessment.meetid}`;
      let mailBody = `<h4>Dear ${users.firstName},</h4>
      <p>Thank you for your participation in the above RA for <b>${riskAssessment.activity}</b> in <b>${riskAssessment.department}</b> Department. </p>
      
     <p> The RA Team Leader has now published the Draft Risk Assessment based on the discussions. Please review and affirm your agreement with the same so that the RA can be formalized and released. If you are not in agreement with the Draft RA, please communicate directly with the RA Team Leader and ensure conensus.  </p>
      
     <p> Note: The RA will not be considered as "Active" till such time all RA Team Members affirm their agreement to this draft. Please do so at the earliest. </p>
       <i>This email is an automated notification. Please do not reply to this message.</i>

      <i> If you need any assistance kindly reach <NAME_EMAIL></i>
       
     `;

      await this.sqsService.sendMessage(users, mailSubject, mailBody);

    }
    const users1 = await this.userRepository.findById(risk.userId)
    let mailSubject1 = `Risk Assessment Published  - ${riskAssessment.meetid}`;
    let mailBody1 = `Please be informed that  ${riskAssessment.captain} has signed off on  Risk Assessment ${riskAssessment.meetid} for ${riskAssessment.activity}. As per Company procedure all the RA Team Members need to confirm their agreement by signing off on the RA within 24 hours. Please ensure you do so by logging in to the Digital Portal. 
    `;

    await this.sqsService.sendMessage(users1, mailSubject1, mailBody1);

    let mailSubject2 = `Implement Controls:  - ${riskAssessment.meetid}`;
    let mailBody2 = `The team conducting the above Risk Assessment on <Work Activity> have identified the need for addtional controls to ensure safety of the operating process and personnel invovled. Please review and take the actions as identified and within the specified timeline. `;
    if (riskAssessment.additionalDates && riskAssessment.additionalDates.length > 0) {
      for (const add of riskAssessment.additionalDates) {
        for (const date of add) {
          if (date.date !== '') {
            mailBody2 += ` Additional Controls: ${date.value}`;
            mailBody2 += ` Due Date:${moment(date.date).format('YYYY-MM-DD')}`
          }
          mailBody2 += ` Upon taking actions, please update the action card on SAGT SMS. This will send out a notification to the RA Team Leader who can verify the actions and update the RA accordingly. `
          const user = await this.userRepository.findOne({ where: { email: date.name.email } })
          if (!user) { console.log('Mail not sent! user not found') } else {
            await this.sqsService.sendMessage(user, mailSubject2, mailBody2);
          }
        }
      }
    } else {
      // Handle the case where riskAssessment.additionalDates is undefined or empty
      console.error("Risk assessment additionalDates is undefined or empty");
    }


    for (const record of riskAssessment.teamMemberInvolved) {

      const actionItem = {
        application: "RA",
        actionType: "ra_confirm",
        status: "open",
        createdDate: moment().utcOffset('+05:30').format('DD/MM/YYYY HH:mm'),
        objectId: id,
        submittedById: riskAssessment.userId,
        assignedToId: [record.id]
      }

      await this.actionRepository.create(actionItem)
    }



    await this.riskAssessmentRepository.updateById(id, riskAssessment);
  }

  @put('/risk-assessments/{id}')
  @response(204, {
    description: 'RiskAssessment PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() riskAssessment: RiskAssessment,
  ): Promise<void> {
    await this.riskAssessmentRepository.replaceById(id, riskAssessment);
  }

  @del('/risk-assessments/{id}')
  @response(204, {
    description: 'RiskAssessment DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.riskAssessmentRepository.updateById(id, { statusRa: '2' });
    await this.actionRepository.updateAll({ objectId: id }, { status: 'completed' });
    const riskAssessment = await this.riskAssessmentRepository.findById(id);

    for (const record of riskAssessment.teamMemberInvolved) {
      const users = await this.userRepository.findById(record.id)

      const mailSubject = `Notification - Risk Assessment Deletion - ${riskAssessment.meetid}`;
      const mailBody = `<h4>Dear ${users.firstName},</h4>
      <p>This is to inform you that the Risk Assessment for <b>${riskAssessment.activity}</b> in <b>${riskAssessment.department}</b> Department has been deleted by RA Team Leader.  </p>


      <i> This email is an automated notification. Please do not reply to this message. If you need any assistance kindly reach <NAME_EMAIL></i>
       
     `;

      await this.sqsService.sendMessage(users, mailSubject, mailBody);

    }
    // await this.riskAssessmentRepository.deleteById(id);
  }
}
