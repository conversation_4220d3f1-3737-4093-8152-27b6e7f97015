import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {TierTwo, TierTwoRelations} from '../models';

export class TierTwoRepository extends DefaultCrudRepository<
  TierTwo,
  typeof TierTwo.prototype.id,
  TierTwoRelations
> {
  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource,
  ) {
    super(TierTwo, dataSource);
  }
}
