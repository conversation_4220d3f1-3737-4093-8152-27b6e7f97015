import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import { GroupChecklistAllocation } from '../models';
import { GroupChecklistAllocationRepository, GroupsRepository, UserChecklistAllocationRepository } from '../repositories';
type Checklist = {
  checklistId: [],
  groupid: string

};
export class GroupChecklistAllocationController {
  constructor(
    @repository(GroupChecklistAllocationRepository)
    public groupChecklistAllocationRepository: GroupChecklistAllocationRepository,
    @repository(UserChecklistAllocationRepository)
    public userChecklistAllocationRepository: UserChecklistAllocationRepository,
    @repository(GroupsRepository)
    public groupRepository: GroupsRepository,
  ) { }

  @post('/group-checklist-allocations')
  @response(200, {
    description: 'GroupChecklistAllocation model instance',
    content: { 'application/json': { schema: getModelSchemaRef(GroupChecklistAllocation) } },
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(GroupChecklistAllocation, {
            title: 'NewGroupChecklistAllocation',
            exclude: ['id'],
          }),
        },
      },
    })
    groupChecklistAllocation: Omit<GroupChecklistAllocation, 'id'>,
  ): Promise<GroupChecklistAllocation> {
    return this.groupChecklistAllocationRepository.create(groupChecklistAllocation);
  }

  @get('/group-checklist-allocations/count')
  @response(200, {
    description: 'GroupChecklistAllocation model count',
    content: { 'application/json': { schema: CountSchema } },
  })
  async count(
    @param.where(GroupChecklistAllocation) where?: Where<GroupChecklistAllocation>,
  ): Promise<Count> {
    return this.groupChecklistAllocationRepository.count(where);
  }

  @get('/group-checklist-allocations')
  @response(200, {
    description: 'Array of GroupChecklistAllocation model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(GroupChecklistAllocation, { includeRelations: true }),
        },
      },
    },
  })
  async find(
    @param.filter(GroupChecklistAllocation) filter?: Filter<GroupChecklistAllocation>,
  ): Promise<GroupChecklistAllocation[]> {
    return this.groupChecklistAllocationRepository.find(filter);
  }

  @patch('/group-checklist-allocations')
  @response(200, {
    description: 'GroupChecklistAllocation PATCH success count',
    content: { 'application/json': { schema: CountSchema } },
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(GroupChecklistAllocation, { partial: true }),
        },
      },
    })
    groupChecklistAllocation: GroupChecklistAllocation,
    @param.where(GroupChecklistAllocation) where?: Where<GroupChecklistAllocation>,
  ): Promise<Count> {
    return this.groupChecklistAllocationRepository.updateAll(groupChecklistAllocation, where);
  }

  @get('/group-checklist-allocations/{id}')
  @response(200, {
    description: 'GroupChecklistAllocation model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(GroupChecklistAllocation, { includeRelations: true }),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(GroupChecklistAllocation, { exclude: 'where' }) filter?: FilterExcludingWhere<GroupChecklistAllocation>
  ): Promise<GroupChecklistAllocation> {
    return this.groupChecklistAllocationRepository.findById(id, filter);
  }

  @patch('/group-checklist-allocations/{id}')
  @response(204, {
    description: 'GroupChecklistAllocation PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(GroupChecklistAllocation, { partial: true }),
        },
      },
    })
    groupChecklistAllocation: GroupChecklistAllocation,
  ): Promise<void> {
    await this.groupChecklistAllocationRepository.updateById(id, groupChecklistAllocation);
  }

  @put('/group-checklist-allocations/{id}')
  @response(204, {
    description: 'GroupChecklistAllocation PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() groupChecklistAllocation: GroupChecklistAllocation,
  ): Promise<void> {
    await this.groupChecklistAllocationRepository.replaceById(id, groupChecklistAllocation);
  }

  @del('/group-checklist-allocations/{id}')
  @response(204, {
    description: 'GroupChecklistAllocation DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.groupChecklistAllocationRepository.deleteById(id);
  }

  @post('/add-checklist-group-user')
  @response(200, {
    description: 'UserTierThree model instance'
  })
  async userThrees(
    @requestBody() threeOne: Checklist,
  ): Promise<void> {
    if (threeOne.checklistId.length === 0) {
      // return this.groupUserRepository.create(groupUser);
      const user = await this.groupRepository.users(threeOne.groupid).find()
      await this.groupChecklistAllocationRepository.deleteAll({
        and: [
          { groupsId: threeOne.groupid },

        ],

      })
      user.map(async item => {
        await this.userChecklistAllocationRepository.deleteAll({
          and: [

            { userId: item.id },
            { groupId: threeOne.groupid },
            { type: 'group' }
          ],

        })
      })



    }
    else {

      // group allocation part
      const kaOut: (string | undefined)[] = threeOne.checklistId;


      const alreadyRa = await this.groupChecklistAllocationRepository.find({
        where: {
          "groupsId": threeOne.groupid
        }
      })
      const kaIn = alreadyRa.map(item => {
        return item.checklistId
      })
      const convertedIntArray: (string | undefined)[] = kaIn.map(num => String(num));


      const insertA = kaOut.filter(item => !convertedIntArray.includes(item));

      if (insertA.length !== 0) {
        insertA.map(async i => {
          await this.groupChecklistAllocationRepository.create({ "groupsId": threeOne.groupid, "checklistId": i });

        })

      }
      const deleteA = convertedIntArray.filter(item => !kaOut.includes(item))

      if (deleteA.length !== 0) {
        deleteA.map(async i => {
          await this.groupChecklistAllocationRepository.deleteAll({
            and: [

              { checklistId: i },
              { groupsId: threeOne.groupid }
            ],
          });

        })

      }


      //Unit allocation part
      const user = await this.groupRepository.users(threeOne.groupid).find();
      if (user.length !== 0) {
        user.map(async item => {
          const alreadyKu = await this.userChecklistAllocationRepository.find({
            where: {
              userId: item.id
            }
          })
          const kaUserIn = alreadyKu.map(k => {
            return k.checklistId
          })
          const convertedInt: (string | undefined)[] = kaUserIn.map(num => String(num));


          const insertKaUser = kaOut.filter(it => !convertedInt.includes(it));


          if (insertKaUser.length !== 0) {
            insertKaUser.map(async i => {
              await this.userChecklistAllocationRepository.create({
                "userId": item.id,
                "checklistId": i,
                "groupId": threeOne.groupid,
                "type": 'group'
              });

            })
          }

          const deleteKaUser = convertedInt.filter(ite => !kaOut.includes(ite));
      
          if (deleteKaUser.length !== 0) {

           
            deleteKaUser.map(async i => {
            
         await this.userChecklistAllocationRepository.deleteAll({
              
                  and: [

                    { groupId: threeOne.groupid },
                    { checklistId: i },
                    { userId: item.id },
                    { type: 'group' }
                  ],
            
              });


            })
          }

        })
      }


    }


  }

  @get('/group-checklist-groupid/{id}')
  @response(200, {
    description: 'GroupAssignTierThree model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(GroupChecklistAllocation, { includeRelations: true }),
      },
    },
  })
  async findGroupById(
    @param.path.string('id') id: string,
    @param.filter(GroupChecklistAllocation, { exclude: 'where' }) filter?: FilterExcludingWhere<GroupChecklistAllocation>
  ): Promise<GroupChecklistAllocation[]> {
    return this.groupChecklistAllocationRepository.find({
      where: {
        groupsId: id
      }
    });
  }
}
