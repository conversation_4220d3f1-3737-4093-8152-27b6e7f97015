import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasManyRepositoryFactory} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {HumanPartPrimary, HumanPartPrimaryRelations, HumanPartSecondary} from '../models';
import {HumanPartSecondaryRepository} from './human-part-secondary.repository';

export class HumanPartPrimaryRepository extends DefaultCrudRepository<
  HumanPartPrimary,
  typeof HumanPartPrimary.prototype.id,
  HumanPartPrimaryRelations
> {

  public readonly humanPartSecondaries: HasManyRepositoryFactory<HumanPartSecondary, typeof HumanPartPrimary.prototype.id>;

  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource, @repository.getter('HumanPartSecondaryRepository') protected humanPartSecondaryRepositoryGetter: Getter<HumanPartSecondaryRepository>,
  ) {
    super(HumanPartPrimary, dataSource);
    this.humanPartSecondaries = this.createHasManyRepositoryFactoryFor('humanPartSecondaries', humanPartSecondaryRepositoryGetter,);
    this.registerInclusionResolver('humanPartSecondaries', this.humanPartSecondaries.inclusionResolver);
  }
}
