import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  ReportIncident,
  RiskCategory,
} from '../models';
import {ReportIncidentRepository} from '../repositories';

export class ReportIncidentRiskCategoryController {
  constructor(
    @repository(ReportIncidentRepository)
    public reportIncidentRepository: ReportIncidentRepository,
  ) { }

  @get('/report-incidents/{id}/risk-category', {
    responses: {
      '200': {
        description: 'RiskCategory belonging to ReportIncident',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(RiskCategory)},
          },
        },
      },
    },
  })
  async getRiskCategory(
    @param.path.string('id') id: typeof ReportIncident.prototype.id,
  ): Promise<RiskCategory> {
    return this.reportIncidentRepository.riskCategory(id);
  }
}
