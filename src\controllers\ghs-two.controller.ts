import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import { GhsTwo } from '../models';
import { GhsTwoRepository } from '../repositories';
import { authenticate } from '@loopback/authentication';
@authenticate('jwt')
export class GhsTwoController {
  constructor(
    @repository(GhsTwoRepository)
    public ghsTwoRepository: GhsTwoRepository,
  ) { }

  @post('/ghs-twos')
  @response(200, {
    description: 'GhsTwo model instance',
    content: { 'application/json': { schema: getModelSchemaRef(GhsTwo) } },
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(GhsTwo, {
            title: 'NewGhsTwo',
            exclude: ['id'],
          }),
        },
      },
    })
    ghsTwo: Omit<GhsTwo, 'id'>,
  ): Promise<GhsTwo> {
    return this.ghsTwoRepository.create(ghsTwo);
  }

  @get('/ghs-twos/count')
  @response(200, {
    description: 'GhsTwo model count',
    content: { 'application/json': { schema: CountSchema } },
  })
  async count(
    @param.where(GhsTwo) where?: Where<GhsTwo>,
  ): Promise<Count> {
    return this.ghsTwoRepository.count(where);
  }

  @get('/ghs-twos')
  @response(200, {
    description: 'Array of GhsTwo model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'string' },
              title: { type: 'string' },
            },
          },
        },
      },
    },
  })
  async find(
    @param.filter(GhsTwo) filter?: Filter<GhsTwo>,
  ): Promise<{ id: string; title: string }[]> {
    const ghsTwos = await this.ghsTwoRepository.find(filter);

    // Transform ghsTwos into the desired format
    const transformedGhsTwos = ghsTwos.map(ghsTwo => ({
      id: ghsTwo.id ?? '',
      title: ghsTwo.name ?? '',
      ...ghsTwo
    }));

    return transformedGhsTwos;
  }

  @patch('/ghs-twos')
  @response(200, {
    description: 'GhsTwo PATCH success count',
    content: { 'application/json': { schema: CountSchema } },
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(GhsTwo, { partial: true }),
        },
      },
    })
    ghsTwo: GhsTwo,
    @param.where(GhsTwo) where?: Where<GhsTwo>,
  ): Promise<Count> {
    return this.ghsTwoRepository.updateAll(ghsTwo, where);
  }

  @get('/ghs-twos/{id}')
  @response(200, {
    description: 'GhsTwo model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(GhsTwo, { includeRelations: true }),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(GhsTwo, { exclude: 'where' }) filter?: FilterExcludingWhere<GhsTwo>
  ): Promise<GhsTwo> {
    return this.ghsTwoRepository.findById(id, filter);
  }

  @patch('/ghs-twos/{id}')
  @response(204, {
    description: 'GhsTwo PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(GhsTwo, { partial: true }),
        },
      },
    })
    ghsTwo: GhsTwo,
  ): Promise<void> {
    await this.ghsTwoRepository.updateById(id, ghsTwo);
  }

  @put('/ghs-twos/{id}')
  @response(204, {
    description: 'GhsTwo PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() ghsTwo: GhsTwo,
  ): Promise<void> {
    await this.ghsTwoRepository.replaceById(id, ghsTwo);
  }

  @del('/ghs-twos/{id}')
  @response(204, {
    description: 'GhsTwo DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.ghsTwoRepository.deleteById(id);
  }
}
