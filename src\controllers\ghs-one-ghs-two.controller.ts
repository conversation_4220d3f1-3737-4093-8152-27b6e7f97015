import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  GhsOne,
  GhsTwo,
} from '../models';
import {GhsOneRepository} from '../repositories';

export class GhsOneGhsTwoController {
  constructor(
    @repository(GhsOneRepository) protected ghsOneRepository: GhsOneRepository,
  ) { }

  @get('/ghs-ones/{id}/ghs-twos', {
    responses: {
      '200': {
        description: 'Array of GhsOne has many GhsTwo',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(GhsTwo)},
          },
        },
      },
    },
  })
  async find(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<GhsTwo>,
  ): Promise<GhsTwo[]> {
    return this.ghsOneRepository.ghsTwos(id).find(filter);
  }

  @post('/ghs-ones/{id}/ghs-twos', {
    responses: {
      '200': {
        description: 'GhsOne model instance',
        content: {'application/json': {schema: getModelSchemaRef(GhsTwo)}},
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof GhsOne.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(GhsTwo, {
            title: 'NewGhsTwoInGhsOne',
            exclude: ['id'],
            optional: ['ghsOneId']
          }),
        },
      },
    }) ghsTwo: Omit<GhsTwo, 'id'>,
  ): Promise<GhsTwo> {
    return this.ghsOneRepository.ghsTwos(id).create(ghsTwo);
  }

  @patch('/ghs-ones/{id}/ghs-twos', {
    responses: {
      '200': {
        description: 'GhsOne.GhsTwo PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(GhsTwo, {partial: true}),
        },
      },
    })
    ghsTwo: Partial<GhsTwo>,
    @param.query.object('where', getWhereSchemaFor(GhsTwo)) where?: Where<GhsTwo>,
  ): Promise<Count> {
    return this.ghsOneRepository.ghsTwos(id).patch(ghsTwo, where);
  }

  @del('/ghs-ones/{id}/ghs-twos', {
    responses: {
      '200': {
        description: 'GhsOne.GhsTwo DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(GhsTwo)) where?: Where<GhsTwo>,
  ): Promise<Count> {
    return this.ghsOneRepository.ghsTwos(id).delete(where);
  }
}
