import {Entity, model, property} from '@loopback/repository';

@model({settings: {strict: false}})
export class TitleConfig extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  tier1?: string;

  @property({
    type: 'string',
  })
  tier2?: string;

  @property({
    type: 'string',
  })
  tier3?: string;

  // Define well-known properties here

  // Indexer property to allow additional data
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [prop: string]: any;

  constructor(data?: Partial<TitleConfig>) {
    super(data);
  }
}

export interface TitleConfigRelations {
  // describe navigational properties here
}

export type TitleConfigWithRelations = TitleConfig & TitleConfigRelations;
