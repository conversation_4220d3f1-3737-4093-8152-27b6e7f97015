import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  Topic,
  Unit,
} from '../models';
import {TopicRepository} from '../repositories';

export class TopicUnitController {
  constructor(
    @repository(TopicRepository) protected topicRepository: TopicRepository,
  ) { }

  @get('/topics/{id}/units', {
    responses: {
      '200': {
        description: 'Array of Topic has many Unit',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(Unit)},
          },
        },
      },
    },
  })
  async find(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<Unit>,
  ): Promise<Unit[]> {
    return this.topicRepository.units(id).find(filter);
  }

  @post('/topics/{id}/units', {
    responses: {
      '200': {
        description: 'Topic model instance',
        content: {'application/json': {schema: getModelSchemaRef(Unit)}},
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof Topic.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Unit, {
            title: 'NewUnitInTopic',
            exclude: ['id'],
            optional: ['topicId']
          }),
        },
      },
    }) unit: Omit<Unit, 'id'>,
  ): Promise<Unit> {
    return this.topicRepository.units(id).create(unit);
  }

  @patch('/topics/{id}/units', {
    responses: {
      '200': {
        description: 'Topic.Unit PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Unit, {partial: true}),
        },
      },
    })
    unit: Partial<Unit>,
    @param.query.object('where', getWhereSchemaFor(Unit)) where?: Where<Unit>,
  ): Promise<Count> {
    return this.topicRepository.units(id).patch(unit, where);
  }

  @del('/topics/{id}/units', {
    responses: {
      '200': {
        description: 'Topic.Unit DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(Unit)) where?: Where<Unit>,
  ): Promise<Count> {
    return this.topicRepository.units(id).delete(where);
  }
}
