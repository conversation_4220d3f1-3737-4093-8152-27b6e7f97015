import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  UserLocationRole,
  LocationOne,
} from '../models';
import {UserLocationRoleRepository} from '../repositories';

export class UserLocationRoleLocationOneController {
  constructor(
    @repository(UserLocationRoleRepository)
    public userLocationRoleRepository: UserLocationRoleRepository,
  ) { }

  @get('/user-location-roles/{id}/location-one', {
    responses: {
      '200': {
        description: 'LocationOne belonging to UserLocationRole',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(LocationOne)},
          },
        },
      },
    },
  })
  async getLocationOne(
    @param.path.string('id') id: typeof UserLocationRole.prototype.id,
  ): Promise<LocationOne> {
    return this.userLocationRoleRepository.locationOne(id);
  }
}
