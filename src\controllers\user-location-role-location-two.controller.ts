import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  UserLocationRole,
  LocationTwo,
} from '../models';
import {UserLocationRoleRepository} from '../repositories';

export class UserLocationRoleLocationTwoController {
  constructor(
    @repository(UserLocationRoleRepository)
    public userLocationRoleRepository: UserLocationRoleRepository,
  ) { }

  @get('/user-location-roles/{id}/location-two', {
    responses: {
      '200': {
        description: 'LocationTwo belonging to UserLocationRole',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(LocationTwo)},
          },
        },
      },
    },
  })
  async getLocationTwo(
    @param.path.string('id') id: typeof UserLocationRole.prototype.id,
  ): Promise<LocationTwo> {
    return this.userLocationRoleRepository.locationTwo(id);
  }
}
