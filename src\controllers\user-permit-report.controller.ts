import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  User,
  PermitReport,
} from '../models';
import {UserRepository} from '../repositories';

export class UserPermitReportController {
  constructor(
    @repository(UserRepository) protected userRepository: UserRepository,
  ) { }

  @get('/users/{id}/permit-reports', {
    responses: {
      '200': {
        description: 'Array of User has many PermitReport',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(PermitReport)},
          },
        },
      },
    },
  })
  async find(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<PermitReport>,
  ): Promise<PermitReport[]> {
    return this.userRepository.dscoApproverReports(id).find(filter);
  }

  @post('/users/{id}/permit-reports', {
    responses: {
      '200': {
        description: 'User model instance',
        content: {'application/json': {schema: getModelSchemaRef(PermitReport)}},
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof User.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(PermitReport, {
            title: 'NewPermitReportInUser',
            exclude: ['id'],
            optional: ['dcsoApproverId']
          }),
        },
      },
    }) permitReport: Omit<PermitReport, 'id'>,
  ): Promise<PermitReport> {
    return this.userRepository.dscoApproverReports(id).create(permitReport);
  }

  @patch('/users/{id}/permit-reports', {
    responses: {
      '200': {
        description: 'User.PermitReport PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(PermitReport, {partial: true}),
        },
      },
    })
    permitReport: Partial<PermitReport>,
    @param.query.object('where', getWhereSchemaFor(PermitReport)) where?: Where<PermitReport>,
  ): Promise<Count> {
    return this.userRepository.dscoApproverReports(id).patch(permitReport, where);
  }

  @del('/users/{id}/permit-reports', {
    responses: {
      '200': {
        description: 'User.PermitReport DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(PermitReport)) where?: Where<PermitReport>,
  ): Promise<Count> {
    return this.userRepository.dscoApproverReports(id).delete(where);
  }
}
