import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasManyRepositoryFactory} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {LocationFour, LocationFourRelations, LocationFive, ObservationReport, UserLocationRole, PermitReport} from '../models';
import {LocationFiveRepository} from './location-five.repository';
import {ObservationReportRepository} from './observation-report.repository';
import {UserLocationRoleRepository} from './user-location-role.repository';
import {PermitReportRepository} from './permit-report.repository';

export class LocationFourRepository extends DefaultCrudRepository<
  LocationFour,
  typeof LocationFour.prototype.id,
  LocationFourRelations
> {

  public readonly locationFives: HasManyRepositoryFactory<LocationFive, typeof LocationFour.prototype.id>;

  public readonly observationReports: HasManyRepositoryFactory<ObservationReport, typeof LocationFour.prototype.id>;

  public readonly userLocationRoles: HasManyRepositoryFactory<UserLocationRole, typeof LocationFour.prototype.id>;

  public readonly permitReports: HasManyRepositoryFactory<PermitReport, typeof LocationFour.prototype.id>;

  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource, @repository.getter('LocationFiveRepository') protected locationFiveRepositoryGetter: Getter<LocationFiveRepository>, @repository.getter('ObservationReportRepository') protected observationReportRepositoryGetter: Getter<ObservationReportRepository>, @repository.getter('UserLocationRoleRepository') protected userLocationRoleRepositoryGetter: Getter<UserLocationRoleRepository>, @repository.getter('PermitReportRepository') protected permitReportRepositoryGetter: Getter<PermitReportRepository>,
  ) {
    super(LocationFour, dataSource);
    this.permitReports = this.createHasManyRepositoryFactoryFor('permitReports', permitReportRepositoryGetter,);
    this.registerInclusionResolver('permitReports', this.permitReports.inclusionResolver);
    this.userLocationRoles = this.createHasManyRepositoryFactoryFor('userLocationRoles', userLocationRoleRepositoryGetter,);
    this.registerInclusionResolver('userLocationRoles', this.userLocationRoles.inclusionResolver);
    this.observationReports = this.createHasManyRepositoryFactoryFor('observationReports', observationReportRepositoryGetter,);
    this.registerInclusionResolver('observationReports', this.observationReports.inclusionResolver);
    this.locationFives = this.createHasManyRepositoryFactoryFor('locationFives', locationFiveRepositoryGetter,);
    this.registerInclusionResolver('locationFives', this.locationFives.inclusionResolver);
  }
}
