import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  User,
  Designation,
} from '../models';
import {UserRepository} from '../repositories';

export class UserDesignationController {
  constructor(
    @repository(UserRepository)
    public userRepository: UserRepository,
  ) { }

  @get('/users/{id}/designation', {
    responses: {
      '200': {
        description: 'Designation belonging to User',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(Designation)},
          },
        },
      },
    },
  })
  async getDesignation(
    @param.path.string('id') id: typeof User.prototype.id,
  ): Promise<Designation> {
    return this.userRepository.designation(id);
  }
}
