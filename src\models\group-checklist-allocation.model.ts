import {Entity, model, property} from '@loopback/repository';

@model({settings: {strict: false}})
export class GroupChecklistAllocation extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  checklistId?: string;

  @property({
    type: 'string',
  })
  groupsId?: string;
  // Define well-known properties here

  // Indexer property to allow additional data
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [prop: string]: any;

  constructor(data?: Partial<GroupChecklistAllocation>) {
    super(data);
  }
}

export interface GroupChecklistAllocationRelations {
  // describe navigational properties here
}

export type GroupChecklistAllocationWithRelations = GroupChecklistAllocation & GroupChecklistAllocationRelations;
