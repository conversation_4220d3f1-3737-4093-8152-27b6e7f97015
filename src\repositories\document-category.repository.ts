import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasManyRepositoryFactory} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {DocumentCategory, DocumentCategoryRelations, Documents} from '../models';
import {DocumentsRepository} from './documents.repository';

export class DocumentCategoryRepository extends DefaultCrudRepository<
  DocumentCategory,
  typeof DocumentCategory.prototype.id,
  DocumentCategoryRelations
> {

  public readonly documents: HasManyRepositoryFactory<Documents, typeof DocumentCategory.prototype.id>;

  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource, @repository.getter('DocumentsRepository') protected documentsRepositoryGetter: Getter<DocumentsRepository>,
  ) {
    super(DocumentCategory, dataSource);
    this.documents = this.createHasManyRepositoryFactoryFor('documents', documentsRepositoryGetter,);
    this.registerInclusionResolver('documents', this.documents.inclusionResolver);
  }
}
