import { Entity, model, property } from '@loopback/repository';

@model()
export class GeneralGroup extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'string',
  })
  group?: string;

  @property({
    type: 'array',
    itemType: 'object',
  })
  email?: object[];

  @property({
    type: 'string',
  })
  created?: string;


  constructor(data?: Partial<GeneralGroup>) {
    super(data);
  }
}

export interface GeneralGroupRelations {
  // describe navigational properties here
}

export type GeneralGroupWithRelations = GeneralGroup & GeneralGroupRelations;
