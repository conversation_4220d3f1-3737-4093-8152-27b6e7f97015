import {Entity, model, property, belongsTo, hasMany} from '@loopback/repository';
import {User} from './user.model';
import {RiskUpdate} from './risk-update.model';

@model({settings: {strict: false}})
export class RiskAssessment extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'array',
    itemType: 'object',
  })
  member?: object[];

  @property({
    type: 'string',
  })
  riskid?: string;

  @property({
    type: 'array',
    itemType: 'any',
  })
  task?: any[];

  @property({
    type: 'array',
    itemType: 'any',
  })
  teamMemberInvovled?: any[];

  @property({
    type: 'string',
  })
  status?: string;

  @property({
    type: 'string',
  })
  hazard_name?: string;

  @property({
    type: 'string',
    default: '1',
  })
  statusRa?: string;
  @property({
    type: 'string',
    default: '',
  })
  topical?: string;
  @property({
    type: 'string',
  })
  date?: string;

  @property({
    type: 'string',
  })
  nextDate?: string;

  @property({
    type: 'string',
  })
  updatedDate?: string;
  @property({
    type: 'array',
    itemType: 'any',
  })
  additional?: any[];
  @property({
    type: 'array',
    itemType: 'any',
  })
  additionalDates?: any[];
  @property({
    type: 'string',
  })
  captain?: string;
  @property({
    type: 'string',
  })
  sign?: string;
  
  @property({
    type: 'string',
  })
  thirdAdd?: string;

  @property({
    type: 'object',
  })
  recommendation?: object;

  @property({
    type: 'object',
  })
  additionRecommendation?: object;

  @property({
    type: 'boolean',
    default: null,
  })
  eptw?: boolean;

  @property({
    type: 'array',
    itemType: 'any',
  })
  currentcontrol?: any[];

  @property({
    type: 'array',
    itemType: 'any',
  })
  eptwHighRisk?: any[];

  @belongsTo(() => User)
  userId: string;

  @hasMany(() => RiskUpdate)
  riskUpdates: RiskUpdate[];
  // Define well-known properties here

  // Indexer property to allow additional data
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [prop: string]: any;

  constructor(data?: Partial<RiskAssessment>) {
    super(data);
  }
}

export interface RiskAssessmentRelations {
  // describe navigational properties here
}

export type RiskAssessmentWithRelations = RiskAssessment & RiskAssessmentRelations;
