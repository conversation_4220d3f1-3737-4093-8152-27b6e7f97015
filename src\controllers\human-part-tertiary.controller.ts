import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {HumanPartTertiary} from '../models';
import {HumanPartTertiaryRepository} from '../repositories';

export class HumanPartTertiaryController {
  constructor(
    @repository(HumanPartTertiaryRepository)
    public humanPartTertiaryRepository : HumanPartTertiaryRepository,
  ) {}

  @post('/human-part-tertiaries')
  @response(200, {
    description: 'HumanPartTertiary model instance',
    content: {'application/json': {schema: getModelSchemaRef(HumanPartTertiary)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(HumanPartTertiary, {
            title: 'NewHumanPartTertiary',
            exclude: ['id'],
          }),
        },
      },
    })
    humanPartTertiary: Omit<HumanPartTertiary, 'id'>,
  ): Promise<HumanPartTertiary> {
    return this.humanPartTertiaryRepository.create(humanPartTertiary);
  }

  @get('/human-part-tertiaries/count')
  @response(200, {
    description: 'HumanPartTertiary model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(HumanPartTertiary) where?: Where<HumanPartTertiary>,
  ): Promise<Count> {
    return this.humanPartTertiaryRepository.count(where);
  }

  @get('/human-part-tertiaries')
  @response(200, {
    description: 'Array of HumanPartTertiary model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(HumanPartTertiary, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(HumanPartTertiary) filter?: Filter<HumanPartTertiary>,
  ): Promise<HumanPartTertiary[]> {
    return this.humanPartTertiaryRepository.find(filter);
  }

  @patch('/human-part-tertiaries')
  @response(200, {
    description: 'HumanPartTertiary PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(HumanPartTertiary, {partial: true}),
        },
      },
    })
    humanPartTertiary: HumanPartTertiary,
    @param.where(HumanPartTertiary) where?: Where<HumanPartTertiary>,
  ): Promise<Count> {
    return this.humanPartTertiaryRepository.updateAll(humanPartTertiary, where);
  }

  @get('/human-part-tertiaries/{id}')
  @response(200, {
    description: 'HumanPartTertiary model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(HumanPartTertiary, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(HumanPartTertiary, {exclude: 'where'}) filter?: FilterExcludingWhere<HumanPartTertiary>
  ): Promise<HumanPartTertiary> {
    return this.humanPartTertiaryRepository.findById(id, filter);
  }

  @patch('/human-part-tertiaries/{id}')
  @response(204, {
    description: 'HumanPartTertiary PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(HumanPartTertiary, {partial: true}),
        },
      },
    })
    humanPartTertiary: HumanPartTertiary,
  ): Promise<void> {
    await this.humanPartTertiaryRepository.updateById(id, humanPartTertiary);
  }

  @put('/human-part-tertiaries/{id}')
  @response(204, {
    description: 'HumanPartTertiary PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() humanPartTertiary: HumanPartTertiary,
  ): Promise<void> {
    await this.humanPartTertiaryRepository.replaceById(id, humanPartTertiary);
  }

  @del('/human-part-tertiaries/{id}')
  @response(204, {
    description: 'HumanPartTertiary DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.humanPartTertiaryRepository.deleteById(id);
  }
}
