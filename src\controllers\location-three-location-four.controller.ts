import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  HttpErrors,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  LocationThree,
  LocationFour,
} from '../models';
import { LocationThreeRepository, UserRepository, UserLocationRoleRepository } from '../repositories';

import { inject } from '@loopback/core';
import { SecurityBindings, securityId, UserProfile } from '@loopback/security';
import { authenticate } from '@loopback/authentication';

@authenticate('jwt')
export class LocationThreeLocationFourController {
  constructor(
    @repository(UserRepository) protected userRepository: UserRepository,
    @repository(UserLocationRoleRepository) protected userLocationRoleRepository: UserLocationRoleRepository,
    @repository(LocationThreeRepository) protected locationThreeRepository: LocationThreeRepository,
  ) { }

  @get('/all-location-threes/{id}/all-location-fours', {
    responses: {
      '200': {
        description: 'Array of LocationThree has many LocationFour',
        content: {
          'application/json': {
            schema: { type: 'array', items: getModelSchemaRef(LocationFour) },
          },
        },
      },
    },
  })
  async findAll(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<LocationFour>,
  ): Promise<LocationFour[]> {
    const email = '<EMAIL>'
    const user = await this.userRepository.findOne({ where: { email: email } });

    if (!user) {
      throw new HttpErrors.NotFound(`User not found with this email: ${email}`);
    }


    return this.locationThreeRepository.locationFours(id).find(filter);

  }

  @get('/location-threes/{id}/location-fours', {
    responses: {
      '200': {
        description: 'Array of LocationThree has many LocationFour',
        content: {
          'application/json': {
            schema: { type: 'array', items: getModelSchemaRef(LocationFour) },
          },
        },
      },
    },
  })
  async find(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<LocationFour>,
  ): Promise<LocationFour[]> {
    const email = '<EMAIL>'
    const user = await this.userRepository.findOne({ where: { email: email } });

    if (!user) {
      throw new HttpErrors.NotFound(`User not found with this email: ${email}`);
    }

    const userLocationRoles = await this.userLocationRoleRepository.find({ where: { userId: user.id, locationThreeId: id } });

    const locationFourIds = userLocationRoles.map((userLocationRole) => userLocationRole.locationFourId);
    const uniqueLocationFourIds = Array.from(new Set(locationFourIds));

    // If 'all' is one of the locationTwoIds, then we return all LocationTwos for the given LocationOne
    if (uniqueLocationFourIds.includes('tier4-all')) {
      return this.locationThreeRepository.locationFours(id).find(filter);
    } else {
      return this.locationThreeRepository.locationFours(id).find({
        where: { id: { inq: uniqueLocationFourIds } },
      });
    }
  }

  @post('/location-threes/{id}/location-fours', {
    responses: {
      '200': {
        description: 'LocationThree model instance',
        content: { 'application/json': { schema: getModelSchemaRef(LocationFour) } },
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof LocationThree.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(LocationFour, {
            title: 'NewLocationFourInLocationThree',
            exclude: ['id'],
            optional: ['locationThreeId']
          }),
        },
      },
    }) locationFour: Omit<LocationFour, 'id'>,
  ): Promise<LocationFour> {
    return this.locationThreeRepository.locationFours(id).create(locationFour);
  }

  @patch('/location-threes/{id}/location-fours', {
    responses: {
      '200': {
        description: 'LocationThree.LocationFour PATCH success count',
        content: { 'application/json': { schema: CountSchema } },
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(LocationFour, { partial: true }),
        },
      },
    })
    locationFour: Partial<LocationFour>,
    @param.query.object('where', getWhereSchemaFor(LocationFour)) where?: Where<LocationFour>,
  ): Promise<Count> {
    return this.locationThreeRepository.locationFours(id).patch(locationFour, where);
  }

  @del('/location-threes/{id}/location-fours', {
    responses: {
      '200': {
        description: 'LocationThree.LocationFour DELETE success count',
        content: { 'application/json': { schema: CountSchema } },
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(LocationFour)) where?: Where<LocationFour>,
  ): Promise<Count> {
    return this.locationThreeRepository.locationFours(id).delete(where);
  }
}
