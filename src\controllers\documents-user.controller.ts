import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
  import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
Documents,
UserDocumentAllocation,
User,
} from '../models';
import {DocumentsRepository} from '../repositories';

export class DocumentsUserController {
  constructor(
    @repository(DocumentsRepository) protected documentsRepository: DocumentsRepository,
  ) { }

  @get('/documents/{id}/users', {
    responses: {
      '200': {
        description: 'Array of Documents has many User through UserDocumentAllocation',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(User)},
          },
        },
      },
    },
  })
  async find(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<User>,
  ): Promise<User[]> {
    return this.documentsRepository.users(id).find(filter);
  }

  @post('/documents/{id}/users', {
    responses: {
      '200': {
        description: 'create a User model instance',
        content: {'application/json': {schema: getModelSchemaRef(User)}},
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof Documents.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(User, {
            title: 'NewUserInDocuments',
            exclude: ['id'],
          }),
        },
      },
    }) user: Omit<User, 'id'>,
  ): Promise<User> {
    return this.documentsRepository.users(id).create(user);
  }

  @patch('/documents/{id}/users', {
    responses: {
      '200': {
        description: 'Documents.User PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(User, {partial: true}),
        },
      },
    })
    user: Partial<User>,
    @param.query.object('where', getWhereSchemaFor(User)) where?: Where<User>,
  ): Promise<Count> {
    return this.documentsRepository.users(id).patch(user, where);
  }

  @del('/documents/{id}/users', {
    responses: {
      '200': {
        description: 'Documents.User DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(User)) where?: Where<User>,
  ): Promise<Count> {
    return this.documentsRepository.users(id).delete(where);
  }
}
