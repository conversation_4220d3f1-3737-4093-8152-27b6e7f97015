import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {GoodCatchRole} from '../models';
import {GoodCatchRoleRepository} from '../repositories';

export class GoodCatchRoleController {
  constructor(
    @repository(GoodCatchRoleRepository)
    public goodCatchRoleRepository : GoodCatchRoleRepository,
  ) {}

  @post('/good-catch-roles')
  @response(200, {
    description: 'GoodCatchRole model instance',
    content: {'application/json': {schema: getModelSchemaRef(GoodCatchRole)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(GoodCatchRole, {
            title: 'NewGoodCatchRole',
            exclude: ['id'],
          }),
        },
      },
    })
    goodCatchRole: Omit<GoodCatchRole, 'id'>,
  ): Promise<GoodCatchRole> {
    return this.goodCatchRoleRepository.create(goodCatchRole);
  }

  @get('/good-catch-roles/count')
  @response(200, {
    description: 'GoodCatchRole model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(GoodCatchRole) where?: Where<GoodCatchRole>,
  ): Promise<Count> {
    return this.goodCatchRoleRepository.count(where);
  }

  @get('/good-catch-roles')
  @response(200, {
    description: 'Array of GoodCatchRole model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(GoodCatchRole, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(GoodCatchRole) filter?: Filter<GoodCatchRole>,
  ): Promise<GoodCatchRole[]> {
    return this.goodCatchRoleRepository.find(filter);
  }

  @patch('/good-catch-roles')
  @response(200, {
    description: 'GoodCatchRole PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(GoodCatchRole, {partial: true}),
        },
      },
    })
    goodCatchRole: GoodCatchRole,
    @param.where(GoodCatchRole) where?: Where<GoodCatchRole>,
  ): Promise<Count> {
    return this.goodCatchRoleRepository.updateAll(goodCatchRole, where);
  }

  @get('/good-catch-roles/{id}')
  @response(200, {
    description: 'GoodCatchRole model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(GoodCatchRole, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(GoodCatchRole, {exclude: 'where'}) filter?: FilterExcludingWhere<GoodCatchRole>
  ): Promise<GoodCatchRole> {
    return this.goodCatchRoleRepository.findById(id, filter);
  }

  @patch('/good-catch-roles/{id}')
  @response(204, {
    description: 'GoodCatchRole PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(GoodCatchRole, {partial: true}),
        },
      },
    })
    goodCatchRole: GoodCatchRole,
  ): Promise<void> {
    await this.goodCatchRoleRepository.updateById(id, goodCatchRole);
  }

  @put('/good-catch-roles/{id}')
  @response(204, {
    description: 'GoodCatchRole PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() goodCatchRole: GoodCatchRole,
  ): Promise<void> {
    await this.goodCatchRoleRepository.replaceById(id, goodCatchRole);
  }

  @del('/good-catch-roles/{id}')
  @response(204, {
    description: 'GoodCatchRole DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.goodCatchRoleRepository.deleteById(id);
  }
}
