import {Entity, model, property} from '@loopback/repository';

@model({settings: {strict: false}})
export class StepTitle extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'string',
  })
  value?: string;

  @property({
    type: 'string',
  })
  createdAt?: string;

  @property({
    type: 'string',
  })
  unitId?: string;
  // Define well-known properties here

  // Indexer property to allow additional data
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [prop: string]: any;

  constructor(data?: Partial<StepTitle>) {
    super(data);
  }
}

export interface StepTitleRelations {
  // describe navigational properties here
}

export type StepTitleWithRelations = StepTitle & StepTitleRelations;
