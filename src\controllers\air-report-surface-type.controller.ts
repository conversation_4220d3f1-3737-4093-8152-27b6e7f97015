import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  AirReport,
  SurfaceType,
} from '../models';
import {AirReportRepository} from '../repositories';

export class AirReportSurfaceTypeController {
  constructor(
    @repository(AirReportRepository)
    public airReportRepository: AirReportRepository,
  ) { }

  @get('/air-reports/{id}/surface-type', {
    responses: {
      '200': {
        description: 'SurfaceType belonging to AirReport',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(SurfaceType)},
          },
        },
      },
    },
  })
  async getSurfaceType(
    @param.path.string('id') id: typeof AirReport.prototype.id,
  ): Promise<SurfaceType> {
    return this.airReportRepository.surfaceType(id);
  }
}
