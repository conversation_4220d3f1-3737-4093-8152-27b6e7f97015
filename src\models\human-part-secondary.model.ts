import {Entity, model, property, hasMany} from '@loopback/repository';
import {HumanPartTertiary} from './human-part-tertiary.model';

@model()
export class HumanPartSecondary extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'string',
  })
  humanPartPrimaryId?: string;

  @hasMany(() => HumanPartTertiary)
  humanPartTertiaries: HumanPartTertiary[];

  constructor(data?: Partial<HumanPartSecondary>) {
    super(data);
  }
}

export interface HumanPartSecondaryRelations {
  // describe navigational properties here
}

export type HumanPartSecondaryWithRelations = HumanPartSecondary & HumanPartSecondaryRelations;
