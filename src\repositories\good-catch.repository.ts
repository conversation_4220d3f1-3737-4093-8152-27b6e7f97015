import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, BelongsToAccessor, HasManyRepositoryFactory} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {GoodCatch, GoodCatchRelations, User, Action, LocationThree, LocationFour, WorkingGroup} from '../models';
import {UserRepository} from './user.repository';
import {ActionRepository} from './action.repository';
import {LocationThreeRepository} from './location-three.repository';
import {LocationFourRepository} from './location-four.repository';
import {WorkingGroupRepository} from './working-group.repository';

export class GoodCatchRepository extends DefaultCrudRepository<
  GoodCatch,
  typeof GoodCatch.prototype.id,
  GoodCatchRelations
> {

  public readonly reporter: BelongsToAccessor<User, typeof GoodCatch.prototype.id>;

  public readonly admin: BelongsToAccessor<User, typeof GoodCatch.prototype.id>;

  public readonly actionOwner: BelongsToAccessor<User, typeof GoodCatch.prototype.id>;

  public readonly actions: HasManyRepositoryFactory<Action, typeof GoodCatch.prototype.id>;

  public readonly locationThree: BelongsToAccessor<LocationThree, typeof GoodCatch.prototype.id>;

  public readonly locationFour: BelongsToAccessor<LocationFour, typeof GoodCatch.prototype.id>;

  public readonly workingGroup: BelongsToAccessor<WorkingGroup, typeof GoodCatch.prototype.id>;

  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource, @repository.getter('UserRepository') protected userRepositoryGetter: Getter<UserRepository>, @repository.getter('ActionRepository') protected actionRepositoryGetter: Getter<ActionRepository>, @repository.getter('LocationThreeRepository') protected locationThreeRepositoryGetter: Getter<LocationThreeRepository>, @repository.getter('LocationFourRepository') protected locationFourRepositoryGetter: Getter<LocationFourRepository>, @repository.getter('WorkingGroupRepository') protected workingGroupRepositoryGetter: Getter<WorkingGroupRepository>,
  ) {
    super(GoodCatch, dataSource);
    this.workingGroup = this.createBelongsToAccessorFor('workingGroup', workingGroupRepositoryGetter,);
    this.registerInclusionResolver('workingGroup', this.workingGroup.inclusionResolver);
    this.locationFour = this.createBelongsToAccessorFor('locationFour', locationFourRepositoryGetter,);
    this.registerInclusionResolver('locationFour', this.locationFour.inclusionResolver);
    this.locationThree = this.createBelongsToAccessorFor('locationThree', locationThreeRepositoryGetter,);
    this.registerInclusionResolver('locationThree', this.locationThree.inclusionResolver);
    this.actions = this.createHasManyRepositoryFactoryFor('actions', actionRepositoryGetter,);
    this.registerInclusionResolver('actions', this.actions.inclusionResolver);
    this.actionOwner = this.createBelongsToAccessorFor('actionOwner', userRepositoryGetter,);
    this.registerInclusionResolver('actionOwner', this.actionOwner.inclusionResolver);
    this.admin = this.createBelongsToAccessorFor('admin', userRepositoryGetter,);
    this.registerInclusionResolver('admin', this.admin.inclusionResolver);
    this.reporter = this.createBelongsToAccessorFor('reporter', userRepositoryGetter,);
    this.registerInclusionResolver('reporter', this.reporter.inclusionResolver);
  }
}
