import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasManyThroughRepositoryFactory, HasManyRepositoryFactory} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {Documents, DocumentsRelations, User, UserDocumentAllocation, Groups, GroupDocumentAllocation, DocumentUpdate} from '../models';
import {UserDocumentAllocationRepository} from './user-document-allocation.repository';
import {UserRepository} from './user.repository';
import {GroupDocumentAllocationRepository} from './group-document-allocation.repository';
import {GroupsRepository} from './groups.repository';
import {DocumentUpdateRepository} from './document-update.repository';

export class DocumentsRepository extends DefaultCrudRepository<
  Documents,
  typeof Documents.prototype.id,
  DocumentsRelations
> {

  public readonly users: HasManyThroughRepositoryFactory<User, typeof User.prototype.id,
          UserDocumentAllocation,
          typeof Documents.prototype.id
        >;

  public readonly groups: HasManyThroughRepositoryFactory<Groups, typeof Groups.prototype.id,
          GroupDocumentAllocation,
          typeof Documents.prototype.id
        >;

  public readonly documentUpdates: HasManyRepositoryFactory<DocumentUpdate, typeof Documents.prototype.id>;

  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource, @repository.getter('UserDocumentAllocationRepository') protected userDocumentAllocationRepositoryGetter: Getter<UserDocumentAllocationRepository>, @repository.getter('UserRepository') protected userRepositoryGetter: Getter<UserRepository>, @repository.getter('GroupDocumentAllocationRepository') protected groupDocumentAllocationRepositoryGetter: Getter<GroupDocumentAllocationRepository>, @repository.getter('GroupsRepository') protected groupsRepositoryGetter: Getter<GroupsRepository>, @repository.getter('DocumentUpdateRepository') protected documentUpdateRepositoryGetter: Getter<DocumentUpdateRepository>,
  ) {
    super(Documents, dataSource);
    this.documentUpdates = this.createHasManyRepositoryFactoryFor('documentUpdates', documentUpdateRepositoryGetter,);
    this.registerInclusionResolver('documentUpdates', this.documentUpdates.inclusionResolver);
    this.groups = this.createHasManyThroughRepositoryFactoryFor('groups', groupsRepositoryGetter, groupDocumentAllocationRepositoryGetter,);
    this.registerInclusionResolver('groups', this.groups.inclusionResolver);
    this.users = this.createHasManyThroughRepositoryFactoryFor('users', userRepositoryGetter, userDocumentAllocationRepositoryGetter,);
    this.registerInclusionResolver('users', this.users.inclusionResolver);
  }
}
