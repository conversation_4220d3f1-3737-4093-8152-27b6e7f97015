import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
  HttpErrors,
} from '@loopback/rest';
import { inject } from '@loopback/core';
import { SecurityBindings, securityId, UserProfile } from '@loopback/security';
import { ObservationReport, Action } from '../models';
import { ObservationReportRepository, ActionRepository, UserRepository, UserLocationRoleRepository } from '../repositories';
import { authenticate } from '@loopback/authentication';
import moment from "moment";
import { SqsService } from '../services/sqs-service.service';

@authenticate('jwt')
export class ObservationReportController {
  constructor(
    @repository(ObservationReportRepository)
    public observationReportRepository: ObservationReportRepository,
    @repository(ActionRepository)
    public actionRepository: ActionRepository,
    @repository(UserRepository)
    public userRepository: UserRepository,
    @repository(UserLocationRoleRepository)
    public userLocationRoleRepository: UserLocationRoleRepository,
    @inject('services.SqsService')
    public sqsService: SqsService,
  ) { }

  @post('/observation-reports')
  @response(200, {
    description: 'ObservationReport model instance',
    content: { 'application/json': { schema: getModelSchemaRef(ObservationReport) } },
  })
  async create(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ObservationReport, {
            title: 'NewObservationReport',
            exclude: ['id'],
          }),
        },
      },
    })
    observationReport: Omit<ObservationReport, 'id'>,
  ): Promise<ObservationReport> {
    const email = '<EMAIL>'
    const user = await this.userRepository.findOne({ where: { email: email } });
    if (user !== null && user !== undefined) {
      observationReport.submittedId = user.id;
    } else {
      observationReport.submittedId = '';
    }

    const count = await this.observationReportRepository.count();
    observationReport.maskId = `OBS-${moment().format('YYMMDD')}-${count.count + 1}`;
    observationReport.status = 'Captured';
    const obsReport = await this.observationReportRepository.create(observationReport);

    if (obsReport.type === 'At Risk' && obsReport.rectifiedStatus === 'No') {

      const actionItem = {
        application: "Observation",
        actionType: "hse_action",
        comments: obsReport.actionTaken,
        description: obsReport.description,
        dueDate: obsReport.dueDate,
        actionToBeTaken: obsReport.actionToBeTaken,
        status: "open",
        createdDate: moment(new Date()).toISOString(),
        objectId: obsReport.id,
        submittedById: user?.id,
        assignedToId: await this.getUserLocationRolesByRoleId('660173c3d6bc0f0356cb9b31')
      }


      await this.sendMailByRoleId('660173c3d6bc0f0356cb9b31', `Action to be taken on Observation ${observationReport.maskId}`, `Observation ${observationReport.maskId} is submitted by ${user?.firstName}. Action to be Taken: ${obsReport.actionToBeTaken}. Due Date: ${obsReport.dueDate}`)


      await this.actionRepository.create(actionItem)
      await this.observationReportRepository.updateById(obsReport.id, { status: 'Initiated' })
    }

    return obsReport;
  }

  @patch('/observation-reports/{id}/{actionId}')
  @response(204, {
    description: 'ObservationReport PATCH success',
  })
  async updateActionById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @param.path.string('actionId') actionId: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ObservationReport, { partial: true }),
        },
      },
    })
    observationReport: ObservationReport,
  ): Promise<void> {
    const email = '<EMAIL>'
    const user = await this.userRepository.findOne({ where: { email: email } });
    const obsReport = await this.observationReportRepository.findById(id);
    const actionItem = {
      application: "Observation",
      actionType: "action_owner",
      comments: obsReport.actionTaken,
      description: obsReport.description,
      dueDate: observationReport.dueDate,
      actionToBeTaken: obsReport.actionToBeTaken,
      status: "open",
      createdDate: moment(new Date()).toISOString(),
      objectId: obsReport.id,
      submittedById: user?.id,
      assignedToId: [observationReport.actionOwnerId]
    }

    if (observationReport.actionOwnerId) {
      const actionOwner = await this.userRepository.findById(observationReport.actionOwnerId);
      if (actionOwner) { await this.sqsService.sendMessage(actionOwner, `Action to be taken on Observation ${obsReport.maskId}`, `Observation ${obsReport.maskId} is submitted by ${user?.firstName}. Action to be Taken: ${obsReport.actionToBeTaken}. Due Date: ${observationReport.dueDate}`) } else { throw new HttpErrors.NotFound(`User not found. Try again`); }
    }

    await this.actionRepository.create(actionItem)
    await this.actionRepository.updateById(actionId, { createdDate: moment(new Date()).toISOString(), status: 'completed' })
    await this.observationReportRepository.updateById(id, observationReport);
  }


  @patch('/observation-reports-close/{id}/{actionId}')
  @response(204, {
    description: 'ObservationReport PATCH success',
  })
  async updateActionCloseById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @param.path.string('actionId') actionId: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ObservationReport, { partial: true }),
        },
      },
    })
    observationReport: ObservationReport,
  ): Promise<void> {
    const email = '<EMAIL>'
    const user = await this.userRepository.findOne({ where: { email: email } });
    await this.actionRepository.updateById(actionId, { status: 'completed' })
    observationReport.status = 'Closed'
    await this.observationReportRepository.updateById(id, observationReport);
  }

  @get('/observation-reports/count')
  @response(200, {
    description: 'ObservationReport model count',
    content: { 'application/json': { schema: CountSchema } },
  })
  async count(
    @param.where(ObservationReport) where?: Where<ObservationReport>,
  ): Promise<Count> {
    return this.observationReportRepository.count(where);
  }

  @get('/observation-reports')
  @response(200, {
    description: 'Array of ObservationReport model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(ObservationReport, { includeRelations: true }),
        },
      },
    },
  })
  async find(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.filter(ObservationReport) filter?: Filter<ObservationReport>,
  ): Promise<ObservationReport[]> {
    const email = '<EMAIL>'
    const user = await this.userRepository.findOne({ where: { email: email } });

    if (user) {
      return this.observationReportRepository.find({ ...filter, where: { submittedId: user?.id } });
    } else {
      throw new HttpErrors.NotFound('user not found')
    }

  }

  @get('/observation-reports-by-locations')
  @response(200, {
    description: 'Array of ObservationReport model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(ObservationReport, { includeRelations: true }),
        },
      },
    },
  })
  async findOther(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.filter(ObservationReport) filter?: Filter<ObservationReport>,
  ): Promise<ObservationReport[]> {
    const email = '<EMAIL>'
    const user = await this.userRepository.findOne({ where: { email: email } });

    if (user) {
      const userId = user?.id; // Assuming user?.id is a string
      const notInUserId = userId ? [userId] : [];
      return this.observationReportRepository.find({
        ...filter, where: {
          submittedId: { nin: notInUserId }
        }
      });
    } else {
      throw new HttpErrors.NotFound('user not found')
    }

  }



  @patch('/observation-reports')
  @response(200, {
    description: 'ObservationReport PATCH success count',
    content: { 'application/json': { schema: CountSchema } },
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ObservationReport, { partial: true }),
        },
      },
    })
    observationReport: ObservationReport,
    @param.where(ObservationReport) where?: Where<ObservationReport>,
  ): Promise<Count> {
    return this.observationReportRepository.updateAll(observationReport, where);
  }

  @get('/observation-reports/{id}')
  @response(200, {
    description: 'ObservationReport model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(ObservationReport, { includeRelations: true }),
      },
    },
  })
  async findById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @param.filter(ObservationReport, { exclude: 'where' }) filter?: FilterExcludingWhere<ObservationReport>
  ): Promise<ObservationReport> {
    return this.observationReportRepository.findById(id, filter);
  }

  @patch('/observation-reports/{id}')
  @response(204, {
    description: 'ObservationReport PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ObservationReport, { partial: true }),
        },
      },
    })
    observationReport: ObservationReport,
  ): Promise<void> {
    await this.observationReportRepository.updateById(id, observationReport);
  }

  @put('/observation-reports/{id}')
  @response(204, {
    description: 'ObservationReport PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() observationReport: ObservationReport,
  ): Promise<void> {
    await this.observationReportRepository.replaceById(id, observationReport);
  }

  @del('/observation-reports/{id}')
  @response(204, {
    description: 'ObservationReport DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.observationReportRepository.deleteById(id);
  }

  async getUserLocationRolesByRoleId(roleId: string) {
    let whereCondition = {
      roles: { inq: [[roleId]] },
      or: [
        {
          locationOneId: { inq: ['tier1-all'] },
          locationTwoId: '',
          locationThreeId: '',
          locationFourId: '',
        },
      ],
    };

    const userLocationRoles = await this.userLocationRoleRepository.find({
      where: whereCondition,
    });

    const userIds = userLocationRoles.map(userLocationRole => userLocationRole.userId);
    return Array.from(new Set(userIds));
  }

  async sendMailByRoleId(roleId: string, mailSubject: string, mailBody: string) {



    const allLocationId = 'all';

    let whereCondition = {
      roles: { inq: [[roleId]] },
      or: [

        {
          locationOneId: { inq: ['tier1-all'] },
          locationTwoId: '',
          locationThreeId: '',
          locationFourId: '',
        },

      ]
    };

    const userLocationRoles = await this.userLocationRoleRepository.find({
      where: whereCondition,
    });

    const userIds = userLocationRoles.map((userLocationRole) => userLocationRole.userId);
    const uniqueUserIds = Array.from(new Set(userIds));

    const users = await this.userRepository.find({
      where: { id: { inq: uniqueUserIds as string[] } },
    });

    if (users && users.length) {
      for (const user of users) {
        this.sqsService.sendMessage(user, mailSubject, mailBody);
      }
    } else {
      throw new HttpErrors.NotFound(`User not found. Try again`);
    }
  }
}

