import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, BelongsToAccessor} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {KnowledgeSessionRecord, KnowledgeSessionRecordRelations, User, Area, Topic, Unit} from '../models';
import {UserRepository} from './user.repository';
import {AreaRepository} from './area.repository';
import {TopicRepository} from './topic.repository';
import {UnitRepository} from './unit.repository';

export class KnowledgeSessionRecordRepository extends DefaultCrudRepository<
  KnowledgeSessionRecord,
  typeof KnowledgeSessionRecord.prototype.id,
  KnowledgeSessionRecordRelations
> {

  public readonly user: BelongsToAccessor<User, typeof KnowledgeSessionRecord.prototype.id>;

  public readonly area: BelongsToAccessor<Area, typeof KnowledgeSessionRecord.prototype.id>;

  public readonly topic: BelongsToAccessor<Topic, typeof KnowledgeSessionRecord.prototype.id>;

  public readonly unit: BelongsToAccessor<Unit, typeof KnowledgeSessionRecord.prototype.id>;

  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource, @repository.getter('UserRepository') protected userRepositoryGetter: Getter<UserRepository>, @repository.getter('AreaRepository') protected areaRepositoryGetter: Getter<AreaRepository>, @repository.getter('TopicRepository') protected topicRepositoryGetter: Getter<TopicRepository>, @repository.getter('UnitRepository') protected unitRepositoryGetter: Getter<UnitRepository>,
  ) {
    super(KnowledgeSessionRecord, dataSource);
    this.unit = this.createBelongsToAccessorFor('unit', unitRepositoryGetter,);
    this.registerInclusionResolver('unit', this.unit.inclusionResolver);
    this.topic = this.createBelongsToAccessorFor('topic', topicRepositoryGetter,);
    this.registerInclusionResolver('topic', this.topic.inclusionResolver);
    this.area = this.createBelongsToAccessorFor('area', areaRepositoryGetter,);
    this.registerInclusionResolver('area', this.area.inclusionResolver);
    this.user = this.createBelongsToAccessorFor('user', userRepositoryGetter,);
    this.registerInclusionResolver('user', this.user.inclusionResolver);
  }
}
