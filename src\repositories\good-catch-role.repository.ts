import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {GoodCatchRole, GoodCatchRoleRelations} from '../models';

export class GoodCatchRoleRepository extends DefaultCrudRepository<
  GoodCatchRole,
  typeof GoodCatchRole.prototype.id,
  GoodCatchRoleRelations
> {
  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource,
  ) {
    super(GoodCatchRole, dataSource);
  }
}
