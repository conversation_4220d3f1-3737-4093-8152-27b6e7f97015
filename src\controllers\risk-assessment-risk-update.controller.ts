import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  RiskAssessment,
  RiskUpdate,
} from '../models';
import { RiskAssessmentRepository,UserRepository } from '../repositories';
import moment from 'moment';
import { SqsService } from '../services/sqs-service.service';
import { inject } from '@loopback/core';

export class RiskAssessmentRiskUpdateController {
  constructor(
    @repository(RiskAssessmentRepository) protected riskAssessmentRepository: RiskAssessmentRepository,
    @repository(UserRepository) protected userRepository: UserRepository,
    @inject('services.SqsService')
    public sqsService: SqsService,
  ) { }

  @get('/risk-assessments/{id}/risk-updates', {
    responses: {
      '200': {
        description: 'Array of RiskAssessment has many RiskUpdate',
        content: {
          'application/json': {
            schema: { type: 'array', items: getModelSchemaRef(RiskUpdate) },
          },
        },
      },
    },
  })
  async find(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<RiskUpdate>,
  ): Promise<RiskUpdate[]> {
    return this.riskAssessmentRepository.riskUpdates(id).find(filter);
  }

  @post('/risk-assessments/{id}/risk-updates', {
    responses: {
      '200': {
        description: 'RiskAssessment model instance',
        content: { 'application/json': { schema: getModelSchemaRef(RiskUpdate) } },
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof RiskAssessment.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(RiskUpdate, {
            title: 'NewRiskUpdateInRiskAssessment',
            exclude: ['id'],
            optional: ['riskAssessmentId'],
          }),
        },
      },
    }) riskUpdate: Omit<RiskUpdate, 'id'>,
  ): Promise<RiskUpdate> {
    // Set the current UTC date and time
    riskUpdate.date = moment().utc().format('YYYY-MM-DD HH:mm');
  
    // Fetch the associated Risk Assessment
    const riskAssessment = await this.riskAssessmentRepository.findById(id);
  
    // Ensure `teamMemberInvolved` exists and is an array
    if (Array.isArray(riskAssessment.teamMemberInvolved)) {
      try {
        // Prepare email subject and body
        const mailSubject = `Changes to RA - ${riskAssessment.meetid}`;
        const mailBody = `RA Team Leader ${riskAssessment.captain} has made changes to the above RA. Please review the change and affirm your agreement for the change so that the RA can be formalized with the changes. If you are not in agreement with the change, please communicate directly with the RA Team Leader.`;
  
        // Filter out invalid team members and fetch user details
        const validMembers = riskAssessment.teamMemberInvolved.filter(record => record?.id);
        const users = await Promise.all(
          validMembers.map(record => this.userRepository.findById(record.id))
        );
  
        // Filter out users with missing email addresses
        const validUsers = users.filter(user => user?.email);
  
        // Send emails concurrently to valid users
        await Promise.all(
          validUsers.map(user =>
            this.sqsService.sendEmail(user.email, mailSubject, mailBody)
          )
        );
  
        console.log("Emails sent successfully to all valid team members.");
      } catch (error) {
        console.error("Error sending notifications:", error);
        // Optionally handle errors or log them
      }
    } else {
      console.warn("No team members involved in this Risk Assessment.");
    }
  
    // Save the Risk Update to the database
    return this.riskAssessmentRepository.riskUpdates(id).create(riskUpdate);
  }
  

  @patch('/risk-assessments/{id}/risk-updates', {
    responses: {
      '200': {
        description: 'RiskAssessment.RiskUpdate PATCH success count',
        content: { 'application/json': { schema: CountSchema } },
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(RiskUpdate, { partial: true }),
        },
      },
    })
    riskUpdate: Partial<RiskUpdate>,
    @param.query.object('where', getWhereSchemaFor(RiskUpdate)) where?: Where<RiskUpdate>,
  ): Promise<Count> {
    return this.riskAssessmentRepository.riskUpdates(id).patch(riskUpdate, where);
  }

  @del('/risk-assessments/{id}/risk-updates', {
    responses: {
      '200': {
        description: 'RiskAssessment.RiskUpdate DELETE success count',
        content: { 'application/json': { schema: CountSchema } },
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(RiskUpdate)) where?: Where<RiskUpdate>,
  ): Promise<Count> {
    return this.riskAssessmentRepository.riskUpdates(id).delete(where);
  }
}
