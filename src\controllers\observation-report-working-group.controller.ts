import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  ObservationReport,
  WorkingGroup,
} from '../models';
import {ObservationReportRepository} from '../repositories';

export class ObservationReportWorkingGroupController {
  constructor(
    @repository(ObservationReportRepository)
    public observationReportRepository: ObservationReportRepository,
  ) { }

  @get('/observation-reports/{id}/working-group', {
    responses: {
      '200': {
        description: 'WorkingGroup belonging to ObservationReport',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(WorkingGroup)},
          },
        },
      },
    },
  })
  async getWorkingGroup(
    @param.path.string('id') id: typeof ObservationReport.prototype.id,
  ): Promise<WorkingGroup> {
    return this.observationReportRepository.workingGroup(id);
  }
}
