import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  TierOne,
  TierTwo,
} from '../models';
import {TierOneRepository} from '../repositories';

export class TierOneTierTwoController {
  constructor(
    @repository(TierOneRepository) protected tierOneRepository: TierOneRepository,
  ) { }

  @get('/tier-ones/{id}/tier-twos', {
    responses: {
      '200': {
        description: 'Array of TierOne has many TierTwo',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(TierTwo)},
          },
        },
      },
    },
  })
  async find(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<TierTwo>,
  ): Promise<TierTwo[]> {
    return this.tierOneRepository.tierTwos(id).find(filter);
  }

  @post('/tier-ones/{id}/tier-twos', {
    responses: {
      '200': {
        description: 'TierOne model instance',
        content: {'application/json': {schema: getModelSchemaRef(TierTwo)}},
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof TierOne.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(TierTwo, {
            title: 'NewTierTwoInTierOne',
            exclude: ['id'],
            optional: ['tierOneId']
          }),
        },
      },
    }) tierTwo: Omit<TierTwo, 'id'>,
  ): Promise<TierTwo> {
    return this.tierOneRepository.tierTwos(id).create(tierTwo);
  }

  @patch('/tier-ones/{id}/tier-twos', {
    responses: {
      '200': {
        description: 'TierOne.TierTwo PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(TierTwo, {partial: true}),
        },
      },
    })
    tierTwo: Partial<TierTwo>,
    @param.query.object('where', getWhereSchemaFor(TierTwo)) where?: Where<TierTwo>,
  ): Promise<Count> {
    return this.tierOneRepository.tierTwos(id).patch(tierTwo, where);
  }

  @del('/tier-ones/{id}/tier-twos', {
    responses: {
      '200': {
        description: 'TierOne.TierTwo DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(TierTwo)) where?: Where<TierTwo>,
  ): Promise<Count> {
    return this.tierOneRepository.tierTwos(id).delete(where);
  }
}
