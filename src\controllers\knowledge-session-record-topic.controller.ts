import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  KnowledgeSessionRecord,
  Topic,
} from '../models';
import {KnowledgeSessionRecordRepository} from '../repositories';

export class KnowledgeSessionRecordTopicController {
  constructor(
    @repository(KnowledgeSessionRecordRepository)
    public knowledgeSessionRecordRepository: KnowledgeSessionRecordRepository,
  ) { }

  @get('/knowledge-session-records/{id}/topic', {
    responses: {
      '200': {
        description: 'Topic belonging to KnowledgeSessionRecord',
        content: {
          'application/json': {
            schema: getModelSchemaRef(Topic),
          },
        },
      },
    },
  })
  async getTopic(
    @param.path.string('id') id: typeof KnowledgeSessionRecord.prototype.id,
  ): Promise<Topic> {
    return this.knowledgeSessionRecordRepository.topic(id);
  }
}
