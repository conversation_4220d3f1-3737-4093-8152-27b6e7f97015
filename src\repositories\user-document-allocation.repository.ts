import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {UserDocumentAllocation, UserDocumentAllocationRelations} from '../models';

export class UserDocumentAllocationRepository extends DefaultCrudRepository<
  UserDocumentAllocation,
  typeof UserDocumentAllocation.prototype.id,
  UserDocumentAllocationRelations
> {
  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource,
  ) {
    super(UserDocumentAllocation, dataSource);
  }
}
