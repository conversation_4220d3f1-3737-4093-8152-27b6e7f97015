import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  LocationOne,
  PermitReport,
} from '../models';
import {LocationOneRepository} from '../repositories';

export class LocationOnePermitReportController {
  constructor(
    @repository(LocationOneRepository) protected locationOneRepository: LocationOneRepository,
  ) { }

  @get('/location-ones/{id}/permit-reports', {
    responses: {
      '200': {
        description: 'Array of LocationOne has many PermitReport',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(PermitReport)},
          },
        },
      },
    },
  })
  async find(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<PermitReport>,
  ): Promise<PermitReport[]> {
    return this.locationOneRepository.permitReports(id).find(filter);
  }

  @post('/location-ones/{id}/permit-reports', {
    responses: {
      '200': {
        description: 'LocationOne model instance',
        content: {'application/json': {schema: getModelSchemaRef(PermitReport)}},
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof LocationOne.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(PermitReport, {
            title: 'NewPermitReportInLocationOne',
            exclude: ['id'],
            optional: ['locationOneId']
          }),
        },
      },
    }) permitReport: Omit<PermitReport, 'id'>,
  ): Promise<PermitReport> {
    return this.locationOneRepository.permitReports(id).create(permitReport);
  }

  @patch('/location-ones/{id}/permit-reports', {
    responses: {
      '200': {
        description: 'LocationOne.PermitReport PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(PermitReport, {partial: true}),
        },
      },
    })
    permitReport: Partial<PermitReport>,
    @param.query.object('where', getWhereSchemaFor(PermitReport)) where?: Where<PermitReport>,
  ): Promise<Count> {
    return this.locationOneRepository.permitReports(id).patch(permitReport, where);
  }

  @del('/location-ones/{id}/permit-reports', {
    responses: {
      '200': {
        description: 'LocationOne.PermitReport DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(PermitReport)) where?: Where<PermitReport>,
  ): Promise<Count> {
    return this.locationOneRepository.permitReports(id).delete(where);
  }
}
