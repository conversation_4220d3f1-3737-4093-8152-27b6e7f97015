import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  User,
  GhsOne,
} from '../models';
import {UserRepository} from '../repositories';

export class UserGhsOneController {
  constructor(
    @repository(UserRepository)
    public userRepository: UserRepository,
  ) { }

  @get('/users/{id}/ghs-one', {
    responses: {
      '200': {
        description: 'GhsOne belonging to User',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(GhsOne)},
          },
        },
      },
    },
  })
  async getGhsOne(
    @param.path.string('id') id: typeof User.prototype.id,
  ): Promise<GhsOne> {
    return this.userRepository.ghsOne(id);
  }
}
