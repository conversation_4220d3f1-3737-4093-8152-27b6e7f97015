import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  Documents,
  DocumentUpdate,
} from '../models';
import {DocumentsRepository} from '../repositories';

export class DocumentsDocumentUpdateController {
  constructor(
    @repository(DocumentsRepository) protected documentsRepository: DocumentsRepository,
  ) { }

  @get('/documents/{id}/document-updates', {
    responses: {
      '200': {
        description: 'Array of Documents has many DocumentUpdate',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(DocumentUpdate)},
          },
        },
      },
    },
  })
  async find(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<DocumentUpdate>,
  ): Promise<DocumentUpdate[]> {
    return this.documentsRepository.documentUpdates(id).find(filter);
  }

  @post('/documents/{id}/document-updates', {
    responses: {
      '200': {
        description: 'Documents model instance',
        content: {'application/json': {schema: getModelSchemaRef(DocumentUpdate)}},
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof Documents.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(DocumentUpdate, {
            title: 'NewDocumentUpdateInDocuments',
            exclude: ['id'],
            optional: ['documentsId']
          }),
        },
      },
    }) documentUpdate: Omit<DocumentUpdate, 'id'>,
  ): Promise<DocumentUpdate> {
    return this.documentsRepository.documentUpdates(id).create(documentUpdate);
  }

  @patch('/documents/{id}/document-updates', {
    responses: {
      '200': {
        description: 'Documents.DocumentUpdate PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(DocumentUpdate, {partial: true}),
        },
      },
    })
    documentUpdate: Partial<DocumentUpdate>,
    @param.query.object('where', getWhereSchemaFor(DocumentUpdate)) where?: Where<DocumentUpdate>,
  ): Promise<Count> {
    return this.documentsRepository.documentUpdates(id).patch(documentUpdate, where);
  }

  @del('/documents/{id}/document-updates', {
    responses: {
      '200': {
        description: 'Documents.DocumentUpdate DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(DocumentUpdate)) where?: Where<DocumentUpdate>,
  ): Promise<Count> {
    return this.documentsRepository.documentUpdates(id).delete(where);
  }
}
