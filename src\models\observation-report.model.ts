import { Entity, model, property, belongsTo, hasMany } from '@loopback/repository';
import { WorkActivity } from './work-activity.model';
import { GhsOne } from './ghs-one.model';
import { GhsTwo } from './ghs-two.model';
import { LocationOne } from './location-one.model';
import { LocationTwo } from './location-two.model';
import { LocationThree } from './location-three.model';
import { LocationFour } from './location-four.model';
import { LocationFive } from './location-five.model';
import { LocationSix } from './location-six.model';
import { Action } from './action.model';
import { WorkingGroup } from './working-group.model';
import {User} from './user.model';
import {ObservationType} from './observation-type.model';

@model()
export class ObservationReport extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  type?: string;

  @property({
    type: 'boolean',
  })
  isArchive?: boolean;

  @property({
    type: 'string',
  })
  category?: string;

  @property({
    type: 'string',
  })
  description?: string;

  @property({
    type: 'string',
  })
  rectifiedStatus?: string;

  @property({
    type: 'array',
    itemType: 'string',
  })
  evidence?: string[];

  @property({
    type: 'array',
    itemType: 'string',
  })
  uploads?: string[];

  @property({
    type: 'array',
    itemType: 'string',
  })
  participantUploads?: string[];

  @property({
    type: 'array',
    itemType: 'object',
  })
  participants?: object[];

  @property({
    type: 'string',
  })
  remarks?: string;

  @property({
    type: 'string',
  })
  actionTaken?: string;

  @property({
    type: 'string',
  })
  actionToBeTaken?: string;

  @property({
    type: 'string',
  })
  dueDate?: string;

  @property({
    type: 'string',
  })
  status?: string;
  @property({
    type: 'string',
  })
  severity?: string;
  @property({
    type: 'string',
  })
  actionOwnerId?: string;


  @property({
    type: 'string',
  })
  reviewerId?: string;

  @property({
    type: 'string',
  })
  created?: string;

  @property({
    type: 'string',
  })
  modified?: string;

  @property({
    type: 'string',
  })
  maskId?: string;

  @belongsTo(() => WorkActivity)
  workActivityId: string;
  @belongsTo(() => GhsOne)
  ghsOneId: string;

  @belongsTo(() => GhsTwo)
  ghsTwoId: string;
  @belongsTo(() => LocationOne)
  locationOneId: string;

  @belongsTo(() => LocationTwo)
  locationTwoId: string;

  @belongsTo(() => LocationThree)
  locationThreeId: string;

  @belongsTo(() => LocationFour)
  locationFourId: string;

  @belongsTo(() => LocationFive)
  locationFiveId: string;

  @belongsTo(() => LocationSix)
  locationSixId: string;

  @hasMany(() => Action, {keyTo: 'objectId'})
  actions: Action[];

  @belongsTo(() => WorkingGroup)
  workingGroupId: string;

  @belongsTo(() => GhsOne)
  workActivityDepartmentId: string;

  @belongsTo(() => User)
  submittedId: string;

  @belongsTo(() => ObservationType)
  observationTypeId: string;

  constructor(data?: Partial<ObservationReport>) {
    super(data);
  }
}

export interface ObservationReportRelations {
  // describe navigational properties here
}

export type ObservationReportWithRelations = ObservationReport & ObservationReportRelations;
