import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasManyRepositoryFactory, HasManyThroughRepositoryFactory} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {Unit, UnitRelations, StepTitle, User, UserUnitAllocation, Groups, GroupUnit} from '../models';
import {StepTitleRepository} from './step-title.repository';
import {UserUnitAllocationRepository} from './user-unit-allocation.repository';
import {UserRepository} from './user.repository';
import {GroupUnitRepository} from './group-unit.repository';
import {GroupsRepository} from './groups.repository';

export class UnitRepository extends DefaultCrudRepository<
  Unit,
  typeof Unit.prototype.id,
  UnitRelations
> {

  public readonly stepTitles: HasManyRepositoryFactory<StepTitle, typeof Unit.prototype.id>;

  public readonly users: HasManyThroughRepositoryFactory<User, typeof User.prototype.id,
          UserUnitAllocation,
          typeof Unit.prototype.id
        >;

  public readonly groups: HasManyThroughRepositoryFactory<Groups, typeof Groups.prototype.id,
          GroupUnit,
          typeof Unit.prototype.id
        >;

  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource, @repository.getter('StepTitleRepository') protected stepTitleRepositoryGetter: Getter<StepTitleRepository>, @repository.getter('UserUnitAllocationRepository') protected userUnitAllocationRepositoryGetter: Getter<UserUnitAllocationRepository>, @repository.getter('UserRepository') protected userRepositoryGetter: Getter<UserRepository>, @repository.getter('GroupUnitRepository') protected groupUnitRepositoryGetter: Getter<GroupUnitRepository>, @repository.getter('GroupsRepository') protected groupsRepositoryGetter: Getter<GroupsRepository>,
  ) {
    super(Unit, dataSource);
    this.groups = this.createHasManyThroughRepositoryFactoryFor('groups', groupsRepositoryGetter, groupUnitRepositoryGetter,);
    this.registerInclusionResolver('groups', this.groups.inclusionResolver);
    this.users = this.createHasManyThroughRepositoryFactoryFor('users', userRepositoryGetter, userUnitAllocationRepositoryGetter,);
    this.registerInclusionResolver('users', this.users.inclusionResolver);
    this.stepTitles = this.createHasManyRepositoryFactoryFor('stepTitles', stepTitleRepositoryGetter,);
    this.registerInclusionResolver('stepTitles', this.stepTitles.inclusionResolver);
  }
}
