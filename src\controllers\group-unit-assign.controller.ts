import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {GroupUnit} from '../models';
import {GroupUnitRepository,GroupsRepository,UserUnitAllocationRepository,GroupUserRepository} from '../repositories';

export type Three = {
  tierThreeId: [],
  groupid: string,


};
export class GroupUnitAssignController {
  constructor(
    @repository(GroupUnitRepository)
    public groupUnitRepository : GroupUnitRepository,
    @repository(GroupsRepository)
    public groupRepository: GroupsRepository,
    @repository(GroupUserRepository)
    public groupUserRepository: GroupUserRepository,
    @repository(UserUnitAllocationRepository)
    public userUnitAllocationRepository :UserUnitAllocationRepository,
  ) {}

  @post('/group-units')
  @response(200, {
    description: 'GroupUnit model instance',
    content: {'application/json': {schema: getModelSchemaRef(GroupUnit)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(GroupUnit, {
            title: 'NewGroupUnit',
            exclude: ['id'],
          }),
        },
      },
    })
    groupUnit: Omit<GroupUnit, 'id'>,
  ): Promise<GroupUnit> {
    return this.groupUnitRepository.create(groupUnit);
  }

  @get('/group-units/count')
  @response(200, {
    description: 'GroupUnit model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(GroupUnit) where?: Where<GroupUnit>,
  ): Promise<Count> {
    return this.groupUnitRepository.count(where);
  }

  @get('/group-units')
  @response(200, {
    description: 'Array of GroupUnit model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(GroupUnit, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(GroupUnit) filter?: Filter<GroupUnit>,
  ): Promise<GroupUnit[]> {
    return this.groupUnitRepository.find(filter);
  }

  @patch('/group-units')
  @response(200, {
    description: 'GroupUnit PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(GroupUnit, {partial: true}),
        },
      },
    })
    groupUnit: GroupUnit,
    @param.where(GroupUnit) where?: Where<GroupUnit>,
  ): Promise<Count> {
    return this.groupUnitRepository.updateAll(groupUnit, where);
  }

  @get('/group-units/{id}')
  @response(200, {
    description: 'GroupUnit model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(GroupUnit, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(GroupUnit, {exclude: 'where'}) filter?: FilterExcludingWhere<GroupUnit>
  ): Promise<GroupUnit> {
    return this.groupUnitRepository.findById(id, filter);
  }

  @patch('/group-units/{id}')
  @response(204, {
    description: 'GroupUnit PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(GroupUnit, {partial: true}),
        },
      },
    })
    groupUnit: GroupUnit,
  ): Promise<void> {
    await this.groupUnitRepository.updateById(id, groupUnit);
  }

  @put('/group-units/{id}')
  @response(204, {
    description: 'GroupUnit PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() groupUnit: GroupUnit,
  ): Promise<void> {
    await this.groupUnitRepository.replaceById(id, groupUnit);
  }

  @del('/group-units/{id}')
  @response(204, {
    description: 'GroupUnit DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.groupUnitRepository.deleteById(id);
  }
  @get('/group-tier-threes-groupid/{id}')
  @response(200, {
    description: 'GroupAssignTierThree model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(GroupUnit, {includeRelations: true}),
      },
    },
  })
  async findGroupById(
    @param.path.string('id') id: string,
    @param.filter(GroupUnit, {exclude: 'where'}) filter?: FilterExcludingWhere<GroupUnit>
  ): Promise<GroupUnit[]> {
    return this.groupUnitRepository.find({
      where: {
        groupsId: id
      }
    });
  }

  @post('/add-ku-group-user-threes')
  @response(200, {
    description: 'UserTierThree model instance'
  })
  async userThrees(
    @requestBody() threeOne: Three,
  ): Promise<void> {
    if (threeOne.tierThreeId.length === 0) {
      // return this.groupUserRepository.create(groupUser);
      const user = await this.groupRepository.users(threeOne.groupid).find()
      await this.groupUnitRepository.deleteAll({
        and: [
          {groupsId: threeOne.groupid},

        ],

      })
      user.map(async item => {
        await this.userUnitAllocationRepository.deleteAll({
          and: [
            
            {userId: item.id},
            {groupId: threeOne.groupid},
            {type: 'group'}
          ],

        })
      })



    }
    else {

      // group allocation part
      const kaOut: (string | undefined)[] = threeOne.tierThreeId;


      const alreadyRa = await this.groupUnitRepository.find({
        where: {
          "groupsId": threeOne.groupid
        }
      })
      const kaIn = alreadyRa.map(item => {
        return item.unitId
      })
      const convertedIntArray: (string | undefined)[] = kaIn.map(num => String(num));


      const insertA = kaOut.filter(item => !convertedIntArray.includes(item));

      if (insertA.length !== 0) {
        insertA.map(async i => {
          await this.groupUnitRepository.create({"groupsId": threeOne.groupid,  "unitId": i});

        })

      }
      const deleteA = convertedIntArray.filter(item => !kaOut.includes(item))

      if (deleteA.length !== 0) {
        deleteA.map(async i => {
          await this.groupUnitRepository.deleteAll({
            and: [
           
              {unitId: i},
              {groupsId: threeOne.groupid}
            ],
          });

        })

      }


      //Unit allocation part
      
      const user = await this.groupRepository.users(threeOne.groupid).find();

      if (user.length !== 0) {
        user.map(async item => {
          const alreadyKu = await this.userUnitAllocationRepository.find({
            where: {
              userId: item.id
            }
          })
          const kaUserIn = alreadyKu.map(k => {
            return k.unitId
          })
          const convertedInt: (string | undefined)[] = kaUserIn.map(num => String(num));


          const insertKaUser = kaOut.filter(it => !convertedInt.includes(it));


          if (insertKaUser.length !== 0) {
            insertKaUser.map(async i => {
              await this.userUnitAllocationRepository.create({
                "userId": item.id,
                "unitId": i,
                "groupId": threeOne.groupid,
                "type": 'group'
              });

            })
          }
          const deleteKaUser = convertedInt.filter(ite => !kaOut.includes(ite));
          if (deleteKaUser.length !== 0) {
            deleteKaUser.map(async i => {
              await this.userUnitAllocationRepository.deleteAll({
                and: [
                 
                  {groupId: threeOne.groupid},
                  {unitId: i},
                  {userId: item.id},
                  {type: 'group'}
                ],
              });

            })
          }

        })
      }


    }


  }
}
