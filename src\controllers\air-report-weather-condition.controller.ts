import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  AirReport,
  WeatherCondition,
} from '../models';
import {AirReportRepository} from '../repositories';

export class AirReportWeatherConditionController {
  constructor(
    @repository(AirReportRepository)
    public airReportRepository: AirReportRepository,
  ) { }

  @get('/air-reports/{id}/weather-condition', {
    responses: {
      '200': {
        description: 'WeatherCondition belonging to AirReport',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(WeatherCondition)},
          },
        },
      },
    },
  })
  async getWeatherCondition(
    @param.path.string('id') id: typeof AirReport.prototype.id,
  ): Promise<WeatherCondition> {
    return this.airReportRepository.weatherCondition(id);
  }
}
