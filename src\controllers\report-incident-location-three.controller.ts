import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  ReportIncident,
  LocationThree,
} from '../models';
import {ReportIncidentRepository} from '../repositories';

export class ReportIncidentLocationThreeController {
  constructor(
    @repository(ReportIncidentRepository)
    public reportIncidentRepository: ReportIncidentRepository,
  ) { }

  @get('/report-incidents/{id}/location-three', {
    responses: {
      '200': {
        description: 'LocationThree belonging to ReportIncident',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(LocationThree)},
          },
        },
      },
    },
  })
  async getLocationThree(
    @param.path.string('id') id: typeof ReportIncident.prototype.id,
  ): Promise<LocationThree> {
    return this.reportIncidentRepository.locationThree(id);
  }
}
