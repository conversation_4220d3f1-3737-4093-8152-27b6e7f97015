import { Entity, model, property } from '@loopback/repository';

@model()
export class Action extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  application?: string;

  @property({
    type: 'string',
  })
  actionType?: string;


  @property({
    type: 'string',
  })
  comments?: string;

  @property({
    type: 'string',
  })
  actionTaken?: string;

  @property({
    type: 'string',
  })
  actionToBeTaken?: string;

  @property({
    type: 'array',
    itemType: 'string',
  })
  uploads?: string[];

  @property({
    type: 'string',
  })
  description?: string;

  @property({
    type: 'string',
  })
  dueDate?: string;


  @property({
    type: 'string',
  })
  additionalDetails?: string;

  @property({
    type: 'string',
  })
  status?: string;

  @property({
    type: 'string',
  })
  createdDate?: string;

  @property({
    type: 'string',
  })
  objectId?: string;

  @property({
    type: 'string',
  })
  submittedById?: string;

  @property({
    type: 'array',
    itemType: 'string'
  })
  assignedToId?: string[];

  @property({
    type: 'string',
  })
  goodCatchId?: string;

  constructor(data?: Partial<Action>) {
    super(data);
  }
}

export interface ActionRelations {
  // describe navigational properties here
}

export type ActionWithRelations = Action & ActionRelations;
