import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {Groups} from '../models';
import {GroupsRepository} from '../repositories';
import moment from 'moment';

export class GroupsController {
  constructor(
    @repository(GroupsRepository)
    public groupsRepository : GroupsRepository,
  ) {}

  @post('/groups')
  @response(200, {
    description: 'Groups model instance',
    content: {'application/json': {schema: getModelSchemaRef(Groups)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Groups, {
            title: 'NewGroups',
            exclude: ['id'],
          }),
        },
      },
    })
    groups: Omit<Groups, 'id'>,
  ): Promise<Groups> {
    groups.createdAt =moment().utc().format('YYYY-MM-DD HH:mm');
    return this.groupsRepository.create(groups);
  }

  @get('/groups/count')
  @response(200, {
    description: 'Groups model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(Groups) where?: Where<Groups>,
  ): Promise<Count> {
    return this.groupsRepository.count(where);
  }

  @get('/groups')
  @response(200, {
    description: 'Array of Groups model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(Groups, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(Groups) filter?: Filter<Groups>,
  ): Promise<Groups[]> {
    return this.groupsRepository.find(filter);
  }

  @patch('/groups')
  @response(200, {
    description: 'Groups PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Groups, {partial: true}),
        },
      },
    })
    groups: Groups,
    @param.where(Groups) where?: Where<Groups>,
  ): Promise<Count> {
    return this.groupsRepository.updateAll(groups, where);
  }

  @get('/groups/{id}')
  @response(200, {
    description: 'Groups model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(Groups, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(Groups, {exclude: 'where'}) filter?: FilterExcludingWhere<Groups>
  ): Promise<Groups> {
    return this.groupsRepository.findById(id, filter);
  }

  @patch('/groups/{id}')
  @response(204, {
    description: 'Groups PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Groups, {partial: true}),
        },
      },
    })
    groups: Groups,
  ): Promise<void> {
    await this.groupsRepository.updateById(id, groups);
  }

  @put('/groups/{id}')
  @response(204, {
    description: 'Groups PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() groups: Groups,
  ): Promise<void> {
    await this.groupsRepository.replaceById(id, groups);
  }

  @del('/groups/{id}')
  @response(204, {
    description: 'Groups DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.groupsRepository.deleteById(id);
  }
}
