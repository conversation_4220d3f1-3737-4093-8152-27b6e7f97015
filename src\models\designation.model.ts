import { Entity, model, property } from '@loopback/repository';

@model()
export class Designation extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'number',
  })
  order?: number;

  @property({
    type: 'string',
  })
  created?: string;

  @property({
    type: 'string',
  })
  modified?: string;
  constructor(data?: Partial<Designation>) {
    super(data);
  }
}

export interface DesignationRelations {
  // describe navigational properties here
}

export type DesignationWithRelations = Designation & DesignationRelations;
