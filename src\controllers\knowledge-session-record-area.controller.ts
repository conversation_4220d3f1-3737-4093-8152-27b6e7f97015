import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  KnowledgeSessionRecord,
  Area,
} from '../models';
import {KnowledgeSessionRecordRepository} from '../repositories';

export class KnowledgeSessionRecordAreaController {
  constructor(
    @repository(KnowledgeSessionRecordRepository)
    public knowledgeSessionRecordRepository: KnowledgeSessionRecordRepository,
  ) { }

  @get('/knowledge-session-records/{id}/area', {
    responses: {
      '200': {
        description: 'Area belonging to KnowledgeSessionRecord',
        content: {
          'application/json': {
            schema: getModelSchemaRef(Area),
          },
        },
      },
    },
  })
  async getArea(
    @param.path.string('id') id: typeof KnowledgeSessionRecord.prototype.id,
  ): Promise<Area> {
    return this.knowledgeSessionRecordRepository.area(id);
  }
}
