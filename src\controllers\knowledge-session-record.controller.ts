import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import { authenticate } from '@loopback/authentication';
import { OPERATION_SECURITY_SPEC } from '@loopback/authentication-jwt';
import { SecurityBindings, UserProfile, securityId } from '@loopback/security';
import { KnowledgeSessionRecord, User } from '../models';
import { KnowledgeSessionRecordRepository, AreaRepository, TopicRepository, GroupsRepository, UnitRepository, StepTitleRepository, UserUnitAllocationRepository, UserRepository } from '../repositories';
import { inject } from '@loopback/core';
import moment from 'moment';
import { UUIDGeneratorService } from '../services';
import { UserProfileSchema } from './specs/user-controller.specs';
type KnowledgeSessionStart = {
  tierThreeId: string,

}
type KnowledgeSessionEnd = {
  sessionId: string,
  stepsData: string,
  duration: string,
  address: string
}
export class KnowledgeSessionRecordController {
  constructor(
    @repository(KnowledgeSessionRecordRepository)
    public knowledgeSessionRecordRepository: KnowledgeSessionRecordRepository,
    @repository(AreaRepository)
    public areaRepository: AreaRepository,
    @repository(TopicRepository)
    public topicRepository: TopicRepository,
    @repository(UnitRepository)
    public unitRepository: UnitRepository,
    @repository(GroupsRepository)
    public groupRepository: GroupsRepository,
    @repository(UserRepository)
    public userRepository: UserRepository,
    @inject('services.UUIDGeneratorService')
    protected uuidGeneratorService: UUIDGeneratorService,
    @repository(StepTitleRepository)
    public stepRepository: StepTitleRepository,
    @repository(UserUnitAllocationRepository)
    public userUnitAllocationRepository: UserUnitAllocationRepository
  ) { }

  @post('/knowledge-session-records')
  @response(200, {
    description: 'KnowledgeSessionRecord model instance',
    content: { 'application/json': { schema: getModelSchemaRef(KnowledgeSessionRecord) } },
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(KnowledgeSessionRecord, {
            title: 'NewKnowledgeSessionRecord',
            exclude: ['id'],
          }),
        },
      },
    })
    knowledgeSessionRecord: Omit<KnowledgeSessionRecord, 'id'>,
  ): Promise<KnowledgeSessionRecord> {
    return this.knowledgeSessionRecordRepository.create(knowledgeSessionRecord);
  }

  @get('/knowledge-session-records/count')
  @response(200, {
    description: 'KnowledgeSessionRecord model count',
    content: { 'application/json': { schema: CountSchema } },
  })
  async count(
    @param.where(KnowledgeSessionRecord) where?: Where<KnowledgeSessionRecord>,
  ): Promise<Count> {
    return this.knowledgeSessionRecordRepository.count(where);
  }

  @get('/knowledge-session-records')
  @response(200, {
    description: 'Array of KnowledgeSessionRecord model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(KnowledgeSessionRecord, { includeRelations: true }),
        },
      },
    },
  })
  async find(
    @param.filter(KnowledgeSessionRecord) filter?: Filter<KnowledgeSessionRecord>,
  ): Promise<KnowledgeSessionRecord[]> {
    return this.knowledgeSessionRecordRepository.find(filter);
  }

  @patch('/knowledge-session-records')
  @response(200, {
    description: 'KnowledgeSessionRecord PATCH success count',
    content: { 'application/json': { schema: CountSchema } },
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(KnowledgeSessionRecord, { partial: true }),
        },
      },
    })
    knowledgeSessionRecord: KnowledgeSessionRecord,
    @param.where(KnowledgeSessionRecord) where?: Where<KnowledgeSessionRecord>,
  ): Promise<Count> {
    return this.knowledgeSessionRecordRepository.updateAll(knowledgeSessionRecord, where);
  }

  @get('/knowledge-session-records/{id}')
  @response(200, {
    description: 'KnowledgeSessionRecord model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(KnowledgeSessionRecord, { includeRelations: true }),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(KnowledgeSessionRecord, { exclude: 'where' }) filter?: FilterExcludingWhere<KnowledgeSessionRecord>
  ): Promise<KnowledgeSessionRecord> {
    return this.knowledgeSessionRecordRepository.findById(id, filter);
  }

  @patch('/knowledge-session-records/{id}')
  @response(204, {
    description: 'KnowledgeSessionRecord PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(KnowledgeSessionRecord, { partial: true }),
        },
      },
    })
    knowledgeSessionRecord: KnowledgeSessionRecord,
  ): Promise<void> {
    await this.knowledgeSessionRecordRepository.updateById(id, knowledgeSessionRecord);
  }

  @put('/knowledge-session-records/{id}')
  @response(204, {
    description: 'KnowledgeSessionRecord PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() knowledgeSessionRecord: KnowledgeSessionRecord,
  ): Promise<void> {
    await this.knowledgeSessionRecordRepository.replaceById(id, knowledgeSessionRecord);
  }

  @del('/knowledge-session-records/{id}')
  @response(204, {
    description: 'KnowledgeSessionRecord DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.knowledgeSessionRecordRepository.deleteById(id);
  }

  @get('/dashboardka-data', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'The current user profile',
        content: {
          'application/json': {
            schema: UserProfileSchema,
          },
        },
      },
    },
  })
  @authenticate('jwt')
  async dashfind(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
  ): Promise<Object> {
    const { id } = currentUserProfile;

    const tierOne = await this.areaRepository.find();
    const tierOneId = tierOne.map(item => {
      return item.id
    })
    const tierTwo = await this.topicRepository.find(
      {
        where: {
          areaId: { inq: tierOneId }, // Filter products by categories
        }
      })
    const tierTwoId = tierTwo.map(item => {
      return item.id
    })
    const tierThree = await this.unitRepository.find(
      {
        where: {
          topicId: { inq: tierTwoId }, // Filter products by categories
        }
      })

    // const group = await this.groupRepository.find()

    // // const users = await this.enterpriseRepository.users(enterpriseId).find();
    // const document = await this.enterpriseRepository.documents(enterpriseId).find();
    // const form = await this.enterpriseRepository.forms(enterpriseId).find();
    // const checklist = await this.enterpriseRepository.checklists(enterpriseId).find();


    return { tierOne: tierOneId.length, tierTwo: tierTwoId.length, tierThree: tierThree.length }
  }




  @get('/knowledge-session-threeid/{id}')
  @response(200, {
    security: OPERATION_SECURITY_SPEC,
    description: 'KnowledgeSessionRecord model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(KnowledgeSessionRecord, { includeRelations: true }),
      },
    },
  })
  @authenticate('jwt')
  async findByKUId(
    @param.path.string('id') id: string,
    @param.filter(KnowledgeSessionRecord, { exclude: 'where' }) filter?: FilterExcludingWhere<KnowledgeSessionRecord>
  ): Promise<KnowledgeSessionRecord[]> {
    return this.knowledgeSessionRecordRepository.find({
      where: {
        unitId: id
      },
      include: [
        { relation: 'user' },
        { relation: 'area' },
        { relation: 'topic' },
        { relation: 'unit' }
      ]
    });
  }
  @get('/knowledge-session-userid/{id}')
  @response(200, {
    security: OPERATION_SECURITY_SPEC,
    description: 'KnowledgeSessionRecord model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(KnowledgeSessionRecord, { includeRelations: true }),
      },
    },
  })
  @authenticate('jwt')
  async findByUserId(
    @param.path.string('id') id: string,
    @param.filter(KnowledgeSessionRecord, { exclude: 'where' }) filter?: FilterExcludingWhere<KnowledgeSessionRecord>
  ): Promise<KnowledgeSessionRecord[]> {
    return this.knowledgeSessionRecordRepository.find({
      where: {
        userId: id
      },
      include: [
        { relation: 'user' },
        { relation: 'area' },
        { relation: 'topic' },
        { relation: 'unit' }
      ]
    });
  }
  @post('/knowledge-session-start')
  @response(200, {
    security: OPERATION_SECURITY_SPEC,
    description: 'KnowledgeSessionRecord model instance',
    content: { 'application/json': { schema: getModelSchemaRef(KnowledgeSessionRecord) } },
  })
  @authenticate('jwt')
  async knowledgeSessionStart(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(KnowledgeSessionRecord, {
            title: 'NewKnowledgeSessionRecord',
            exclude: ['id'],
          }),
        },
      },
    })
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    knowledgeSessionRecord: KnowledgeSessionStart,
  ): Promise<object> {
    const uniqueId = this.uuidGeneratorService.generateUUID();
    const email = '<EMAIL>'
    let id = ''
    const user: User | null = await this.userRepository.findOne({ where: { email: email } })
    if (user !== null) {
      id = user.id;
    } else {

    }
    const currentDateTimeUtc = moment.utc();
    const currentDate = currentDateTimeUtc.format('YYYY-MM-DD HH:mm:ss')

    const getThree = await this.unitRepository.find({
      where: {
        id: knowledgeSessionRecord.tierThreeId
      }
    });
    // console.log(getThree);
    const tierTwoId: (string | undefined)[] = getThree.map(item => {
      return item.topicId
    })

    const getTwo = await this.topicRepository.find({
      where: {
        id: tierTwoId[0]
      }
    });
    const tierOneId: (string | undefined)[] = getTwo.map(item => {
      return item.areaId
    })




    const risk = await this.userUnitAllocationRepository.findOne({
      where: {
        and: [
          { userId: id },
          { unitId: knowledgeSessionRecord.tierThreeId },
        ],
      }
    });

    let step: any = []
    if (risk) {
      step = await this.stepRepository.find({
        where: { unitId: knowledgeSessionRecord.tierThreeId },
      });

      async function shuffleArray(array: any[]): Promise<any[]> {
        for (let i = array.length - 1; i > 0; i--) {
          const j = Math.floor(Math.random() * (i + 1));
          [array[i], array[j]] = [array[j], array[i]];
        }
        return array;
      }

      function pickRandomQuestions(array: any[], numQuestions: number): any[] {
        return array.slice(0, numQuestions);
      }

// console.log(step)


      for (const stepData of step) {
        if(stepData.value){
        const tools = JSON.parse(stepData.value);
        for (const tool of tools) {
          if (tool.toolType === 'BANK') {

            tool.question = await shuffleArray(tool.question);
            tool.question = pickRandomQuestions(tool.question, tool.display);

          }
        }
      
        stepData.value = JSON.stringify(tools); // Update the stepData.value with the shuffled data
      }else{
        stepData.value=[]
      }
      }

      const session = await this.knowledgeSessionRecordRepository.create({ userId: id, sessionId: uniqueId, areaId: tierOneId[0], topicId: tierTwoId[0], unitId: knowledgeSessionRecord.tierThreeId, status: 'started', createdAt: currentDate });




      return { sessionId: session.sessionId, steps: step, status: session.status }

    }
    return { status: 'Not Allowed' }
  }

  @patch('/knowledge-session-end')
  @response(204, {
    security: OPERATION_SECURITY_SPEC,
    description: 'KnowledgeSessionRecord model instance',
    content: { 'application/json': { schema: getModelSchemaRef(KnowledgeSessionRecord) } },
  })
  @authenticate('jwt')
  async knowledgeSessionEnd(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(KnowledgeSessionRecord, {
            title: 'NewKnowledgeSessionRecord',
            exclude: ['id'],
          }),
        },
      },
    })
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    knowledgeSessionRecord: KnowledgeSessionEnd,
  ): Promise<Count> {

    const email = '<EMAIL>'
    // const userId = '5f17349a-2198-4900-a4f1-be69107245d8'

    let id = ''
    const user: User | null = await this.userRepository.findOne({ where: { email: email } })
    if (user !== null) {
      id = user.id;
    } else {

    }

    let hasMCQ = false;
    let checkANS = false;
    let index = 0;
    let blue = 0;
    let yellow = 0;
    let green = 0;
    let red = 0;
    let qCount = 0;
    const currentDateTimeUtc = moment.utc();
    const currentDate = currentDateTimeUtc.format('YYYY-MM-DD HH:mm:ss')
    const where: Where<KnowledgeSessionRecord> = { sessionId: knowledgeSessionRecord.sessionId, userId: id }

    const StepData = JSON.parse(knowledgeSessionRecord.stepsData)

    for (const step of StepData) {
      const tools = step.value;

      for (const tool of tools) {
        if (tool.toolType === 'MCQ') {

          for (const option of tool.radios) {
            if (option.selected === true && option.u_select === true) {
              checkANS = true
            }
          }
          // console.log(tool.slider)
          if (checkANS === true) {
            switch (tool.slider) {
              case 5:
                green += 100;
                blue += 0;
                index += 100;
                break;
              case 4:
                green += 75;
                blue += 25;
                index += 87.5;
                break;
              case 3:
                green += 50;
                blue += 50;
                index += 75;
                break;
              case 2:
                green += 25;
                blue += 75;
                index += 62.5;
                break;
              case 1:
                green += 0;
                blue += 100;
                index += 50;
                break;
              default:
                break;
            }

          } else {
            switch (tool.slider) {
              case 5:
                red += 100;
                yellow += 0;
                index += 0;
                break;
              case 4:
                red += 75;
                yellow += 25;
                index += 12.5;
                break;
              case 3:
                red += 50;
                yellow += 50;
                index += 25;
                break;
              case 2:
                red += 25;
                yellow += 75;
                index += 37.5;
                break;
              case 1:
                red += 0;
                yellow += 100;
                index += 50;
                break;
            }
          }
          qCount++;
          hasMCQ = true;
        }
      }


    }

    index = index / qCount;
    red = red / qCount;
    blue = blue / qCount;
    yellow = yellow / qCount;
    green = green / qCount;

    let tick = "";
    if (hasMCQ === true) {

      if (red > 0 || yellow > 0) {
        tick = "grey";

      } else if (blue > 0) {
        tick = "blue";

      } else {
        tick = "green";

      }
    } else {
      tick = "green";

    }
    if (hasMCQ) {
      return this.knowledgeSessionRecordRepository.updateAll({ 'address': knowledgeSessionRecord.address, 'duration': knowledgeSessionRecord.duration, 'stepsData': knowledgeSessionRecord.stepsData, 'hasMCQ': hasMCQ, 'insightIndex': index, 'blue': blue, 'red': red, 'green': green, 'yellow': yellow, 'status': 'end', 'qCount': qCount, 'updatedAt': currentDate, 'tick': tick }, where);
    }
    else {
      return this.knowledgeSessionRecordRepository.updateAll({ 'address': knowledgeSessionRecord.address, 'duration': knowledgeSessionRecord.duration, 'stepsData': knowledgeSessionRecord.stepsData, 'hasMCQ': hasMCQ, 'status': 'end', 'updatedAt': currentDate, 'tick': tick }, where);
    }




  }
}
