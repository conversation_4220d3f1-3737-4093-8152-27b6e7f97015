import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {StepTitle} from '../models';
import {StepTitleRepository} from '../repositories';

export class StepTitleController {
  constructor(
    @repository(StepTitleRepository)
    public stepTitleRepository : StepTitleRepository,
  ) {}

  @post('/step-titles')
  @response(200, {
    description: 'StepTitle model instance',
    content: {'application/json': {schema: getModelSchemaRef(StepTitle)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(StepTitle, {
            title: 'NewStepTitle',
            exclude: ['id'],
          }),
        },
      },
    })
    stepTitle: Omit<StepTitle, 'id'>,
  ): Promise<StepTitle> {
    return this.stepTitleRepository.create(stepTitle);
  }

  @get('/step-titles/count')
  @response(200, {
    description: 'StepTitle model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(StepTitle) where?: Where<StepTitle>,
  ): Promise<Count> {
    return this.stepTitleRepository.count(where);
  }

  @get('/step-titles')
  @response(200, {
    description: 'Array of StepTitle model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(StepTitle, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(StepTitle) filter?: Filter<StepTitle>,
  ): Promise<StepTitle[]> {
    return this.stepTitleRepository.find(filter);
  }

  @patch('/step-titles')
  @response(200, {
    description: 'StepTitle PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(StepTitle, {partial: true}),
        },
      },
    })
    stepTitle: StepTitle,
    @param.where(StepTitle) where?: Where<StepTitle>,
  ): Promise<Count> {
    return this.stepTitleRepository.updateAll(stepTitle, where);
  }

  @get('/step-titles/{id}')
  @response(200, {
    description: 'StepTitle model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(StepTitle, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(StepTitle, {exclude: 'where'}) filter?: FilterExcludingWhere<StepTitle>
  ): Promise<StepTitle> {
    return this.stepTitleRepository.findById(id, filter);
  }

  @patch('/step-titles/{id}')
  @response(204, {
    description: 'StepTitle PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(StepTitle, {partial: true}),
        },
      },
    })
    stepTitle: StepTitle,
  ): Promise<void> {
    await this.stepTitleRepository.updateById(id, stepTitle);
  }

  @put('/step-titles/{id}')
  @response(204, {
    description: 'StepTitle PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() stepTitle: StepTitle,
  ): Promise<void> {
    await this.stepTitleRepository.replaceById(id, stepTitle);
  }

  @del('/step-titles/{id}')
  @response(204, {
    description: 'StepTitle DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.stepTitleRepository.deleteById(id);
  }
}
