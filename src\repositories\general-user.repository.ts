import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {GeneralUser, GeneralUserRelations} from '../models';

export class GeneralUserRepository extends DefaultCrudRepository<
  GeneralUser,
  typeof GeneralUser.prototype.id,
  GeneralUserRelations
> {
  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource,
  ) {
    super(GeneralUser, dataSource);
  }
}
