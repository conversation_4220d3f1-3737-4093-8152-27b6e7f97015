import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {GroupUser, GroupUserRelations} from '../models';

export class GroupUserRepository extends DefaultCrudRepository<
  GroupUser,
  typeof GroupUser.prototype.id,
  GroupUserRelations
> {
  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource,
  ) {
    super(GroupUser, dataSource);
  }
}
