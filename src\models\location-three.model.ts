import { Entity, model, property, hasMany} from '@loopback/repository';
import {LocationFour} from './location-four.model';
import {ObservationReport} from './observation-report.model';
import {UserLocationRole} from './user-location-role.model';
import {PermitReport} from './permit-report.model';

@model()
export class LocationThree extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'string',
  })
  description?: string;

  @property({
    type: 'string',
  })
  created?: string;

  @property({
    type: 'string',
  })
  modified?: string;

  @property({
    type: 'string',
  })
  locationTwoId?: string;

  @hasMany(() => LocationFour)
  locationFours: LocationFour[];

  @hasMany(() => ObservationReport)
  observationReports: ObservationReport[];

  @hasMany(() => UserLocationRole)
  userLocationRoles: UserLocationRole[];

  @hasMany(() => PermitReport)
  permitReports: PermitReport[];

  constructor(data?: Partial<LocationThree>) {
    super(data);
  }
}

export interface LocationThreeRelations {
  // describe navigational properties here
}

export type LocationThreeWithRelations = LocationThree & LocationThreeRelations;
