import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {Ppe} from '../models';
import {PpeRepository} from '../repositories';

export class PpeController {
  constructor(
    @repository(PpeRepository)
    public ppeRepository : PpeRepository,
  ) {}

  @post('/ppes')
  @response(200, {
    description: 'Ppe model instance',
    content: {'application/json': {schema: getModelSchemaRef(Ppe)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Ppe, {
            title: 'NewPpe',
            exclude: ['id'],
          }),
        },
      },
    })
    ppe: Omit<Ppe, 'id'>,
  ): Promise<Ppe> {
    return this.ppeRepository.create(ppe);
  }

  @get('/ppes/count')
  @response(200, {
    description: 'Ppe model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(Ppe) where?: Where<Ppe>,
  ): Promise<Count> {
    return this.ppeRepository.count(where);
  }

  @get('/ppes')
  @response(200, {
    description: 'Array of Ppe model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(Ppe, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(Ppe) filter?: Filter<Ppe>,
  ): Promise<Ppe[]> {
    return this.ppeRepository.find(filter);
  }

  @patch('/ppes')
  @response(200, {
    description: 'Ppe PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Ppe, {partial: true}),
        },
      },
    })
    ppe: Ppe,
    @param.where(Ppe) where?: Where<Ppe>,
  ): Promise<Count> {
    return this.ppeRepository.updateAll(ppe, where);
  }

  @get('/ppes/{id}')
  @response(200, {
    description: 'Ppe model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(Ppe, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(Ppe, {exclude: 'where'}) filter?: FilterExcludingWhere<Ppe>
  ): Promise<Ppe> {
    return this.ppeRepository.findById(id, filter);
  }

  @patch('/ppes/{id}')
  @response(204, {
    description: 'Ppe PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Ppe, {partial: true}),
        },
      },
    })
    ppe: Ppe,
  ): Promise<void> {
    await this.ppeRepository.updateById(id, ppe);
  }

  @put('/ppes/{id}')
  @response(204, {
    description: 'Ppe PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() ppe: Ppe,
  ): Promise<void> {
    await this.ppeRepository.replaceById(id, ppe);
  }

  @del('/ppes/{id}')
  @response(204, {
    description: 'Ppe DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.ppeRepository.deleteById(id);
  }
}
