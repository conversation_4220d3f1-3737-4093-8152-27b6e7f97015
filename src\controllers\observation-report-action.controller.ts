import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  ObservationReport,
  Action,
} from '../models';
import {ObservationReportRepository} from '../repositories';

export class ObservationReportActionController {
  constructor(
    @repository(ObservationReportRepository) protected observationReportRepository: ObservationReportRepository,
  ) { }

  @get('/observation-reports/{id}/actions', {
    responses: {
      '200': {
        description: 'Array of ObservationReport has many Action',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(Action)},
          },
        },
      },
    },
  })
  async find(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<Action>,
  ): Promise<Action[]> {
    return this.observationReportRepository.actions(id).find(filter);
  }

  @post('/observation-reports/{id}/actions', {
    responses: {
      '200': {
        description: 'ObservationReport model instance',
        content: {'application/json': {schema: getModelSchemaRef(Action)}},
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof ObservationReport.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Action, {
            title: 'NewActionInObservationReport',
            exclude: ['id'],
            optional: ['objectId']
          }),
        },
      },
    }) action: Omit<Action, 'id'>,
  ): Promise<Action> {
    return this.observationReportRepository.actions(id).create(action);
  }

  @patch('/observation-reports/{id}/actions', {
    responses: {
      '200': {
        description: 'ObservationReport.Action PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Action, {partial: true}),
        },
      },
    })
    action: Partial<Action>,
    @param.query.object('where', getWhereSchemaFor(Action)) where?: Where<Action>,
  ): Promise<Count> {
    return this.observationReportRepository.actions(id).patch(action, where);
  }

  @del('/observation-reports/{id}/actions', {
    responses: {
      '200': {
        description: 'ObservationReport.Action DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(Action)) where?: Where<Action>,
  ): Promise<Count> {
    return this.observationReportRepository.actions(id).delete(where);
  }
}
