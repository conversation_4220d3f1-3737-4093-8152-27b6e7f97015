import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  TestCase,
  Checklist,
} from '../models';
import {TestCaseRepository} from '../repositories';

export class TestCaseChecklistController {
  constructor(
    @repository(TestCaseRepository)
    public testCaseRepository: TestCaseRepository,
  ) { }

  @get('/test-cases/{id}/checklist', {
    responses: {
      '200': {
        description: 'Checklist belonging to TestCase',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(Checklist)},
          },
        },
      },
    },
  })
  async getChecklist(
    @param.path.string('id') id: typeof TestCase.prototype.id,
  ): Promise<Checklist> {
    return this.testCaseRepository.checklist(id);
  }
}
