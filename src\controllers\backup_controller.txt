import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
  HttpErrors,
} from '@loopback/rest';
import { AirReport } from '../models';
import { AirReportRepository, UserRepository, ActionRepository, UserLocationRoleRepository } from '../repositories';

import { SqsService } from '../services/sqs-service.service';

import { inject } from '@loopback/core';
import { SecurityBindings, securityId, UserProfile } from '@loopback/security';
import { authenticate } from '@loopback/authentication';
import moment from 'moment';


@authenticate('jwt')
export class AirReportController {
  constructor(
    @repository(AirReportRepository)
    public airReportRepository: AirReportRepository,
    @repository(ActionRepository)
    public actionRepository: ActionRepository,
    @repository(UserRepository)
    public userRepository: UserRepository,
    @repository(UserLocationRoleRepository)
    public userLocationRoleRepository: UserLocationRoleRepository,
    @inject('services.SqsService')
    public sqsService: SqsService,
  ) { }

  @post('/air-reports')
  @response(200, {
    description: 'AirReport model instance',
    content: { 'application/json': { schema: getModelSchemaRef(AirReport) } },
  })
  async create(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AirReport, {
            title: 'NewAirReport',
            exclude: ['id'],
          }),
        },
      },
    })
    airReport: Omit<AirReport, 'id'>,
  ): Promise<AirReport> {

    const email = '<EMAIL>'
    const user = await this.userRepository.findOne({ where: { email: email } });
    if (user) {
      airReport.reporterId = user.id;
      const count = await this.airReportRepository.count();
      airReport.maskId = `AIR-${moment().format('YYMMDD')}-${count.count + 1}`;
      airReport.status = 'Stage I: Preliminary Notification'
      const aReport = await this.airReportRepository.create(airReport);
      const actionItem = {
        application: "AIR",
        actionType: "air_reviewer",

        description: airReport.description,
        dueDate: '',

        status: "open",
        createdDate: aReport.created,
        objectId: aReport.id,
        submittedById: user?.id,
        assignedToId: airReport.reviewerId
      }

      const hasDamagedEquipment = airReport.damagedEquipmentNumber.length > 0;

      // Check for any injured person inside personInvolved and personnelImpacted
      const personsInvolved = airReport.personInvolved || [];
      const personsImpacted = airReport.personnelImpacted || [];
      const isAnyPersonInjured = [...personsInvolved, ...personsImpacted].some(person => {
        if (typeof person === 'object' && person !== null) {
          return 'injured' in person && person.injured;
        }
        return false;
      });


      if (hasDamagedEquipment) {
        const mailSubject = `Notification to Duty Engineering Manager`;
        const mailBody = `<!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Medical Report</title>
        </head>
        <body>
          
            <p><strong>IR Ref Number:</strong> ${airReport.maskId}</p>
            <p><strong>Date & Time of Incident:</strong> ${airReport.incidentDate}</p>
            <p><strong>Damaged Equipment:</strong> ${airReport.damagedEquipmentNumber?.join(', ')} </p>
            
            <p><strong>Damage Type:</strong> ${airReport.damageType}</p>
            <p><strong>How / Why did the incident occur:</strong> ${airReport.moreDetails}</p>
            <p><strong>Submitted By:</strong> ${user?.firstName}</p>
            <p><strong>Submitted Date / Time:</strong> ${airReport.created}</p>
           
        </body>
        </html>`;

        const roleId = "64d5e4b4a3e49d1da2e04f70";
        const allLocationId = 'all';

        let whereCondition = {
          roles: { inq: [[roleId]] },
          or: [

            {
              locationOneId: { inq: ['tier1-all'] },
              locationTwoId: '',
              locationThreeId: '',
              locationFourId: '',
            },

          ]
        };

        const userLocationRoles = await this.userLocationRoleRepository.find({
          where: whereCondition,
        });

        const userIds = userLocationRoles.map((userLocationRole) => userLocationRole.userId);
        const uniqueUserIds = Array.from(new Set(userIds));

        const users = await this.userRepository.find({
          where: { id: { inq: uniqueUserIds as string[] } },
        });

        if (users && users.length) {
          for (const user of users) {
            this.sqsService.sendMessage(user, mailSubject, mailBody);
          }
        } else {
          throw new HttpErrors.NotFound(`User not found. Try again`);
        }
      }

      if (isAnyPersonInjured) {
        const personsInvolved = airReport.personInvolved || [];
        const personsImpacted = airReport.personnelImpacted || [];
        const isAnyPersonInjured = [...personsInvolved, ...personsImpacted].filter(person => {
          if (typeof person === 'object' && person !== null) {
            return 'injured' in person && person.injured;
          }
          return false;
        });

        const formattedDetails = isAnyPersonInjured?.map((person: any, index) => {
          if (person && person?.selectedEmp && person?.selectedEmp.name)
            return `
              <li>   
                <p>Person ${index + 1}: </p>
                <p>Name: ${person?.selectedEmp.name || 'N/A'} </p>
                <p>Employee ID: ${person?.selectedEmp.uniqueId || 'N/A'} </p>
                <p>Designation: ${person?.selectedEmp.designation || 'N/A'} </p>
                <p>Injured Parts: ${person?.injuryParts.map((i: any) => i).join(',')} </p>
               
            </li>
            `;
          else
            return ``;
        }).join('\n\n');

        const mailSubject = `Notification to Medical Officer`;
        const mailBody = `<!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Medical Report</title>
        </head>
        <body>
          
            <p><strong>IR Ref Number:</strong>  ${airReport.maskId}</p>
            <p><strong>Date & Time of Incident:</strong> ${airReport.incidentDate}</p>
            <p><strong>Person/s Impacted:</strong> </p>
            <ol>
            ${formattedDetails}
            </ol>
            <p><strong>Submitted By:</strong> ${user?.firstName}</p>
            <p><strong>Submitted Date / Time:</strong> ${airReport.created}</p>
           
        </body>
        </html>`;

        const roleId = "64f0375eb7adc567b4925e92";
        const allLocationId = 'all';

        let whereCondition = {
          roles: { inq: [[roleId]] },
          or: [

            {
              locationOneId: { inq: ['tier1-all'] },
              locationTwoId: '',
              locationThreeId: '',
              locationFourId: '',
            },

          ]
        };

        const userLocationRoles = await this.userLocationRoleRepository.find({
          where: whereCondition,
        });

        const userIds = userLocationRoles.map((userLocationRole) => userLocationRole.userId);
        const uniqueUserIds = Array.from(new Set(userIds));

        const users = await this.userRepository.find({
          where: { id: { inq: uniqueUserIds as string[] } },
        });

        if (users && users.length) {
          for (const user of users) {
            this.sqsService.sendMessage(user, mailSubject, mailBody);
          }
        } else {
          throw new HttpErrors.NotFound(`User not found. Try again`);
        }
      }


      if (airReport.reviewerId && user?.id) {
        const airReviewer = await this.userRepository.findById(airReport.reviewerId);
        if (airReviewer) { this.sqsService.sendMessage(airReviewer, `Review incident Reported by ${user.firstName} | AIR ${aReport.maskId}`, `AIR ${aReport.maskId} is submitted by ${user?.firstName}. Please Review.`) } else { throw new HttpErrors.NotFound(`User not found. Try again`); }
      }
      await this.actionRepository.create(actionItem)
      return aReport;
    } else {
      throw new HttpErrors.Unauthorized('Unauthorized')
    }



  }

  @get('/air-reports/count')
  @response(200, {
    description: 'AirReport model count',
    content: { 'application/json': { schema: CountSchema } },
  })
  async count(
    @param.where(AirReport) where?: Where<AirReport>,
  ): Promise<Count> {
    return this.airReportRepository.count(where);
  }

  @get('/air-reports')
  @response(200, {
    description: 'Array of AirReport model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(AirReport, { includeRelations: true }),
        },
      },
    },
  })
  async find(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.filter(AirReport) filter?: Filter<AirReport>,
  ): Promise<AirReport[]> {
    const email = '<EMAIL>'
    const user = await this.userRepository.findOne({ where: { email: email } });
    if (user) {
      const where = {
        or: [
          { reviewerId: user.id },
          { reporterId: user.id },
          { surveyorId: user.id },
          { estimatorId: user.id },
          { traineeId: user.id },
          { gmOpsId: user.id },
          { thirdPartyId: user.id },
          { securityId: user.id },
          { costReviewerId: user.id },
          { financerId: user.id },
          { dutyEngManagerId: user.id },
        ]
      };

      return this.airReportRepository.find({ where });
    } else {
      throw new HttpErrors.Unauthorized('Unauthorized')
    }

  }

  @get('/all-air-reports')
  @response(200, {
    description: 'Array of AirReport model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(AirReport, { includeRelations: true }),
        },
      },
    },
  })
  async findAll(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.filter(AirReport) filter?: Filter<AirReport>,
  ): Promise<AirReport[]> {
    const email = '<EMAIL>'
    const user = await this.userRepository.findOne({ where: { email: email } });
    if (user) {
      return this.airReportRepository.find(filter);
    } else {
      throw new HttpErrors.Unauthorized('Unauthorized')
    }

  }


  @get('/my-air-reports')
  @response(200, {
    description: 'Array of AirReport model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(AirReport, { includeRelations: true }),
        },
      },
    },
  })
  async findMyReport(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.filter(AirReport) filter?: Filter<AirReport>,
  ): Promise<AirReport[]> {
    const email = '<EMAIL>'
    const user = await this.userRepository.findOne({ where: { email: email } });
    if (user) {
      return this.airReportRepository.find({ where: { reporterId: user.id } });
    } else {
      throw new HttpErrors.Unauthorized('Unauthorized')
    }

  }

  @patch('/air-reports')
  @response(200, {
    description: 'AirReport PATCH success count',
    content: { 'application/json': { schema: CountSchema } },
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AirReport, { partial: true }),
        },
      },
    })
    airReport: AirReport,
    @param.where(AirReport) where?: Where<AirReport>,
  ): Promise<Count> {
    return this.airReportRepository.updateAll(airReport, where);
  }

  @get('/air-reports/{id}')
  @response(200, {
    description: 'AirReport model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(AirReport, { includeRelations: true }),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(AirReport, { exclude: 'where' }) filter?: FilterExcludingWhere<AirReport>
  ): Promise<AirReport> {
    return this.airReportRepository.findById(id, filter);
  }

  @patch('/air-reports/{id}')
  @response(204, {
    description: 'AirReport PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AirReport, { partial: true }),
        },
      },
    })
    airReport: AirReport,
  ): Promise<void> {
    await this.airReportRepository.updateById(id, airReport);
  }

  @patch('/air-reports-truck-status/{id}')
  @response(204, {
    description: 'AirReport PATCH success',
  })
  async updateTruckStatusById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @param.path.string('actionId') actionId: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AirReport, { partial: true }),
        },
      },
    })
    airReport: AirReport,
  ): Promise<void> {



    await this.airReportRepository.updateById(id, airReport);
  }


  @patch('/air-reviewer-reports/{id}/{actionId}')
  @response(204, {
    description: 'AirReport PATCH success',
  })
  async updateReviewerById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @param.path.string('actionId') actionId: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AirReport, { partial: true }),
        },
      },
    })
    airReport: AirReport,
  ): Promise<void> {

    const email = '<EMAIL>'
    const user = await this.userRepository.findOne({ where: { email: email } });

    const airReportData = await this.airReportRepository.findById(id)
    airReport.status = 'Stage 2: Initial Assessment';

    if (airReport.immediateActionRequired) {
      //Notify concern people
      if (airReport.cllInvolved) {
        airReport.truckStatus = true
      }
    }

    if (airReport.isMajorIncident) {
      //inform TT Club
    }

    let actionItem = [];
    if (airReport.isMedicalPersonInvolved) {
      actionItem = [{
        application: "AIR",
        actionType: "air_medical_officer",

        description: airReportData.description,

        dueDate: '',
        status: "open",
        createdDate: moment(new Date()).format('DD-MM-YYYY HH:mm'),
        objectId: airReportData.id,
        submittedById: user?.id,
        assignedToId: airReport.medicalOfficerId
      }]

      if (airReport.medicalOfficerId && user?.id) {
        const airMedicalOfficer = await this.userRepository.findById(airReport.medicalOfficerId);
        if (airMedicalOfficer) { this.sqsService.sendMessage(airMedicalOfficer, `Send Medical Report for the incident ${airReportData.maskId} Reported by ${user.firstName} | AIR ${airReportData.maskId}`, `AIR ${airReportData.maskId} is submitted by ${user?.firstName}. Please Send Medical Report.`) } else { throw new HttpErrors.NotFound(`User not found. Try again`); }
      }
    }

    if (airReport.damagedEquipmentNumber && airReport.damagedEquipmentNumber.length > 0) {
      actionItem = [{
        application: "AIR",
        actionType: "air_engineer",

        description: airReportData.description,

        dueDate: '',
        status: "open",
        createdDate: moment(new Date()).format('DD-MM-YYYY HH:mm'),
        objectId: airReportData.id,
        submittedById: user?.id,
        assignedToId: airReport.engineerId
      }]

      if (airReport.engineerId && user?.id) {
        const airEngineer = await this.userRepository.findById(airReport.engineerId);
        if (airEngineer) { this.sqsService.sendMessage(airEngineer, `Inspect the damaged equipmenet incident Reported by ${user.firstName} | AIR ${airReportData.maskId}`, `AIR ${airReportData.maskId} is submitted by ${user?.firstName}. Please do the Survey.`) } else { throw new HttpErrors.NotFound(`User not found. Try again`); }
      }


    }

    actionItem = [{
      application: "AIR",
      actionType: "air_investigator",

      description: airReportData.description,

      dueDate: '',
      status: "open",
      createdDate: moment(new Date()).format('DD-MM-YYYY HH:mm'),
      objectId: airReportData.id,
      submittedById: user?.id,
      assignedToId: airReportData.reviewerId
    },
    {
      application: "AIR",
      actionType: "air_cost_estimator",

      description: airReportData.description,

      dueDate: '',
      status: "open",
      createdDate: moment(new Date()).format('DD-MM-YYYY HH:mm'),
      objectId: airReportData.id,
      submittedById: user?.id,
      assignedToId: airReport.estimatorId
    }


    ]

    if (airReport.surveyorId || airReport.surveyorEmail) {
      actionItem = [...actionItem,
      {
        application: "AIR",
        actionType: "air_surveyor",

        description: airReportData.description,
        dueDate: '',

        status: "open",
        createdDate: moment(new Date()).format('DD-MM-YYYY HH:mm'),
        objectId: airReportData.id,
        submittedById: user?.id,
        assignedToId: airReportData.reviewerId
      }]

      if (airReportData.reviewerId && user?.id) {
        const airSurvyeor = await this.userRepository.findById(airReportData.reviewerId);
        if (airSurvyeor) { this.sqsService.sendMessage(airSurvyeor, `Upload the Survey documents for the incident Reported by ${user.firstName} | AIR ${airReportData.maskId}`, `AIR ${airReportData.maskId} is submitted by ${user?.firstName}. Please upload the Survey Documents.`) } else { throw new HttpErrors.NotFound(`User not found. Try again`); }
      }

      //added surveyor email AIR survey need only notification through email . It should contain AIR # Number, Date and Time,Description ,photos,  Submitted by


      if (airReport.surveyorEmail) {
        this.sqsService.sendEmail(airReport.surveyorEmail, `Incident to be Surveyed`, `AIR ${airReportData.maskId} is submitted by ${user?.firstName}. Description: ${airReportData.description}. Please carryout the survey immediately`)
      }


    }

    if (airReport.reviewerId && user?.id) {
      const airReviewer = await this.userRepository.findById(airReportData.reviewerId);
      if (airReviewer) { this.sqsService.sendMessage(airReviewer, `Investigate incident Reported by ${user.firstName} | AIR ${airReportData.maskId}`, `AIR ${airReportData.maskId} is submitted by ${user?.firstName}. Please Investigate.`) } else { throw new HttpErrors.NotFound(`User not found. Try again`); }
    }

    if (airReport.estimatorId && user?.id) {
      const airEstimator = await this.userRepository.findById(airReport.estimatorId);
      if (airEstimator) { this.sqsService.sendMessage(airEstimator, `Estimate the Cost of incident Reported by ${user.firstName} | AIR ${airReportData.maskId}`, `AIR ${airReportData.maskId} is submitted by ${user?.firstName}. Please Estimate the cost of Incident.`) } else { throw new HttpErrors.NotFound(`User not found. Try again`); }
    }





    if (actionItem && actionItem.length > 0)
      await this.actionRepository.createAll(actionItem)


    await this.actionRepository.updateById(actionId, { status: 'completed' })

    await this.airReportRepository.updateById(id, airReport);
  }


  @patch('/air-reports-approve/{id}/{actionId}')
  @response(204, {
    description: 'AirReport PATCH success',
  })
  async updateApproveById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @param.path.string('actionId') actionId: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AirReport, { partial: true }),
        },
      },
    })
    airReport: AirReport,
  ): Promise<void> {

    const email = '<EMAIL>'
    const user = await this.userRepository.findOne({ where: { email: email } });

    const airReportData = await this.airReportRepository.findById(id)
    airReport.status = 'Stage 3: Reported';



    let actionItem = [];
    if (airReportData.damagedEquipmentNumber && airReportData.damagedEquipmentNumber.length > 0) {
      actionItem = [{
        application: "AIR",
        actionType: "air_engineer",

        description: airReportData.description,

        dueDate: '',
        status: "open",
        createdDate: moment(new Date()).format('DD-MM-YYYY HH:mm'),
        objectId: airReportData.id,
        submittedById: user?.id,
        assignedToId: airReportData.engineerId
      }]

      if (airReportData.engineerId && user?.id) {
        const airEngineer = await this.userRepository.findById(airReportData.engineerId);
        if (airEngineer) { this.sqsService.sendMessage(airEngineer, `Inspect the damaged equipmenet incident Reported by ${user.firstName} | AIR ${airReportData.maskId}`, `AIR ${airReportData.maskId} is submitted by ${user?.firstName}. Please do the Survey.`) } else { throw new HttpErrors.NotFound(`User not found. Try again`); }
      }


    } else {
      actionItem = [{
        application: "AIR",
        actionType: "air_investigator",

        description: airReportData.description,

        dueDate: '',
        status: "open",
        createdDate: moment(new Date()).format('DD-MM-YYYY HH:mm'),
        objectId: airReportData.id,
        submittedById: user?.id,
        assignedToId: airReportData.reviewerId
      },
      {
        application: "AIR",
        actionType: "air_cost_estimator",

        description: airReportData.description,

        dueDate: '',
        status: "open",
        createdDate: moment(new Date()).format('DD-MM-YYYY HH:mm'),
        objectId: airReportData.id,
        submittedById: user?.id,
        assignedToId: airReportData.estimatorId
      }


      ]

      if (airReportData.surveyorId || airReportData.surveyorEmail) {
        actionItem = [...actionItem,
        {
          application: "AIR",
          actionType: "air_surveyor",

          description: airReportData.description,
          dueDate: '',

          status: "open",
          createdDate: moment(new Date()).format('DD-MM-YYYY HH:mm'),
          objectId: airReportData.id,
          submittedById: user?.id,
          assignedToId: airReportData.reviewerId
        }]

        if (airReportData.reviewerId && user?.id) {
          const airSurvyeor = await this.userRepository.findById(airReportData.reviewerId);
          if (airSurvyeor) { this.sqsService.sendMessage(airSurvyeor, `Upload the Survey Document for the incident Reported by ${user.firstName} | AIR ${airReportData.maskId}`, `AIR ${airReportData.maskId} is submitted by ${user?.firstName}. Please Upload the Survey Document.`) } else { throw new HttpErrors.NotFound(`User not found. Try again`); }
        }

        if (airReportData.surveyorEmail) {
          this.sqsService.sendEmail(airReportData.surveyorEmail, `Incident to be Surveyed`, `AIR ${airReportData.maskId} is submitted by ${user?.firstName}. Description: ${airReportData.description}. Please carryout the survey immediately`)
        }


      }

      if (airReportData.reviewerId && user?.id) {
        const airReviewer = await this.userRepository.findById(airReportData.reviewerId);
        if (airReviewer) { this.sqsService.sendMessage(airReviewer, `Investigate incident Reported by ${user.firstName} | AIR ${airReportData.maskId}`, `AIR ${airReportData.maskId} is submitted by ${user?.firstName}. Please Investigate.`) } else { throw new HttpErrors.NotFound(`User not found. Try again`); }
      }

      if (airReportData.estimatorId && user?.id) {
        const airEstimator = await this.userRepository.findById(airReportData.estimatorId);
        if (airEstimator) { this.sqsService.sendMessage(airEstimator, `Estimate the Cost of incident Reported by ${user.firstName} | AIR ${airReportData.maskId}`, `AIR ${airReportData.maskId} is submitted by ${user?.firstName}. Please Estimate the cost of Incident.`) } else { throw new HttpErrors.NotFound(`User not found. Try again`); }
      }

    }

    if (actionItem && actionItem.length > 0)
      await this.actionRepository.createAll(actionItem)


    await this.actionRepository.updateById(actionId, { status: 'completed' })

    await this.airReportRepository.updateById(id, airReport);
  }


  @patch('/air-reports-approve-second/{id}/{actionId}')
  @response(204, {
    description: 'AirReport PATCH success',
  })
  async updateApproveSecondById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @param.path.string('actionId') actionId: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AirReport, { partial: true }),
        },
      },
    })
    airReport: AirReport,
  ): Promise<void> {

    const email = '<EMAIL>'
    const user = await this.userRepository.findOne({ where: { email: email } });

    const airReportData = await this.airReportRepository.findById(id)
    airReport.status = 'Stage 3: Reported';



    let actionItem = [];

    actionItem = [{
      application: "AIR",
      actionType: "air_investigator",

      description: airReportData.description,

      dueDate: '',
      status: "open",
      createdDate: moment(new Date()).format('DD-MM-YYYY HH:mm'),
      objectId: airReportData.id,
      submittedById: user?.id,
      assignedToId: airReportData.reviewerId
    },
    {
      application: "AIR",
      actionType: "air_cost_estimator",

      description: airReportData.description,

      dueDate: '',
      status: "open",
      createdDate: moment(new Date()).format('DD-MM-YYYY HH:mm'),
      objectId: airReportData.id,
      submittedById: user?.id,
      assignedToId: airReportData.estimatorId
    }


    ]

    if (airReportData.surveyorId || airReportData.surveyorEmail) {
      actionItem = [...actionItem,
      {
        application: "AIR",
        actionType: "air_surveyor",

        description: airReportData.description,
        dueDate: '',

        status: "open",
        createdDate: moment(new Date()).format('DD-MM-YYYY HH:mm'),
        objectId: airReportData.id,
        submittedById: user?.id,
        assignedToId: airReportData.reviewerId
      }]

      if (airReportData.reviewerId && user?.id) {
        const airSurvyeor = await this.userRepository.findById(airReportData.reviewerId);
        if (airSurvyeor) { this.sqsService.sendMessage(airSurvyeor, `Upload the Survey Document the incident Reported by ${user.firstName} | AIR ${airReportData.maskId}`, `AIR ${airReportData.maskId} is submitted by ${user?.firstName}. Please Upload the Survey Document.`) } else { throw new HttpErrors.NotFound(`User not found. Try again`); }
      }

      if (airReportData.surveyorEmail) {
        this.sqsService.sendEmail(airReportData.surveyorEmail, `Incident to be Surveyed`, `AIR ${airReportData.maskId} is submitted by ${user?.firstName}. Description: ${airReportData.description}. Please carryout the survey immediately`)
      }

    }

    if (airReportData.reviewerId && user?.id) {
      const airReviewer = await this.userRepository.findById(airReportData.reviewerId);
      if (airReviewer) { this.sqsService.sendMessage(airReviewer, `Investigate incident Reported by ${user.firstName} | AIR ${airReportData.maskId}`, `AIR ${airReportData.maskId} is submitted by ${user?.firstName}. Please Investigate.`) } else { throw new HttpErrors.NotFound(`User not found. Try again`); }
    }

    if (airReportData.estimatorId && user?.id) {
      const airEstimator = await this.userRepository.findById(airReportData.estimatorId);
      if (airEstimator) { this.sqsService.sendMessage(airEstimator, `Estimate the Cost of incident Reported by ${user.firstName} | AIR ${airReportData.maskId}`, `AIR ${airReportData.maskId} is submitted by ${user?.firstName}. Please Estimate the cost of Incident.`) } else { throw new HttpErrors.NotFound(`User not found. Try again`); }
    }



    if (actionItem && actionItem.length > 0)
      await this.actionRepository.createAll(actionItem)


    await this.actionRepository.updateById(actionId, { status: 'completed' })

    await this.airReportRepository.updateById(id, airReport);
  }

  //resubmit here
  @patch('/air-resubmit-reports/{id}/{actionId}')
  @response(204, {
    description: 'AirReport PATCH success',
  })
  async updateResubmitById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @param.path.string('actionId') actionId: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AirReport, { partial: true }),
        },
      },
    })
    airReport: AirReport,
  ): Promise<void> {

    const email = '<EMAIL>'
    const user = await this.userRepository.findOne({ where: { email: email } });

    const airReportData = await this.airReportRepository.findById(id)

    if (user) {
      airReport.reporterId = user.id;


      airReport.status = 'Stage I: Preliminary Notification'

      const actionItem = {
        application: "AIR",
        actionType: "air_reviewer",

        description: airReport.description,
        dueDate: '',

        status: "open",
        createdDate: airReport.created,
        objectId: airReportData.id,
        submittedById: user?.id,
        assignedToId: airReportData.reviewerId
      }
      if (airReportData.reviewerId && user?.id) {
        const airReviewer = await this.userRepository.findById(airReportData.reviewerId);
        if (airReviewer) { this.sqsService.sendMessage(airReviewer, `Review incident Resubmitted by ${user.firstName} | AIR ${airReportData.maskId}`, `AIR ${airReportData.maskId} is re-submitted by ${user?.firstName}. Please Review.`) } else { throw new HttpErrors.NotFound(`User not found. Try again`); }
      }
      await this.actionRepository.create(actionItem)

    } else {
      throw new HttpErrors.Unauthorized('Unauthorized')
    }


    await this.actionRepository.updateById(actionId, { status: 'completed' })

    await this.airReportRepository.updateById(id, airReport);
  }



  @patch('/air-medical-reports/{id}/{actionId}')
  @response(204, {
    description: 'AirReport PATCH success',
  })
  async updateMedicalById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @param.path.string('actionId') actionId: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AirReport, { partial: true }),
        },
      },
    })
    airReport: AirReport,
  ): Promise<void> {



    // airReport.status = 'Stage 3: Reported';



    await this.actionRepository.updateById(actionId, { status: 'completed' })

    await this.airReportRepository.updateById(id, airReport);
  }


  @patch('/air-engineer-reports/{id}/{actionId}')
  @response(204, {
    description: 'AirReport PATCH success',
  })
  async updateEngineerById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @param.path.string('actionId') actionId: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AirReport, { partial: true }),
        },
      },
    })
    airReport: AirReport,
  ): Promise<void> {



    // airReport.status = 'Stage 3: Reported';


    const airReportData = await this.airReportRepository.findById(id)

    airReport.engineerComments = airReportData.engineerComments + '<br />' + airReport.engineerComments;


    await this.actionRepository.updateById(actionId, { status: 'in_progress' })

    await this.airReportRepository.updateById(id, airReport);
  }


  @patch('/air-review-return/{id}/{actionId}')
  @response(204, {
    description: 'AirReport PATCH success',
  })
  async updateReviewReturnById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @param.path.string('actionId') actionId: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AirReport, { partial: true }),
        },
      },
    })
    airReport: AirReport,
  ): Promise<void> {

    const email = '<EMAIL>'
    const user = await this.userRepository.findOne({ where: { email: email } });

    const airReportData = await this.airReportRepository.findById(id)
    airReport.status = 'Stage 2: Initial Assessment'


    let actionItem = [
      {
        application: "AIR",
        actionType: "air_reporter",

        description: airReportData.description,
        dueDate: '',

        status: "open",
        createdDate: moment(new Date()).format('DD-MM-YYYY HH:mm'),
        objectId: airReportData.id,
        submittedById: user?.id,
        assignedToId: airReportData.reporterId
      }]

    await this.actionRepository.createAll(actionItem)

    await this.actionRepository.updateById(actionId, { status: 'completed' })

    await this.airReportRepository.updateById(id, airReport);

  }

  @patch('/air-investigator-reports/{id}/{actionId}')
  @response(204, {
    description: 'AirReport PATCH success',
  })
  async updateInvestigatorById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @param.path.string('actionId') actionId: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AirReport, { partial: true }),
        },
      },
    })
    airReport: AirReport,
  ): Promise<void> {
    //use gmops review after investigation
    const email = '<EMAIL>'
    const user = await this.userRepository.findOne({ where: { email: email } });

    const airReportData = await this.airReportRepository.findById(id)
    airReport.status = 'Stage 4: Investigation Phase'

    let actionItem = [{
      application: "AIR",
      actionType: "air_gmops_review",

      description: airReportData.description,

      dueDate: '',
      status: "open",
      createdDate: moment(new Date()).format('DD-MM-YYYY HH:mm'),
      objectId: airReportData.id,
      submittedById: user?.id,
      assignedToId: airReport.gmOpsId
    }

    ]

    if (airReport.gmOpsId && user?.id) {
      const airGmOps = await this.userRepository.findById(airReport.gmOpsId);
      if (airGmOps) { this.sqsService.sendMessage(airGmOps, `Review the incident Reported by ${user.firstName} | AIR ${airReportData.maskId}`, `AIR ${airReportData.maskId} is submitted by ${user?.firstName}. Please Review.`) } else { throw new HttpErrors.NotFound(`User not found. Try again`); }
    }

    await this.actionRepository.createAll(actionItem)


    await this.actionRepository.updateById(actionId, { status: 'completed' })

    await this.airReportRepository.updateById(id, airReport);
  }


  @patch('/air-gmops-reports/{id}/{actionId}')
  @response(204, {
    description: 'AirReport PATCH success',
  })
  async updateGmOpsById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @param.path.string('actionId') actionId: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AirReport, { partial: true }),
        },
      },
    })
    airReport: AirReport,
  ): Promise<void> {

    const email = '<EMAIL>'
    const user = await this.userRepository.findOne({ where: { email: email } });

    const airReportData = await this.airReportRepository.findById(id)
    airReport.status = 'Stage 5: Action Implementation'

    let actionItem = [{
      application: "AIR",
      actionType: "air_trainee",

      description: airReportData.description,

      dueDate: '',
      status: "open",
      createdDate: moment(new Date()).format('DD-MM-YYYY HH:mm'),
      objectId: airReportData.id,
      submittedById: user?.id,
      assignedToId: airReport.traineeId
    }

    ]



    await this.actionRepository.createAll(actionItem)


    await this.actionRepository.updateById(actionId, { status: 'completed' })

    await this.airReportRepository.updateById(id, airReport);
  }


  @patch('/air-cost-estimator-reports/{id}/{actionId}')
  @response(204, {
    description: 'AirReport PATCH success',
  })
  async updateCostEstimatorById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @param.path.string('actionId') actionId: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AirReport, { partial: true }),
        },
      },
    })
    airReport: AirReport,
  ): Promise<void> {

    const email = '<EMAIL>'
    const user = await this.userRepository.findOne({ where: { email: email } });

    const airReportData = await this.airReportRepository.findById(id)

    airReport.status = 'Stage 5: Action Implementation'
    let actionItem = [{
      application: "AIR",
      actionType: "air_duty_manager",

      description: 'Modify / Submit the Cost Estimation',

      dueDate: '',
      status: "open",
      createdDate: moment(new Date()).format('DD-MM-YYYY HH:mm'),
      objectId: airReportData.id,
      submittedById: user?.id,
      assignedToId: airReport.dutyEngManagerId
    }

    ]

    if (airReport.dutyEngManagerId && user?.id) {
      const airDutyEngManager = await this.userRepository.findById(airReport.dutyEngManagerId);
      if (airDutyEngManager) { this.sqsService.sendMessage(airDutyEngManager, `Estimate the Actual Cost of incident Reported by ${user.firstName} | AIR ${airReportData.maskId}`, `AIR ${airReportData.maskId} is submitted by ${user?.firstName}. Please Estimate the actual cost of Incident.`) } else { throw new HttpErrors.NotFound(`User not found. Try again`); }
    }

    await this.actionRepository.createAll(actionItem)


    await this.actionRepository.updateById(actionId, { status: 'completed' })

    await this.airReportRepository.updateById(id, airReport);
  }


  @patch('/air-duty-manager-estimator-reports/{id}/{actionId}')
  @response(204, {
    description: 'AirReport PATCH success',
  })
  async updateDutyManagerEstimatorById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @param.path.string('actionId') actionId: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AirReport, { partial: true }),
        },
      },
    })
    airReport: AirReport,
  ): Promise<void> {

    const email = '<EMAIL>'
    const user = await this.userRepository.findOne({ where: { email: email } });

    const airReportData = await this.airReportRepository.findById(id)

    airReport.status = 'Stage 5: Action Implementation'
    let actionItem = [{
      application: "AIR",
      actionType: "air_cost_reviewer",

      description: 'Review the Actual Cost Estimation',

      dueDate: '',
      status: "open",
      createdDate: moment(new Date()).format('DD-MM-YYYY HH:mm'),
      objectId: airReportData.id,
      submittedById: user?.id,
      assignedToId: airReport.costReviewerId
    }

    ]


    if (airReport.costReviewerId && user?.id) {
      const airCostReviewer = await this.userRepository.findById(airReport.costReviewerId);
      if (airCostReviewer) { this.sqsService.sendMessage(airCostReviewer, `Review the Actual Cost of incident Reported by ${user.firstName} | AIR ${airReportData.maskId}`, `AIR ${airReportData.maskId} is submitted by ${user?.firstName}. Please Review the actual cost of Incident.`) } else { throw new HttpErrors.NotFound(`User not found. Try again`); }
    }

    await this.actionRepository.createAll(actionItem)


    await this.actionRepository.updateById(actionId, { status: 'completed' })

    await this.airReportRepository.updateById(id, airReport);
  }


  @patch('/air-cost-reviewer-reports/{id}/{actionId}')
  @response(204, {
    description: 'AirReport PATCH success',
  })
  async updateCostReviewerById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @param.path.string('actionId') actionId: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AirReport, { partial: true }),
        },
      },
    })
    airReport: AirReport,
  ): Promise<void> {

    const email = '<EMAIL>'
    const user = await this.userRepository.findOne({ where: { email: email } });

    const airReportData = await this.airReportRepository.findById(id)

    airReport.status = 'Stage 5: Action Implementation'
    let actionItem = [{
      application: "AIR",
      actionType: "air_finance",

      description: 'Actual Cost Estimation',

      dueDate: '',
      status: "open",
      createdDate: moment(new Date()).format('DD-MM-YYYY HH:mm'),
      objectId: airReportData.id,
      submittedById: user?.id,
      assignedToId: airReport.financerId
    }

    ]
    if (airReport.financerId && user?.id) {
      const airFinancer = await this.userRepository.findById(airReport.financerId);
      if (airFinancer) { this.sqsService.sendMessage(airFinancer, `Review the Actual Cost of incident Reported by ${user.firstName} | AIR ${airReportData.maskId}`, `AIR ${airReportData.maskId} is submitted by ${user?.firstName}. Please Review the actual cost of Incident.`) } else { throw new HttpErrors.NotFound(`User not found. Try again`); }
    }

    await this.actionRepository.createAll(actionItem)


    await this.actionRepository.updateById(actionId, { status: 'completed' })

    await this.airReportRepository.updateById(id, airReport);
  }


  @patch('/air-financer-reports/{id}/{actionId}')
  @response(204, {
    description: 'AirReport PATCH success',
  })
  async updateFinancerById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @param.path.string('actionId') actionId: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AirReport, { partial: true }),
        },
      },
    })
    airReport: AirReport,
  ): Promise<void> {

    const email = '<EMAIL>'
    const user = await this.userRepository.findOne({ where: { email: email } });

    const airReportData = await this.airReportRepository.findById(id)

    airReport.truckStatus = false
    airReport.status = 'Stage 6: Closed and Archived'

    await this.actionRepository.updateById(actionId, { status: 'completed' })

    await this.airReportRepository.updateById(id, airReport);
  }

  @patch('/air-surveyor-reports/{id}/{actionId}')
  @response(204, {
    description: 'AirReport PATCH success',
  })
  async updateSurveyorById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @param.path.string('actionId') actionId: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AirReport, { partial: true }),
        },
      },
    })
    airReport: AirReport,
  ): Promise<void> {


    airReport.status = 'Stage 3: Reported'
    await this.actionRepository.updateById(actionId, { status: 'completed' })

    await this.airReportRepository.updateById(id, airReport);
  }

  @patch('/air-trainee-reports/{id}/{actionId}')
  @response(204, {
    description: 'AirReport PATCH success',
  })
  async updateTraineeById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @param.path.string('actionId') actionId: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AirReport, { partial: true }),
        },
      },
    })
    airReport: AirReport,
  ): Promise<void> {

    const email = '<EMAIL>'
    const user = await this.userRepository.findOne({ where: { email: email } });

    await this.actionRepository.updateById(actionId, { status: 'completed' })

    await this.airReportRepository.updateById(id, airReport);
  }

  @put('/air-reports/{id}')
  @response(204, {
    description: 'AirReport PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() airReport: AirReport,
  ): Promise<void> {
    await this.airReportRepository.replaceById(id, airReport);
  }

  @del('/air-reports/{id}')
  @response(204, {
    description: 'AirReport DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.airReportRepository.deleteById(id);
  }
}
