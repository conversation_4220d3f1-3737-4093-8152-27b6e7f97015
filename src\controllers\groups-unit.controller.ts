import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
  import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
Groups,
GroupUnit,
Unit,
} from '../models';
import {GroupsRepository} from '../repositories';

export class GroupsUnitController {
  constructor(
    @repository(GroupsRepository) protected groupsRepository: GroupsRepository,
  ) { }

  @get('/groups/{id}/units', {
    responses: {
      '200': {
        description: 'Array of Groups has many Unit through GroupUnit',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(Unit)},
          },
        },
      },
    },
  })
  async find(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<Unit>,
  ): Promise<Unit[]> {
    return this.groupsRepository.units(id).find(filter);
  }

  @post('/groups/{id}/units', {
    responses: {
      '200': {
        description: 'create a Unit model instance',
        content: {'application/json': {schema: getModelSchemaRef(Unit)}},
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof Groups.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Unit, {
            title: 'NewUnitInGroups',
            exclude: ['id'],
          }),
        },
      },
    }) unit: Omit<Unit, 'id'>,
  ): Promise<Unit> {
    return this.groupsRepository.units(id).create(unit);
  }

  @patch('/groups/{id}/units', {
    responses: {
      '200': {
        description: 'Groups.Unit PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Unit, {partial: true}),
        },
      },
    })
    unit: Partial<Unit>,
    @param.query.object('where', getWhereSchemaFor(Unit)) where?: Where<Unit>,
  ): Promise<Count> {
    return this.groupsRepository.units(id).patch(unit, where);
  }

  @del('/groups/{id}/units', {
    responses: {
      '200': {
        description: 'Groups.Unit DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(Unit)) where?: Where<Unit>,
  ): Promise<Count> {
    return this.groupsRepository.units(id).delete(where);
  }
}
