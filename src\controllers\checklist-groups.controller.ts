import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
  import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
Checklist,
GroupChecklistAllocation,
Groups,
} from '../models';
import {ChecklistRepository} from '../repositories';

export class ChecklistGroupsController {
  constructor(
    @repository(ChecklistRepository) protected checklistRepository: ChecklistRepository,
  ) { }

  @get('/checklists/{id}/groups', {
    responses: {
      '200': {
        description: 'Array of Checklist has many Groups through GroupChecklistAllocation',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(Groups)},
          },
        },
      },
    },
  })
  async find(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<Groups>,
  ): Promise<Groups[]> {
    return this.checklistRepository.groups(id).find(filter);
  }

  @post('/checklists/{id}/groups', {
    responses: {
      '200': {
        description: 'create a Groups model instance',
        content: {'application/json': {schema: getModelSchemaRef(Groups)}},
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof Checklist.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Groups, {
            title: 'NewGroupsInChecklist',
            exclude: ['id'],
          }),
        },
      },
    }) groups: Omit<Groups, 'id'>,
  ): Promise<Groups> {
    return this.checklistRepository.groups(id).create(groups);
  }

  @patch('/checklists/{id}/groups', {
    responses: {
      '200': {
        description: 'Checklist.Groups PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Groups, {partial: true}),
        },
      },
    })
    groups: Partial<Groups>,
    @param.query.object('where', getWhereSchemaFor(Groups)) where?: Where<Groups>,
  ): Promise<Count> {
    return this.checklistRepository.groups(id).patch(groups, where);
  }

  @del('/checklists/{id}/groups', {
    responses: {
      '200': {
        description: 'Checklist.Groups DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(Groups)) where?: Where<Groups>,
  ): Promise<Count> {
    return this.checklistRepository.groups(id).delete(where);
  }
}
