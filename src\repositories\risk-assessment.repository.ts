import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, BelongsToAccessor, HasManyRepositoryFactory} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {RiskAssessment, RiskAssessmentRelations, User, RiskUpdate} from '../models';
import {UserRepository} from './user.repository';
import {RiskUpdateRepository} from './risk-update.repository';

export class RiskAssessmentRepository extends DefaultCrudRepository<
  RiskAssessment,
  typeof RiskAssessment.prototype.id,
  RiskAssessmentRelations
> {

  public readonly user: BelongsToAccessor<User, typeof RiskAssessment.prototype.id>;

  public readonly riskUpdates: HasManyRepositoryFactory<RiskUpdate, typeof RiskAssessment.prototype.id>;

  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource, @repository.getter('UserRepository') protected userRepositoryGetter: Getter<UserRepository>, @repository.getter('RiskUpdateRepository') protected riskUpdateRepositoryGetter: Getter<RiskUpdateRepository>,
  ) {
    super(RiskAssessment, dataSource);
    this.riskUpdates = this.createHasManyRepositoryFactoryFor('riskUpdates', riskUpdateRepositoryGetter,);
    this.registerInclusionResolver('riskUpdates', this.riskUpdates.inclusionResolver);
    this.user = this.createBelongsToAccessorFor('user', userRepositoryGetter,);
    this.registerInclusionResolver('user', this.user.inclusionResolver);
   
  }
}
