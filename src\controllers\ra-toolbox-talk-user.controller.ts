import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  RaToolboxTalk,
  User,
} from '../models';
import {RaToolboxTalkRepository} from '../repositories';

export class RaToolboxTalkUserController {
  constructor(
    @repository(RaToolboxTalkRepository)
    public raToolboxTalkRepository: RaToolboxTalkRepository,
  ) { }

  @get('/ra-toolbox-talks/{id}/user', {
    responses: {
      '200': {
        description: 'User belonging to RaToolboxTalk',
        content: {
          'application/json': {
            schema: getModelSchemaRef(User),
          },
        },
      },
    },
  })
  async getUser(
    @param.path.string('id') id: typeof RaToolboxTalk.prototype.id,
  ): Promise<User> {
    return this.raToolboxTalkRepository.user(id);
  }
}
