import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  ObservationReport,
  ObservationType,
} from '../models';
import {ObservationReportRepository} from '../repositories';

export class ObservationReportObservationTypeController {
  constructor(
    @repository(ObservationReportRepository)
    public observationReportRepository: ObservationReportRepository,
  ) { }

  @get('/observation-reports/{id}/observation-type', {
    responses: {
      '200': {
        description: 'ObservationType belonging to ObservationReport',
        content: {
          'application/json': {
            schema: getModelSchemaRef(ObservationType),
          },
        },
      },
    },
  })
  async getObservationType(
    @param.path.string('id') id: typeof ObservationReport.prototype.id,
  ): Promise<ObservationType> {
    return this.observationReportRepository.observationType(id);
  }
}
