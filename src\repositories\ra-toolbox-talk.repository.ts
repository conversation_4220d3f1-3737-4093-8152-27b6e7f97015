import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, BelongsToAccessor} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {RaToolboxTalk, RaToolboxTalkRelations, RiskAssessment, LocationOne, LocationTwo, LocationThree, LocationFour, LocationFive, LocationSix, User} from '../models';
import {RiskAssessmentRepository} from './risk-assessment.repository';
import {LocationOneRepository} from './location-one.repository';
import {LocationTwoRepository} from './location-two.repository';
import {LocationThreeRepository} from './location-three.repository';
import {LocationFourRepository} from './location-four.repository';
import {LocationFiveRepository} from './location-five.repository';
import {LocationSixRepository} from './location-six.repository';
import {UserRepository} from './user.repository';

export class RaToolboxTalkRepository extends DefaultCrudRepository<
  RaToolboxTalk,
  typeof RaToolboxTalk.prototype.id,
  RaToolboxTalkRelations
> {

  public readonly riskAssessment: BelongsToAccessor<RiskAssessment, typeof RaToolboxTalk.prototype.id>;

  public readonly locationOne: BelongsToAccessor<LocationOne, typeof RaToolboxTalk.prototype.id>;

  public readonly locationTwo: BelongsToAccessor<LocationTwo, typeof RaToolboxTalk.prototype.id>;

  public readonly locationThree: BelongsToAccessor<LocationThree, typeof RaToolboxTalk.prototype.id>;

  public readonly locationFour: BelongsToAccessor<LocationFour, typeof RaToolboxTalk.prototype.id>;

  public readonly locationFive: BelongsToAccessor<LocationFive, typeof RaToolboxTalk.prototype.id>;

  public readonly locationSix: BelongsToAccessor<LocationSix, typeof RaToolboxTalk.prototype.id>;

  public readonly user: BelongsToAccessor<User, typeof RaToolboxTalk.prototype.id>;

  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource, @repository.getter('RiskAssessmentRepository') protected riskAssessmentRepositoryGetter: Getter<RiskAssessmentRepository>, @repository.getter('LocationOneRepository') protected locationOneRepositoryGetter: Getter<LocationOneRepository>, @repository.getter('LocationTwoRepository') protected locationTwoRepositoryGetter: Getter<LocationTwoRepository>, @repository.getter('LocationThreeRepository') protected locationThreeRepositoryGetter: Getter<LocationThreeRepository>, @repository.getter('LocationFourRepository') protected locationFourRepositoryGetter: Getter<LocationFourRepository>, @repository.getter('LocationFiveRepository') protected locationFiveRepositoryGetter: Getter<LocationFiveRepository>, @repository.getter('LocationSixRepository') protected locationSixRepositoryGetter: Getter<LocationSixRepository>, @repository.getter('UserRepository') protected userRepositoryGetter: Getter<UserRepository>,
  ) {
    super(RaToolboxTalk, dataSource);
    this.user = this.createBelongsToAccessorFor('user', userRepositoryGetter,);
    this.registerInclusionResolver('user', this.user.inclusionResolver);
    this.locationSix = this.createBelongsToAccessorFor('locationSix', locationSixRepositoryGetter,);
    this.registerInclusionResolver('locationSix', this.locationSix.inclusionResolver);
    this.locationFive = this.createBelongsToAccessorFor('locationFive', locationFiveRepositoryGetter,);
    this.registerInclusionResolver('locationFive', this.locationFive.inclusionResolver);
    this.locationFour = this.createBelongsToAccessorFor('locationFour', locationFourRepositoryGetter,);
    this.registerInclusionResolver('locationFour', this.locationFour.inclusionResolver);
    this.locationThree = this.createBelongsToAccessorFor('locationThree', locationThreeRepositoryGetter,);
    this.registerInclusionResolver('locationThree', this.locationThree.inclusionResolver);
    this.locationTwo = this.createBelongsToAccessorFor('locationTwo', locationTwoRepositoryGetter,);
    this.registerInclusionResolver('locationTwo', this.locationTwo.inclusionResolver);
    this.locationOne = this.createBelongsToAccessorFor('locationOne', locationOneRepositoryGetter,);
    this.registerInclusionResolver('locationOne', this.locationOne.inclusionResolver);
    this.riskAssessment = this.createBelongsToAccessorFor('riskAssessment', riskAssessmentRepositoryGetter,);
    this.registerInclusionResolver('riskAssessment', this.riskAssessment.inclusionResolver);
  }
}
