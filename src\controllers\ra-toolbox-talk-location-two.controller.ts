import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  RaToolboxTalk,
  LocationTwo,
} from '../models';
import {RaToolboxTalkRepository} from '../repositories';

export class RaToolboxTalkLocationTwoController {
  constructor(
    @repository(RaToolboxTalkRepository)
    public raToolboxTalkRepository: RaToolboxTalkRepository,
  ) { }

  @get('/ra-toolbox-talks/{id}/location-two', {
    responses: {
      '200': {
        description: 'LocationTwo belonging to RaToolboxTalk',
        content: {
          'application/json': {
            schema: getModelSchemaRef(LocationTwo),
          },
        },
      },
    },
  })
  async getLocationTwo(
    @param.path.string('id') id: typeof RaToolboxTalk.prototype.id,
  ): Promise<LocationTwo> {
    return this.raToolboxTalkRepository.locationTwo(id);
  }
}
