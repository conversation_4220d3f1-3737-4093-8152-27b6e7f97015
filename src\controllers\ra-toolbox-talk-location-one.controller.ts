import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  RaToolboxTalk,
  LocationOne,
} from '../models';
import {RaToolboxTalkRepository} from '../repositories';

export class RaToolboxTalkLocationOneController {
  constructor(
    @repository(RaToolboxTalkRepository)
    public raToolboxTalkRepository: RaToolboxTalkRepository,
  ) { }

  @get('/ra-toolbox-talks/{id}/location-one', {
    responses: {
      '200': {
        description: 'LocationOne belonging to RaToolboxTalk',
        content: {
          'application/json': {
            schema: getModelSchemaRef(LocationOne),
          },
        },
      },
    },
  })
  async getLocationOne(
    @param.path.string('id') id: typeof RaToolboxTalk.prototype.id,
  ): Promise<LocationOne> {
    return this.raToolboxTalkRepository.locationOne(id);
  }
}
