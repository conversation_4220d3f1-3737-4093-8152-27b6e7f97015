import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  AirReport,
  GhsOne,
} from '../models';
import {AirReportRepository} from '../repositories';

export class AirReportGhsOneController {
  constructor(
    @repository(AirReportRepository)
    public airReportRepository: AirReportRepository,
  ) { }

  @get('/air-reports/{id}/ghs-one', {
    responses: {
      '200': {
        description: 'GhsOne belonging to AirReport',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(GhsOne)},
          },
        },
      },
    },
  })
  async getGhsOne(
    @param.path.string('id') id: typeof AirReport.prototype.id,
  ): Promise<GhsOne> {
    return this.airReportRepository.workActivityDepartment(id);
  }
}
