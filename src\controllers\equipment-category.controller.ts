import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {EquipmentCategory} from '../models';
import {EquipmentCategoryRepository} from '../repositories';

export class EquipmentCategoryController {
  constructor(
    @repository(EquipmentCategoryRepository)
    public equipmentCategoryRepository : EquipmentCategoryRepository,
  ) {}

  @post('/equipment-categories')
  @response(200, {
    description: 'EquipmentCategory model instance',
    content: {'application/json': {schema: getModelSchemaRef(EquipmentCategory)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(EquipmentCategory, {
            title: 'NewEquipmentCategory',
            exclude: ['id'],
          }),
        },
      },
    })
    equipmentCategory: Omit<EquipmentCategory, 'id'>,
  ): Promise<EquipmentCategory> {
    return this.equipmentCategoryRepository.create(equipmentCategory);
  }

  @get('/equipment-categories/count')
  @response(200, {
    description: 'EquipmentCategory model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(EquipmentCategory) where?: Where<EquipmentCategory>,
  ): Promise<Count> {
    return this.equipmentCategoryRepository.count(where);
  }

  @get('/equipment-categories')
  @response(200, {
    description: 'Array of EquipmentCategory model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(EquipmentCategory, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(EquipmentCategory) filter?: Filter<EquipmentCategory>,
  ): Promise<EquipmentCategory[]> {
    return this.equipmentCategoryRepository.find(filter);
  }

  @patch('/equipment-categories')
  @response(200, {
    description: 'EquipmentCategory PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(EquipmentCategory, {partial: true}),
        },
      },
    })
    equipmentCategory: EquipmentCategory,
    @param.where(EquipmentCategory) where?: Where<EquipmentCategory>,
  ): Promise<Count> {
    return this.equipmentCategoryRepository.updateAll(equipmentCategory, where);
  }

  @get('/equipment-categories/{id}')
  @response(200, {
    description: 'EquipmentCategory model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(EquipmentCategory, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(EquipmentCategory, {exclude: 'where'}) filter?: FilterExcludingWhere<EquipmentCategory>
  ): Promise<EquipmentCategory> {
    return this.equipmentCategoryRepository.findById(id, filter);
  }

  @patch('/equipment-categories/{id}')
  @response(204, {
    description: 'EquipmentCategory PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(EquipmentCategory, {partial: true}),
        },
      },
    })
    equipmentCategory: EquipmentCategory,
  ): Promise<void> {
    await this.equipmentCategoryRepository.updateById(id, equipmentCategory);
  }

  @put('/equipment-categories/{id}')
  @response(204, {
    description: 'EquipmentCategory PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() equipmentCategory: EquipmentCategory,
  ): Promise<void> {
    await this.equipmentCategoryRepository.replaceById(id, equipmentCategory);
  }

  @del('/equipment-categories/{id}')
  @response(204, {
    description: 'EquipmentCategory DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.equipmentCategoryRepository.deleteById(id);
  }
}
