import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {GeneralGroup} from '../models';
import {GeneralGroupRepository} from '../repositories';

export class GeneralGroupController {
  constructor(
    @repository(GeneralGroupRepository)
    public generalGroupRepository : GeneralGroupRepository,
  ) {}

  @post('/general-groups')
  @response(200, {
    description: 'GeneralGroup model instance',
    content: {'application/json': {schema: getModelSchemaRef(GeneralGroup)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(GeneralGroup, {
            title: 'NewGeneralGroup',
            exclude: ['id'],
          }),
        },
      },
    })
    generalGroup: Omit<GeneralGroup, 'id'>,
  ): Promise<GeneralGroup> {
    return this.generalGroupRepository.create(generalGroup);
  }

  @get('/general-groups/count')
  @response(200, {
    description: 'GeneralGroup model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(GeneralGroup) where?: Where<GeneralGroup>,
  ): Promise<Count> {
    return this.generalGroupRepository.count(where);
  }

  @get('/general-groups')
  @response(200, {
    description: 'Array of GeneralGroup model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(GeneralGroup, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(GeneralGroup) filter?: Filter<GeneralGroup>,
  ): Promise<GeneralGroup[]> {
    return this.generalGroupRepository.find(filter);
  }

  @patch('/general-groups')
  @response(200, {
    description: 'GeneralGroup PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(GeneralGroup, {partial: true}),
        },
      },
    })
    generalGroup: GeneralGroup,
    @param.where(GeneralGroup) where?: Where<GeneralGroup>,
  ): Promise<Count> {
    return this.generalGroupRepository.updateAll(generalGroup, where);
  }

  @get('/general-groups/{id}')
  @response(200, {
    description: 'GeneralGroup model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(GeneralGroup, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(GeneralGroup, {exclude: 'where'}) filter?: FilterExcludingWhere<GeneralGroup>
  ): Promise<GeneralGroup> {
    return this.generalGroupRepository.findById(id, filter);
  }

  @patch('/general-groups/{id}')
  @response(204, {
    description: 'GeneralGroup PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(GeneralGroup, {partial: true}),
        },
      },
    })
    generalGroup: GeneralGroup,
  ): Promise<void> {
    await this.generalGroupRepository.updateById(id, generalGroup);
  }

  @put('/general-groups/{id}')
  @response(204, {
    description: 'GeneralGroup PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() generalGroup: GeneralGroup,
  ): Promise<void> {
    await this.generalGroupRepository.replaceById(id, generalGroup);
  }

  @del('/general-groups/{id}')
  @response(204, {
    description: 'GeneralGroup DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.generalGroupRepository.deleteById(id);
  }
}
