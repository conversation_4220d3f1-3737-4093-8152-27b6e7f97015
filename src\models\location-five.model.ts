import { Entity, model, property, hasMany} from '@loopback/repository';
import {LocationSix} from './location-six.model';
import {ObservationReport} from './observation-report.model';
import {PermitReport} from './permit-report.model';

@model()
export class LocationFive extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'string',
  })
  description?: string;

  @property({
    type: 'string',
  })
  created?: string;

  @property({
    type: 'string',
  })
  modified?: string;

  @property({
    type: 'string',
  })
  locationFourId?: string;

  @hasMany(() => LocationSix)
  locationSixes: LocationSix[];

  @hasMany(() => ObservationReport)
  observationReports: ObservationReport[];

  @hasMany(() => PermitReport)
  permitReports: PermitReport[];

  constructor(data?: Partial<LocationFive>) {
    super(data);
  }
}

export interface LocationFiveRelations {
  // describe navigational properties here
}

export type LocationFiveWithRelations = LocationFive & LocationFiveRelations;
