import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  AirReport,
  LocationTwo,
} from '../models';
import {AirReportRepository} from '../repositories';

export class AirReportLocationTwoController {
  constructor(
    @repository(AirReportRepository)
    public airReportRepository: AirReportRepository,
  ) { }

  @get('/air-reports/{id}/location-two', {
    responses: {
      '200': {
        description: 'LocationTwo belonging to AirReport',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(LocationTwo)},
          },
        },
      },
    },
  })
  async getLocationTwo(
    @param.path.string('id') id: typeof AirReport.prototype.id,
  ): Promise<LocationTwo> {
    return this.airReportRepository.locationTwo(id);
  }
}
