import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  Area,
  Topic,
} from '../models';
import {AreaRepository} from '../repositories';

export class AreaTopicController {
  constructor(
    @repository(AreaRepository) protected areaRepository: AreaRepository,
  ) { }

  @get('/areas/{id}/topics', {
    responses: {
      '200': {
        description: 'Array of Area has many Topic',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(Topic)},
          },
        },
      },
    },
  })
  async find(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<Topic>,
  ): Promise<Topic[]> {
    return this.areaRepository.topics(id).find(filter);
  }

  @post('/areas/{id}/topics', {
    responses: {
      '200': {
        description: 'Area model instance',
        content: {'application/json': {schema: getModelSchemaRef(Topic)}},
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof Area.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Topic, {
            title: 'NewTopicInArea',
            exclude: ['id'],
            optional: ['areaId']
          }),
        },
      },
    }) topic: Omit<Topic, 'id'>,
  ): Promise<Topic> {
    return this.areaRepository.topics(id).create(topic);
  }

  @patch('/areas/{id}/topics', {
    responses: {
      '200': {
        description: 'Area.Topic PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Topic, {partial: true}),
        },
      },
    })
    topic: Partial<Topic>,
    @param.query.object('where', getWhereSchemaFor(Topic)) where?: Where<Topic>,
  ): Promise<Count> {
    return this.areaRepository.topics(id).patch(topic, where);
  }

  @del('/areas/{id}/topics', {
    responses: {
      '200': {
        description: 'Area.Topic DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(Topic)) where?: Where<Topic>,
  ): Promise<Count> {
    return this.areaRepository.topics(id).delete(where);
  }
}
