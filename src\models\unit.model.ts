import {Entity, model, property, hasMany} from '@loopback/repository';
import {StepTitle} from './step-title.model';
import {User} from './user.model';
import {UserUnitAllocation} from './user-unit-allocation.model';
import {Groups} from './groups.model';
import {GroupUnit} from './group-unit.model';

@model({settings: {strict: false}})
export class Unit extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
    required: true,
  })
  title: string;

  @property({
    type: 'string',
  })
  description?: string;

  @property({
    type: 'number',
  })
  order?: number;

  @property({
    type: 'string',
  })
  createdAt?: string;

  @property({
    type: 'string',
  })
  topicId?: string;

  @hasMany(() => StepTitle)
  stepTitles: StepTitle[];

  @hasMany(() => User, {through: {model: () => UserUnitAllocation}})
  users: User[];

  @hasMany(() => Groups, {through: {model: () => GroupUnit}})
  groups: Groups[];
  // Define well-known properties here

  // Indexer property to allow additional data
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [prop: string]: any;

  constructor(data?: Partial<Unit>) {
    super(data);
  }
}

export interface UnitRelations {
  // describe navigational properties here
}

export type UnitWithRelations = Unit & UnitRelations;
