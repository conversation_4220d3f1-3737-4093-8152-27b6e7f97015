import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  AirReport,
  GhsTwo,
} from '../models';
import {AirReportRepository} from '../repositories';

export class AirReportGhsTwoController {
  constructor(
    @repository(AirReportRepository)
    public airReportRepository: AirReportRepository,
  ) { }

  @get('/air-reports/{id}/ghs-two', {
    responses: {
      '200': {
        description: 'GhsTwo belonging to AirReport',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(GhsTwo)},
          },
        },
      },
    },
  })
  async getGhsTwo(
    @param.path.string('id') id: typeof AirReport.prototype.id,
  ): Promise<GhsTwo> {
    return this.airReportRepository.workActivity(id);
  }
}
