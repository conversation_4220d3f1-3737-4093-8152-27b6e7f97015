import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {Ppe, PpeRelations} from '../models';

export class PpeRepository extends DefaultCrudRepository<
  Ppe,
  typeof Ppe.prototype.id,
  PpeRelations
> {
  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource,
  ) {
    super(Ppe, dataSource);
  }
}
