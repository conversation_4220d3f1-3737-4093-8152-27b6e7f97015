import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import { LocationFive } from '../models';
import { LocationFiveRepository } from '../repositories';
import { authenticate } from '@loopback/authentication';
@authenticate('jwt')
export class LocationFiveController {
  constructor(
    @repository(LocationFiveRepository)
    public locationFiveRepository: LocationFiveRepository,
  ) { }

  @post('/location-fives')
  @response(200, {
    description: 'LocationFive model instance',
    content: { 'application/json': { schema: getModelSchemaRef(LocationFive) } },
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(LocationFive, {
            title: 'NewLocationFive',
            exclude: ['id'],
          }),
        },
      },
    })
    locationFive: Omit<LocationFive, 'id'>,
  ): Promise<LocationFive> {
    return this.locationFiveRepository.create(locationFive);
  }

  @get('/location-fives/count')
  @response(200, {
    description: 'LocationFive model count',
    content: { 'application/json': { schema: CountSchema } },
  })
  async count(
    @param.where(LocationFive) where?: Where<LocationFive>,
  ): Promise<Count> {
    return this.locationFiveRepository.count(where);
  }

  @get('/location-fives')
  @response(200, {
    description: 'Array of LocationFive model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'string' },
              title: { type: 'string' },
            },
          },
        },
      },
    },
  })
  async find(
    @param.filter(LocationFive) filter?: Filter<LocationFive>,
  ): Promise<{ id: string; title: string }[]> {
    const locationFives = await this.locationFiveRepository.find(filter);

    // Transform locationFives into the desired format
    const transformedLocationFives = locationFives.map(locationFive => ({
      id: locationFive.id ?? '',
      title: locationFive.name ?? '',
      ...locationFive
    }));

    return transformedLocationFives;
  }

  @patch('/location-fives')
  @response(200, {
    description: 'LocationFive PATCH success count',
    content: { 'application/json': { schema: CountSchema } },
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(LocationFive, { partial: true }),
        },
      },
    })
    locationFive: LocationFive,
    @param.where(LocationFive) where?: Where<LocationFive>,
  ): Promise<Count> {
    return this.locationFiveRepository.updateAll(locationFive, where);
  }

  @get('/location-fives/{id}')
  @response(200, {
    description: 'LocationFive model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(LocationFive, { includeRelations: true }),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(LocationFive, { exclude: 'where' }) filter?: FilterExcludingWhere<LocationFive>
  ): Promise<LocationFive> {
    return this.locationFiveRepository.findById(id, filter);
  }

  @patch('/location-fives/{id}')
  @response(204, {
    description: 'LocationFive PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(LocationFive, { partial: true }),
        },
      },
    })
    locationFive: LocationFive,
  ): Promise<void> {
    await this.locationFiveRepository.updateById(id, locationFive);
  }

  @put('/location-fives/{id}')
  @response(204, {
    description: 'LocationFive PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() locationFive: LocationFive,
  ): Promise<void> {
    await this.locationFiveRepository.replaceById(id, locationFive);
  }

  @del('/location-fives/{id}')
  @response(204, {
    description: 'LocationFive DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.locationFiveRepository.deleteById(id);
  }
}
