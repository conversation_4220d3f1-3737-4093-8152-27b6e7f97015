import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  AirReport,
  LocationSix,
} from '../models';
import {AirReportRepository} from '../repositories';

export class AirReportLocationSixController {
  constructor(
    @repository(AirReportRepository)
    public airReportRepository: AirReportRepository,
  ) { }

  @get('/air-reports/{id}/location-six', {
    responses: {
      '200': {
        description: 'LocationSix belonging to AirReport',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(LocationSix)},
          },
        },
      },
    },
  })
  async getLocationSix(
    @param.path.string('id') id: typeof AirReport.prototype.id,
  ): Promise<LocationSix> {
    return this.airReportRepository.locationSix(id);
  }
}
