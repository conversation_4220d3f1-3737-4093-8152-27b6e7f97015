import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  HumanPartSecondary,
  HumanPartTertiary,
} from '../models';
import {HumanPartSecondaryRepository} from '../repositories';

export class HumanPartSecondaryHumanPartTertiaryController {
  constructor(
    @repository(HumanPartSecondaryRepository) protected humanPartSecondaryRepository: HumanPartSecondaryRepository,
  ) { }

  @get('/human-part-secondaries/{id}/human-part-tertiaries', {
    responses: {
      '200': {
        description: 'Array of HumanPartSecondary has many HumanPartTertiary',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(HumanPartTertiary)},
          },
        },
      },
    },
  })
  async find(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<HumanPartTertiary>,
  ): Promise<HumanPartTertiary[]> {
    return this.humanPartSecondaryRepository.humanPartTertiaries(id).find(filter);
  }

  @post('/human-part-secondaries/{id}/human-part-tertiaries', {
    responses: {
      '200': {
        description: 'HumanPartSecondary model instance',
        content: {'application/json': {schema: getModelSchemaRef(HumanPartTertiary)}},
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof HumanPartSecondary.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(HumanPartTertiary, {
            title: 'NewHumanPartTertiaryInHumanPartSecondary',
            exclude: ['id'],
            optional: ['humanPartSecondaryId']
          }),
        },
      },
    }) humanPartTertiary: Omit<HumanPartTertiary, 'id'>,
  ): Promise<HumanPartTertiary> {
    return this.humanPartSecondaryRepository.humanPartTertiaries(id).create(humanPartTertiary);
  }

  @patch('/human-part-secondaries/{id}/human-part-tertiaries', {
    responses: {
      '200': {
        description: 'HumanPartSecondary.HumanPartTertiary PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(HumanPartTertiary, {partial: true}),
        },
      },
    })
    humanPartTertiary: Partial<HumanPartTertiary>,
    @param.query.object('where', getWhereSchemaFor(HumanPartTertiary)) where?: Where<HumanPartTertiary>,
  ): Promise<Count> {
    return this.humanPartSecondaryRepository.humanPartTertiaries(id).patch(humanPartTertiary, where);
  }

  @del('/human-part-secondaries/{id}/human-part-tertiaries', {
    responses: {
      '200': {
        description: 'HumanPartSecondary.HumanPartTertiary DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(HumanPartTertiary)) where?: Where<HumanPartTertiary>,
  ): Promise<Count> {
    return this.humanPartSecondaryRepository.humanPartTertiaries(id).delete(where);
  }
}
