import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
  import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
Documents,
GroupDocumentAllocation,
Groups,
} from '../models';
import {DocumentsRepository} from '../repositories';

export class DocumentsGroupsController {
  constructor(
    @repository(DocumentsRepository) protected documentsRepository: DocumentsRepository,
  ) { }

  @get('/documents/{id}/groups', {
    responses: {
      '200': {
        description: 'Array of Documents has many Groups through GroupDocumentAllocation',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(Groups)},
          },
        },
      },
    },
  })
  async find(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<Groups>,
  ): Promise<Groups[]> {
    return this.documentsRepository.groups(id).find(filter);
  }

  @post('/documents/{id}/groups', {
    responses: {
      '200': {
        description: 'create a Groups model instance',
        content: {'application/json': {schema: getModelSchemaRef(Groups)}},
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof Documents.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Groups, {
            title: 'NewGroupsInDocuments',
            exclude: ['id'],
          }),
        },
      },
    }) groups: Omit<Groups, 'id'>,
  ): Promise<Groups> {
    return this.documentsRepository.groups(id).create(groups);
  }

  @patch('/documents/{id}/groups', {
    responses: {
      '200': {
        description: 'Documents.Groups PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Groups, {partial: true}),
        },
      },
    })
    groups: Partial<Groups>,
    @param.query.object('where', getWhereSchemaFor(Groups)) where?: Where<Groups>,
  ): Promise<Count> {
    return this.documentsRepository.groups(id).patch(groups, where);
  }

  @del('/documents/{id}/groups', {
    responses: {
      '200': {
        description: 'Documents.Groups DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(Groups)) where?: Where<Groups>,
  ): Promise<Count> {
    return this.documentsRepository.groups(id).delete(where);
  }
}
