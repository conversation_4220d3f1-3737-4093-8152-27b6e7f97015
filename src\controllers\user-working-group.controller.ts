import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  User,
  WorkingGroup,
} from '../models';
import {UserRepository} from '../repositories';

export class UserWorkingGroupController {
  constructor(
    @repository(UserRepository)
    public userRepository: UserRepository,
  ) { }

  @get('/users/{id}/working-group', {
    responses: {
      '200': {
        description: 'WorkingGroup belonging to User',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(WorkingGroup)},
          },
        },
      },
    },
  })
  async getWorkingGroup(
    @param.path.string('id') id: typeof User.prototype.id,
  ): Promise<WorkingGroup> {
    return this.userRepository.workingGroup(id);
  }
}
