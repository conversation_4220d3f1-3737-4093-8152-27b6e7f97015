import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
  import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
Groups,
GroupChecklistAllocation,
Checklist,
} from '../models';
import {GroupsRepository} from '../repositories';

export class GroupsChecklistController {
  constructor(
    @repository(GroupsRepository) protected groupsRepository: GroupsRepository,
  ) { }

  @get('/groups/{id}/checklists', {
    responses: {
      '200': {
        description: 'Array of Groups has many Checklist through GroupChecklistAllocation',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(Checklist)},
          },
        },
      },
    },
  })
  async find(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<Checklist>,
  ): Promise<Checklist[]> {
    return this.groupsRepository.checklists(id).find(filter);
  }

  @post('/groups/{id}/checklists', {
    responses: {
      '200': {
        description: 'create a Checklist model instance',
        content: {'application/json': {schema: getModelSchemaRef(Checklist)}},
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof Groups.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Checklist, {
            title: 'NewChecklistInGroups',
            exclude: ['id'],
          }),
        },
      },
    }) checklist: Omit<Checklist, 'id'>,
  ): Promise<Checklist> {
    return this.groupsRepository.checklists(id).create(checklist);
  }

  @patch('/groups/{id}/checklists', {
    responses: {
      '200': {
        description: 'Groups.Checklist PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Checklist, {partial: true}),
        },
      },
    })
    checklist: Partial<Checklist>,
    @param.query.object('where', getWhereSchemaFor(Checklist)) where?: Where<Checklist>,
  ): Promise<Count> {
    return this.groupsRepository.checklists(id).patch(checklist, where);
  }

  @del('/groups/{id}/checklists', {
    responses: {
      '200': {
        description: 'Groups.Checklist DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(Checklist)) where?: Where<Checklist>,
  ): Promise<Count> {
    return this.groupsRepository.checklists(id).delete(where);
  }
}
