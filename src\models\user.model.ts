// Copyright IBM Corp. 2019,2020. All Rights Reserved.
// Node module: loopback4-example-shopping
// This file is licensed under the MIT License.
// License text available at https://opensource.org/licenses/MIT

import { Entity, model, property, hasMany, hasOne, belongsTo } from '@loopback/repository';
import { UserCredentials } from './user-credentials.model';
import { EhsRole } from './ehs-role.model';
import { UserLocation } from './user-location.model';
import { UserLocationRole } from './user-location-role.model';
import { ReportIncident } from './report-incident.model';
import { TowerCrane } from './tower-crane.model';
import { PermitReport } from './permit-report.model';
import { Department } from './department.model';
import { Unit } from './unit.model';
import { UserUnitAllocation } from './user-unit-allocation.model';
import { Groups } from './groups.model';
import { GroupUser } from './group-user.model';
import { Checklist } from './checklist.model';
import { UserChecklistAllocation } from './user-checklist-allocation.model';
import { Documents } from './documents.model';
import { UserDocumentAllocation } from './user-document-allocation.model';
import { Designation } from './designation.model';
import { WorkingGroup } from './working-group.model';
import { GhsOne } from './ghs-one.model';

// import {Action} from './action.model';

@model({
  settings: {
    indexes: {
      uniqueEmail: {
        keys: {
          email: 1,
        },
        options: {
          unique: true,
        },
      },
    },
  },
})
export class User extends Entity {


  @property({
    type: 'string',
    id: true,
  })
  id: string;

  @property({
    type: 'string',
    required: true,
  })
  email: string;

  @property({
    type: 'string',

  })
  deviceToken?: string;

  @property({
    type: 'string',

  })
  arn?: string;

  @property({
    type: 'string',
  })
  firstName?: string;

  @property({
    type: 'string',
  })
  lastName?: string;

  @property({
    type: 'string',
  })
  empId?: string;

  @property({
    type: 'string',
  })
  status?: string;

  @property({
    type: 'string',
  })
  type?: string;



  @hasOne(() => UserCredentials)
  userCredentials: UserCredentials;



  @property({
    type: 'array',
    itemType: 'string',
  })
  roles?: string[];

  @property({
    type: 'array',
    itemType: 'string',
  })
  ehsRoles?: string[];
  @property({
    type: 'any',
  })
  customRoles?: any;
  @property({
    type: 'string',
  })
  company?: string;

  @property({
    type: 'string',
  })
  resetKey?: string;

  @property({
    type: 'number',
  })
  resetCount: number;

  @property({
    type: 'string',
  })
  resetTimestamp: string;

  @property({
    type: 'string',
  })
  resetKeyTimestamp: string;

  @belongsTo(() => EhsRole)
  ehsRoleId: string;

  @hasOne(() => UserLocation)
  userLocation: UserLocation;

  @hasMany(() => UserLocationRole)
  userLocationRoles: UserLocationRole[];

  @hasMany(() => ReportIncident)
  reportIncidents: ReportIncident[];

  @hasMany(() => TowerCrane, { keyTo: 'applicantId' })
  towerCranes: TowerCrane[];

  @hasMany(() => PermitReport, { keyTo: 'applicantId' })
  applicantReports: PermitReport[];

  @hasMany(() => PermitReport, { keyTo: 'assessorId' })
  assessorReports: PermitReport[];

  @hasMany(() => PermitReport, { keyTo: 'approverId' })
  approverReports: PermitReport[];

  @hasMany(() => PermitReport, { keyTo: 'dcsoApproverId' })
  dscoApproverReports: PermitReport[];

  @belongsTo(() => Department)
  departmentId: string;

  @hasMany(() => Unit, { through: { model: () => UserUnitAllocation } })
  units: Unit[];

  @hasMany(() => Groups, { through: { model: () => GroupUser } })
  groups: Groups[];

  @hasMany(() => Checklist, { through: { model: () => UserChecklistAllocation } })
  checklists: Checklist[];

  @hasMany(() => Documents, { through: { model: () => UserDocumentAllocation } })
  documents: Documents[];

  @belongsTo(() => Designation)
  designationId: string;

  @belongsTo(() => WorkingGroup)
  workingGroupId: string;

  @belongsTo(() => GhsOne)
  ghsOneId: string;

  constructor(data?: Partial<User>) {
    super(data);
  }
}
