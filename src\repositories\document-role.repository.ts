import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {DocumentRole, DocumentRoleRelations} from '../models';

export class DocumentRoleRepository extends DefaultCrudRepository<
  DocumentRole,
  typeof DocumentRole.prototype.id,
  DocumentRoleRelations
> {
  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource,
  ) {
    super(DocumentRole, dataSource);
  }
}
