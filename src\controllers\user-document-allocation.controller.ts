import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {Documents, User, UserDocumentAllocation} from '../models';
import {authenticate} from '@loopback/authentication';
import {OPERATION_SECURITY_SPEC} from '@loopback/authentication-jwt';
import {SecurityBindings, UserProfile, securityId} from '@loopback/security';
import {UserDocumentAllocationRepository,DocumentsRepository,UserRepository,DocumentCategoryRepository} from '../repositories';
import { inject } from '@loopback/core';
type Checklist = {
  documentId: [],
  userid: string,
 
};
export class UserDocumentAllocationController {
  constructor(
    @repository(UserDocumentAllocationRepository)
    public userDocumentAllocationRepository : UserDocumentAllocationRepository,
    @repository(DocumentsRepository)
    public documentsRepository : DocumentsRepository,
    @repository(DocumentCategoryRepository)
    public documentCategoryRepository : DocumentCategoryRepository,
    @repository(UserRepository)
    public userRepository : UserRepository,
  ) {}

  @post('/user-document-allocations')
  @response(200, {
    description: 'UserDocumentAllocation model instance',
    content: {'application/json': {schema: getModelSchemaRef(UserDocumentAllocation)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(UserDocumentAllocation, {
            title: 'NewUserDocumentAllocation',
            exclude: ['id'],
          }),
        },
      },
    })
    userDocumentAllocation: Omit<UserDocumentAllocation, 'id'>,
  ): Promise<UserDocumentAllocation> {
    return this.userDocumentAllocationRepository.create(userDocumentAllocation);
  }

  @get('/user-document-allocations/count')
  @response(200, {
    description: 'UserDocumentAllocation model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(UserDocumentAllocation) where?: Where<UserDocumentAllocation>,
  ): Promise<Count> {
    return this.userDocumentAllocationRepository.count(where);
  }

  @get('/user-document-allocations')
  @response(200, {
    description: 'Array of UserDocumentAllocation model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(UserDocumentAllocation, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(UserDocumentAllocation) filter?: Filter<UserDocumentAllocation>,
  ): Promise<UserDocumentAllocation[]> {
    return this.userDocumentAllocationRepository.find(filter);
  }
  @get('/user-list-documents')
  @response(200, {
    security: OPERATION_SECURITY_SPEC,
    description: 'Array of GroupUserSingle model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(UserDocumentAllocation, {includeRelations: false}),
        },
      },
    },
  })
  @authenticate('jwt')
  async userDocument(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
  ): Promise<Documents[]> {
    // const userId = currentUserProfile[securityId];
    const {email} = currentUserProfile;
    let id =''
    const user : User|null = await this.userRepository.findOne({ where: { email: email } })
    if (user !== null) {
      id = user.id;
    } else {
     
    }
    const risk = await this.userDocumentAllocationRepository.find({
      where: {
        userId: id
      }
    });

    const tierThreeId = risk.map(item => {
      return item.documentsId
    })
    const forms = await this.documentsRepository.find({
      where: {
        id: {inq: tierThreeId}
      }
    });

    for (const document of forms) {

      if (document.initiatorId) {
        document.intial = await this.userRepository.findById(document.initiatorId);
      }
      if (document.creatorId) {
        document.creator = await this.userRepository.findById(document.creatorId);
      }

      if (document.reviewerId) {
        document.reviewer = await this.userRepository.findById(document.reviewerId);
      }

      if (document.approverId) {
        document.approver = await this.userRepository.findById(document.approverId);
      }
      if (document.documentCategoryId) {
        document.cate = await this.documentCategoryRepository.findById(document.documentCategoryId);
      }

    }


    return forms;
  }
  @patch('/user-document-allocations')
  @response(200, {
    description: 'UserDocumentAllocation PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(UserDocumentAllocation, {partial: true}),
        },
      },
    })
    userDocumentAllocation: UserDocumentAllocation,
    @param.where(UserDocumentAllocation) where?: Where<UserDocumentAllocation>,
  ): Promise<Count> {
    return this.userDocumentAllocationRepository.updateAll(userDocumentAllocation, where);
  }

  @get('/user-document-allocations/{id}')
  @response(200, {
    description: 'UserDocumentAllocation model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(UserDocumentAllocation, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(UserDocumentAllocation, {exclude: 'where'}) filter?: FilterExcludingWhere<UserDocumentAllocation>
  ): Promise<UserDocumentAllocation> {
    return this.userDocumentAllocationRepository.findById(id, filter);
  }

  @patch('/user-document-allocations/{id}')
  @response(204, {
    description: 'UserDocumentAllocation PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(UserDocumentAllocation, {partial: true}),
        },
      },
    })
    userDocumentAllocation: UserDocumentAllocation,
  ): Promise<void> {
    await this.userDocumentAllocationRepository.updateById(id, userDocumentAllocation);
  }

  @put('/user-document-allocations/{id}')
  @response(204, {
    description: 'UserDocumentAllocation PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() userDocumentAllocation: UserDocumentAllocation,
  ): Promise<void> {
    await this.userDocumentAllocationRepository.replaceById(id, userDocumentAllocation);
  }

  @del('/user-document-allocations/{id}')
  @response(204, {
    description: 'UserDocumentAllocation DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.userDocumentAllocationRepository.deleteById(id);
  }

  @post('/add-document-user')
  @response(200, {
    description: 'UserTierThree model instance'
  })
  async userThrees(
    @requestBody() threeOne: Checklist,
  ): Promise<void> {
    if (threeOne.documentId.length === 0) {
      // return this.groupUserRepository.create(groupUser);
      await this.userDocumentAllocationRepository.deleteAll({
        and: [
         
          {userId: threeOne.userid},
          {type: 'user'}
        ],

      })

    }
    else {
      const kaOut: (string | undefined)[] = threeOne.documentId;


      const alreadyRa = await this.userDocumentAllocationRepository.find({
        where: {
          "userId": threeOne.userid
        }
      })
      const kaIn = alreadyRa.map(item => {
        return item.documentsId
      })
      const convertedIntArray: (string | undefined)[] = kaIn.map(num => String(num));


      const insertA = kaOut.filter(item => !convertedIntArray.includes(item));

      if (insertA.length !== 0) {
        insertA.map(async i => {
          await this.userDocumentAllocationRepository.create({"userId": threeOne.userid,  "documentsId": i, "type": 'user'});

        })

      }
      const deleteA = convertedIntArray.filter(item => !kaOut.includes(item))

      if (deleteA.length !== 0) {
        deleteA.map(async i => {
          await this.userDocumentAllocationRepository.deleteAll({
            and: [
             
              {documentsId: i},
              {userId: threeOne.userid},
              {type: 'user'}
            ],
          });

        })

      }

    }


  }

  @get('/user-document-userid/{userid}')
  @response(200, {
    description: 'UserTierThree model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(UserDocumentAllocation, {includeRelations: false}),
      },
    },
  })
  async findByUserId(
    @param.path.string('userid') userid: string
  ): Promise<UserDocumentAllocation[]> {
    return this.userDocumentAllocationRepository.find({
      where: {
        userId: userid,
        type:'user'
      },

    });
  }
}
