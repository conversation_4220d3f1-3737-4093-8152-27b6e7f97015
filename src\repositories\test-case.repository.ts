import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, BelongsToAccessor} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {TestCase, TestCaseRelations, Checklist} from '../models';
import {ChecklistRepository} from './checklist.repository';

export class TestCaseRepository extends DefaultCrudRepository<
  TestCase,
  typeof TestCase.prototype.id,
  TestCaseRelations
> {

  public readonly checklist: BelongsToAccessor<Checklist, typeof TestCase.prototype.id>;

  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource, @repository.getter('ChecklistRepository') protected checklistRepositoryGetter: Getter<ChecklistRepository>,
  ) {
    super(TestCase, dataSource);
    this.checklist = this.createBelongsToAccessorFor('checklist', checklistRepositoryGetter,);
    this.registerInclusionResolver('checklist', this.checklist.inclusionResolver);
  }
}
