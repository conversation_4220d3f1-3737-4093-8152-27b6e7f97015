import {Entity, model, property} from '@loopback/repository';

@model()
export class ObservationType extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'string',
  })
  created?: string;


  constructor(data?: Partial<ObservationType>) {
    super(data);
  }
}

export interface ObservationTypeRelations {
  // describe navigational properties here
}

export type ObservationTypeWithRelations = ObservationType & ObservationTypeRelations;
