import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  WorkActivity,
  ObservationReport,
} from '../models';
import {WorkActivityRepository} from '../repositories';

export class WorkActivityObservationReportController {
  constructor(
    @repository(WorkActivityRepository) protected workActivityRepository: WorkActivityRepository,
  ) { }

  @get('/work-activities/{id}/observation-reports', {
    responses: {
      '200': {
        description: 'Array of WorkActivity has many ObservationReport',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(ObservationReport)},
          },
        },
      },
    },
  })
  async find(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<ObservationReport>,
  ): Promise<ObservationReport[]> {
    return this.workActivityRepository.observationReports(id).find(filter);
  }

  @post('/work-activities/{id}/observation-reports', {
    responses: {
      '200': {
        description: 'WorkActivity model instance',
        content: {'application/json': {schema: getModelSchemaRef(ObservationReport)}},
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof WorkActivity.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ObservationReport, {
            title: 'NewObservationReportInWorkActivity',
            exclude: ['id'],
            optional: ['workActivityId']
          }),
        },
      },
    }) observationReport: Omit<ObservationReport, 'id'>,
  ): Promise<ObservationReport> {
    return this.workActivityRepository.observationReports(id).create(observationReport);
  }

  @patch('/work-activities/{id}/observation-reports', {
    responses: {
      '200': {
        description: 'WorkActivity.ObservationReport PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ObservationReport, {partial: true}),
        },
      },
    })
    observationReport: Partial<ObservationReport>,
    @param.query.object('where', getWhereSchemaFor(ObservationReport)) where?: Where<ObservationReport>,
  ): Promise<Count> {
    return this.workActivityRepository.observationReports(id).patch(observationReport, where);
  }

  @del('/work-activities/{id}/observation-reports', {
    responses: {
      '200': {
        description: 'WorkActivity.ObservationReport DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(ObservationReport)) where?: Where<ObservationReport>,
  ): Promise<Count> {
    return this.workActivityRepository.observationReports(id).delete(where);
  }
}
