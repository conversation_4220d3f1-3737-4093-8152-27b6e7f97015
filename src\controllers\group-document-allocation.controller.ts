import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {GroupDocumentAllocation} from '../models';
import {GroupDocumentAllocationRepository,UserDocumentAllocationRepository,GroupsRepository} from '../repositories';
type Checklist = {
  documentId: [],
  groupid: string,
  
};
export class GroupDocumentAllocationController {
  constructor(
    @repository(GroupDocumentAllocationRepository)
    public groupDocumentAllocationRepository : GroupDocumentAllocationRepository,
    @repository(UserDocumentAllocationRepository)
    public userDocumentAllocationRepository: UserDocumentAllocationRepository,
    @repository(GroupsRepository)
    public groupRepository: GroupsRepository,
  ) {}

  @post('/group-document-allocations')
  @response(200, {
    description: 'GroupDocumentAllocation model instance',
    content: {'application/json': {schema: getModelSchemaRef(GroupDocumentAllocation)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(GroupDocumentAllocation, {
            title: 'NewGroupDocumentAllocation',
            exclude: ['id'],
          }),
        },
      },
    })
    groupDocumentAllocation: Omit<GroupDocumentAllocation, 'id'>,
  ): Promise<GroupDocumentAllocation> {
    return this.groupDocumentAllocationRepository.create(groupDocumentAllocation);
  }

  @get('/group-document-allocations/count')
  @response(200, {
    description: 'GroupDocumentAllocation model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(GroupDocumentAllocation) where?: Where<GroupDocumentAllocation>,
  ): Promise<Count> {
    return this.groupDocumentAllocationRepository.count(where);
  }

  @get('/group-document-allocations')
  @response(200, {
    description: 'Array of GroupDocumentAllocation model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(GroupDocumentAllocation, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(GroupDocumentAllocation) filter?: Filter<GroupDocumentAllocation>,
  ): Promise<GroupDocumentAllocation[]> {
    return this.groupDocumentAllocationRepository.find(filter);
  }

  @patch('/group-document-allocations')
  @response(200, {
    description: 'GroupDocumentAllocation PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(GroupDocumentAllocation, {partial: true}),
        },
      },
    })
    groupDocumentAllocation: GroupDocumentAllocation,
    @param.where(GroupDocumentAllocation) where?: Where<GroupDocumentAllocation>,
  ): Promise<Count> {
    return this.groupDocumentAllocationRepository.updateAll(groupDocumentAllocation, where);
  }

  @get('/group-document-allocations/{id}')
  @response(200, {
    description: 'GroupDocumentAllocation model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(GroupDocumentAllocation, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(GroupDocumentAllocation, {exclude: 'where'}) filter?: FilterExcludingWhere<GroupDocumentAllocation>
  ): Promise<GroupDocumentAllocation> {
    return this.groupDocumentAllocationRepository.findById(id, filter);
  }

  @patch('/group-document-allocations/{id}')
  @response(204, {
    description: 'GroupDocumentAllocation PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(GroupDocumentAllocation, {partial: true}),
        },
      },
    })
    groupDocumentAllocation: GroupDocumentAllocation,
  ): Promise<void> {
    await this.groupDocumentAllocationRepository.updateById(id, groupDocumentAllocation);
  }

  @put('/group-document-allocations/{id}')
  @response(204, {
    description: 'GroupDocumentAllocation PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() groupDocumentAllocation: GroupDocumentAllocation,
  ): Promise<void> {
    await this.groupDocumentAllocationRepository.replaceById(id, groupDocumentAllocation);
  }

  @del('/group-document-allocations/{id}')
  @response(204, {
    description: 'GroupDocumentAllocation DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.groupDocumentAllocationRepository.deleteById(id);
  }
  @get('/group-document-groupid/{id}')
  @response(200, {
    description: 'GroupAssignTierThree model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(GroupDocumentAllocation, {includeRelations: true}),
      },
    },
  })
  async findGroupById(
    @param.path.string('id') id: string,
    @param.filter(GroupDocumentAllocation, {exclude: 'where'}) filter?: FilterExcludingWhere<GroupDocumentAllocation>
  ): Promise<GroupDocumentAllocation[]> {
    return this.groupDocumentAllocationRepository.find({
      where: {
        groupId: id
      }
    });
  }

  @post('/add-document-group-user')
  @response(200, {
    description: 'UserTierThree model instance'
  })
  async userThrees(
    @requestBody() threeOne: Checklist,
  ): Promise<void> {
    if (threeOne.documentId.length === 0) {
      // return this.groupUserRepository.create(groupUser);
      const user = await this.groupRepository.users(threeOne.groupid).find()
      await this.groupDocumentAllocationRepository.deleteAll({
        and: [
         
          {groupsId: threeOne.groupid},

        ],

      })
      user.map(async item => {
        await this.userDocumentAllocationRepository.deleteAll({
          and: [
           
            {userId: item.id},
            {groupId: threeOne.groupid},
            {type: 'group'}
          ],

        })
      })



    }
    else {

      // group allocation part
      const kaOut: (string | undefined)[] = threeOne.documentId;


      const alreadyRa = await this.groupDocumentAllocationRepository.find({
        where: {
          "groupsId": threeOne.groupid
        }
      })
      const kaIn = alreadyRa.map(item => {
        return item.documentsId
      })
      const convertedIntArray: (string | undefined)[] = kaIn.map(num => String(num));


      const insertA = kaOut.filter(item => !convertedIntArray.includes(item));

      if (insertA.length !== 0) {
        insertA.map(async i => {
          await this.groupDocumentAllocationRepository.create({"groupsId": threeOne.groupid, "documentsId": i});

        })

      }
      const deleteA = convertedIntArray.filter(item => !kaOut.includes(item))

      if (deleteA.length !== 0) {
        deleteA.map(async i => {
          await this.groupDocumentAllocationRepository.deleteAll({
            and: [
             
              {documentsId: i},
              {groupsId: threeOne.groupid}
            ],
          });

        })

      }


      //Unit allocation part
      const user = await this.groupRepository.users(threeOne.groupid).find();
      if (user.length !== 0) {
        user.map(async item => {
          const alreadyKu = await this.userDocumentAllocationRepository.find({
            where: {
              userId: item.id
            }
          })
          const kaUserIn = alreadyKu.map(k => {
            return k.documentsId
          })
          const convertedInt: (string | undefined)[] = kaUserIn.map(num => String(num));


          const insertKaUser = kaOut.filter(it => !convertedInt.includes(it));


          if (insertKaUser.length !== 0) {
            insertKaUser.map(async i => {
              await this.userDocumentAllocationRepository.create({
                "userId": item.id,
                
                "documentsId": i,
                "groupId": threeOne.groupid,
                "type": 'group'
              });

            })
          }
          const deleteKaUser = convertedInt.filter(ite => !kaOut.includes(ite));
          if (deleteKaUser.length !== 0) {
            deleteKaUser.map(async i => {
              await this.userDocumentAllocationRepository.deleteAll({
                and: [
                  
                  {groupId: threeOne.groupid},
                  {documentsId: i},
                  {userId: item.id},
                  {type: 'group'}
                ],
              });

            })
          }

        })
      }


    }


  }
}
