import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {HumanPartSecondary} from '../models';
import {HumanPartSecondaryRepository} from '../repositories';

export class HumanPartSecondaryController {
  constructor(
    @repository(HumanPartSecondaryRepository)
    public humanPartSecondaryRepository : HumanPartSecondaryRepository,
  ) {}

  @post('/human-part-secondaries')
  @response(200, {
    description: 'HumanPartSecondary model instance',
    content: {'application/json': {schema: getModelSchemaRef(HumanPartSecondary)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(HumanPartSecondary, {
            title: 'NewHumanPartSecondary',
            exclude: ['id'],
          }),
        },
      },
    })
    humanPartSecondary: Omit<HumanPartSecondary, 'id'>,
  ): Promise<HumanPartSecondary> {
    return this.humanPartSecondaryRepository.create(humanPartSecondary);
  }

  @get('/human-part-secondaries/count')
  @response(200, {
    description: 'HumanPartSecondary model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(HumanPartSecondary) where?: Where<HumanPartSecondary>,
  ): Promise<Count> {
    return this.humanPartSecondaryRepository.count(where);
  }

  @get('/human-part-secondaries')
  @response(200, {
    description: 'Array of HumanPartSecondary model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(HumanPartSecondary, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(HumanPartSecondary) filter?: Filter<HumanPartSecondary>,
  ): Promise<HumanPartSecondary[]> {
    return this.humanPartSecondaryRepository.find(filter);
  }

  @patch('/human-part-secondaries')
  @response(200, {
    description: 'HumanPartSecondary PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(HumanPartSecondary, {partial: true}),
        },
      },
    })
    humanPartSecondary: HumanPartSecondary,
    @param.where(HumanPartSecondary) where?: Where<HumanPartSecondary>,
  ): Promise<Count> {
    return this.humanPartSecondaryRepository.updateAll(humanPartSecondary, where);
  }

  @get('/human-part-secondaries/{id}')
  @response(200, {
    description: 'HumanPartSecondary model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(HumanPartSecondary, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(HumanPartSecondary, {exclude: 'where'}) filter?: FilterExcludingWhere<HumanPartSecondary>
  ): Promise<HumanPartSecondary> {
    return this.humanPartSecondaryRepository.findById(id, filter);
  }

  @patch('/human-part-secondaries/{id}')
  @response(204, {
    description: 'HumanPartSecondary PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(HumanPartSecondary, {partial: true}),
        },
      },
    })
    humanPartSecondary: HumanPartSecondary,
  ): Promise<void> {
    await this.humanPartSecondaryRepository.updateById(id, humanPartSecondary);
  }

  @put('/human-part-secondaries/{id}')
  @response(204, {
    description: 'HumanPartSecondary PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() humanPartSecondary: HumanPartSecondary,
  ): Promise<void> {
    await this.humanPartSecondaryRepository.replaceById(id, humanPartSecondary);
  }

  @del('/human-part-secondaries/{id}')
  @response(204, {
    description: 'HumanPartSecondary DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.humanPartSecondaryRepository.deleteById(id);
  }
}
