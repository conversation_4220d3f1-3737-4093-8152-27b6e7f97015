import { Entity, model, property } from '@loopback/repository';

@model()
export class Contractor<PERSON>ole extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;
  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'any',
  })
  permission?: any;

  @property({
    type: 'date',
  })
  created?: string;

  constructor(data?: Partial<ContractorRole>) {
    super(data);
  }
}

export interface ContractorRoleRelations {
  // describe navigational properties here
}

export type ContractorRoleWithRelations = ContractorRole & ContractorRoleRelations;
