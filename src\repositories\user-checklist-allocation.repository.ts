import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {UserChecklistAllocation, UserChecklistAllocationRelations} from '../models';

export class UserChecklistAllocationRepository extends DefaultCrudRepository<
  UserChecklistAllocation,
  typeof UserChecklistAllocation.prototype.id,
  UserChecklistAllocationRelations
> {
  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource,
  ) {
    super(UserChecklistAllocation, dataSource);
  }
}
