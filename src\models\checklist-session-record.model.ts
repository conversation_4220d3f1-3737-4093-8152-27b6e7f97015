import {Entity, model, property, belongsTo} from '@loopback/repository';
import {Checklist} from './checklist.model';
import {User} from './user.model';

@model({settings: {strict: false}})
export class ChecklistSessionRecord extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  sessionId?: string;

  @property({
    type: 'string',
  })
  stepData?: string;

  @belongsTo(() => Checklist)
  checklistId: string;

  @belongsTo(() => User)
  userId: string;
  // Define well-known properties here

  // Indexer property to allow additional data
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [prop: string]: any;

  constructor(data?: Partial<ChecklistSessionRecord>) {
    super(data);
  }
}

export interface ChecklistSessionRecordRelations {
  // describe navigational properties here
}

export type ChecklistSessionRecordWithRelations = ChecklistSessionRecord & ChecklistSessionRecordRelations;
