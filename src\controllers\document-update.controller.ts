import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {DocumentUpdate} from '../models';
import {DocumentUpdateRepository} from '../repositories';

export class DocumentUpdateController {
  constructor(
    @repository(DocumentUpdateRepository)
    public documentUpdateRepository : DocumentUpdateRepository,
  ) {}

  @post('/document-updates')
  @response(200, {
    description: 'DocumentUpdate model instance',
    content: {'application/json': {schema: getModelSchemaRef(DocumentUpdate)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(DocumentUpdate, {
            title: 'NewDocumentUpdate',
            exclude: ['id'],
          }),
        },
      },
    })
    documentUpdate: Omit<DocumentUpdate, 'id'>,
  ): Promise<DocumentUpdate> {
    return this.documentUpdateRepository.create(documentUpdate);
  }

  @get('/document-updates/count')
  @response(200, {
    description: 'DocumentUpdate model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(DocumentUpdate) where?: Where<DocumentUpdate>,
  ): Promise<Count> {
    return this.documentUpdateRepository.count(where);
  }

  @get('/document-updates')
  @response(200, {
    description: 'Array of DocumentUpdate model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(DocumentUpdate, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(DocumentUpdate) filter?: Filter<DocumentUpdate>,
  ): Promise<DocumentUpdate[]> {
    return this.documentUpdateRepository.find(filter);
  }

  @patch('/document-updates')
  @response(200, {
    description: 'DocumentUpdate PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(DocumentUpdate, {partial: true}),
        },
      },
    })
    documentUpdate: DocumentUpdate,
    @param.where(DocumentUpdate) where?: Where<DocumentUpdate>,
  ): Promise<Count> {
    return this.documentUpdateRepository.updateAll(documentUpdate, where);
  }

  @get('/document-updates/{id}')
  @response(200, {
    description: 'DocumentUpdate model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(DocumentUpdate, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(DocumentUpdate, {exclude: 'where'}) filter?: FilterExcludingWhere<DocumentUpdate>
  ): Promise<DocumentUpdate> {
    return this.documentUpdateRepository.findById(id, filter);
  }

  @patch('/document-updates/{id}')
  @response(204, {
    description: 'DocumentUpdate PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(DocumentUpdate, {partial: true}),
        },
      },
    })
    documentUpdate: DocumentUpdate,
  ): Promise<void> {
    await this.documentUpdateRepository.updateById(id, documentUpdate);
  }

  @put('/document-updates/{id}')
  @response(204, {
    description: 'DocumentUpdate PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() documentUpdate: DocumentUpdate,
  ): Promise<void> {
    await this.documentUpdateRepository.replaceById(id, documentUpdate);
  }

  @del('/document-updates/{id}')
  @response(204, {
    description: 'DocumentUpdate DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.documentUpdateRepository.deleteById(id);
  }
}
