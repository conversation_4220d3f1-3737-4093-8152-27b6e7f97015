import { Entity, model, property } from '@loopback/repository';

@model()
export class TierTwo extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;
  @property({
    type: 'string',
    required: true,
  })
  name: string;

  @property({
    type: 'string',
  })
  description?: string;

  @property({
    type: 'number',

  })
  test_days?: string;

  @property({
    type: 'date',

  })
  next_date?: string;

  @property({
    type: 'string',
  })
  certificate?: string;

  @property({
    type: 'string',
  })
  certificate_date?: string;

  @property({
    type: 'string',
  })
  new_date?: string;

  @property({
    type: 'string',
  })
  installed_date?: string;
  @property({
    type: 'string',
  })
  equip_id?: string;

  @property({
    type: 'string',
  })
  part_id?: string;

  @property({
    type: 'string',
  })
  critical_type?: string;

  @property({
    type: 'array',
    itemType: 'object',
  })
  test_types?: string;

  @property({
    type: 'array',
    itemType: 'object',
  })
  spare_types?: string;

  @property({
    type: 'array',
    itemType: 'any',
  })
  documents?: any[];

  @property({
    type: 'date',
  })
  created_at?: string;

  @property({
    type: 'date',
  })
  modified_at?: string;

  @property({
    type: 'number',
    default: 0,
  })
  order?: number;

  @property({
    type: 'string',
  })
  tierOneId?: string;

  constructor(data?: Partial<TierTwo>) {
    super(data);
  }
}

export interface TierTwoRelations {
  // describe navigational properties here
}

export type TierTwoWithRelations = TierTwo & TierTwoRelations;
