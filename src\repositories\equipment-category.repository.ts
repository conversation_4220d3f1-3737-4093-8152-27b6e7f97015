import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {EquipmentCategory, EquipmentCategoryRelations} from '../models';

export class EquipmentCategoryRepository extends DefaultCrudRepository<
  EquipmentCategory,
  typeof EquipmentCategory.prototype.id,
  EquipmentCategoryRelations
> {
  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource,
  ) {
    super(EquipmentCategory, dataSource);
  }
}
