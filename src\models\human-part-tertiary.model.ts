import {Entity, model, property} from '@loopback/repository';

@model()
export class HumanPartTertiary extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'string',
  })
  humanPartSecondaryId?: string;

  constructor(data?: Partial<HumanPartTertiary>) {
    super(data);
  }
}

export interface HumanPartTertiaryRelations {
  // describe navigational properties here
}

export type HumanPartTertiaryWithRelations = HumanPartTertiary & HumanPartTertiaryRelations;
