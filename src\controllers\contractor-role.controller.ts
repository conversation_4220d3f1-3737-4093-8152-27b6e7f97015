import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {ContractorRole} from '../models';
import {ContractorRoleRepository} from '../repositories';

export class ContractorRoleController {
  constructor(
    @repository(ContractorRoleRepository)
    public contractorRoleRepository : ContractorRoleRepository,
  ) {}

  @post('/contractor-roles')
  @response(200, {
    description: 'ContractorRole model instance',
    content: {'application/json': {schema: getModelSchemaRef(ContractorRole)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ContractorRole, {
            title: 'NewContractorRole',
            exclude: ['id'],
          }),
        },
      },
    })
    contractorRole: Omit<ContractorRole, 'id'>,
  ): Promise<ContractorRole> {
    return this.contractorRoleRepository.create(contractorRole);
  }

  @get('/contractor-roles/count')
  @response(200, {
    description: 'ContractorRole model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(ContractorRole) where?: Where<ContractorRole>,
  ): Promise<Count> {
    return this.contractorRoleRepository.count(where);
  }

  @get('/contractor-roles')
  @response(200, {
    description: 'Array of ContractorRole model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(ContractorRole, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(ContractorRole) filter?: Filter<ContractorRole>,
  ): Promise<ContractorRole[]> {
    return this.contractorRoleRepository.find(filter);
  }

  @patch('/contractor-roles')
  @response(200, {
    description: 'ContractorRole PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ContractorRole, {partial: true}),
        },
      },
    })
    contractorRole: ContractorRole,
    @param.where(ContractorRole) where?: Where<ContractorRole>,
  ): Promise<Count> {
    return this.contractorRoleRepository.updateAll(contractorRole, where);
  }

  @get('/contractor-roles/{id}')
  @response(200, {
    description: 'ContractorRole model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(ContractorRole, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(ContractorRole, {exclude: 'where'}) filter?: FilterExcludingWhere<ContractorRole>
  ): Promise<ContractorRole> {
    return this.contractorRoleRepository.findById(id, filter);
  }

  @patch('/contractor-roles/{id}')
  @response(204, {
    description: 'ContractorRole PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ContractorRole, {partial: true}),
        },
      },
    })
    contractorRole: ContractorRole,
  ): Promise<void> {
    await this.contractorRoleRepository.updateById(id, contractorRole);
  }

  @put('/contractor-roles/{id}')
  @response(204, {
    description: 'ContractorRole PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() contractorRole: ContractorRole,
  ): Promise<void> {
    await this.contractorRoleRepository.replaceById(id, contractorRole);
  }

  @del('/contractor-roles/{id}')
  @response(204, {
    description: 'ContractorRole DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.contractorRoleRepository.deleteById(id);
  }
}
