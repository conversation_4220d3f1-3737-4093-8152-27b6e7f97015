import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, BelongsToAccessor} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {PermitReport, PermitReportRelations, LocationOne, LocationTwo, LocationThree, LocationFour, LocationFive, LocationSix} from '../models';
import {LocationOneRepository} from './location-one.repository';
import {LocationTwoRepository} from './location-two.repository';
import {LocationThreeRepository} from './location-three.repository';
import {LocationFourRepository} from './location-four.repository';
import {LocationFiveRepository} from './location-five.repository';
import {LocationSixRepository} from './location-six.repository';

export class PermitReportRepository extends DefaultCrudRepository<
  PermitReport,
  typeof PermitReport.prototype.id,
  PermitReportRelations
> {

  public readonly locationOne: BelongsToAccessor<LocationOne, typeof PermitReport.prototype.id>;

  public readonly locationTwo: BelongsToAccessor<LocationTwo, typeof PermitReport.prototype.id>;

  public readonly locationThree: BelongsToAccessor<LocationThree, typeof PermitReport.prototype.id>;

  public readonly locationFour: BelongsToAccessor<LocationFour, typeof PermitReport.prototype.id>;

  public readonly locationFive: BelongsToAccessor<LocationFive, typeof PermitReport.prototype.id>;

  public readonly locationSix: BelongsToAccessor<LocationSix, typeof PermitReport.prototype.id>;

  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource, @repository.getter('LocationOneRepository') protected locationOneRepositoryGetter: Getter<LocationOneRepository>, @repository.getter('LocationTwoRepository') protected locationTwoRepositoryGetter: Getter<LocationTwoRepository>, @repository.getter('LocationThreeRepository') protected locationThreeRepositoryGetter: Getter<LocationThreeRepository>, @repository.getter('LocationFourRepository') protected locationFourRepositoryGetter: Getter<LocationFourRepository>, @repository.getter('LocationFiveRepository') protected locationFiveRepositoryGetter: Getter<LocationFiveRepository>, @repository.getter('LocationSixRepository') protected locationSixRepositoryGetter: Getter<LocationSixRepository>,
  ) {
    super(PermitReport, dataSource);
    this.locationSix = this.createBelongsToAccessorFor('locationSix', locationSixRepositoryGetter,);
    this.registerInclusionResolver('locationSix', this.locationSix.inclusionResolver);
    this.locationFive = this.createBelongsToAccessorFor('locationFive', locationFiveRepositoryGetter,);
    this.registerInclusionResolver('locationFive', this.locationFive.inclusionResolver);
    this.locationFour = this.createBelongsToAccessorFor('locationFour', locationFourRepositoryGetter,);
    this.registerInclusionResolver('locationFour', this.locationFour.inclusionResolver);
    this.locationThree = this.createBelongsToAccessorFor('locationThree', locationThreeRepositoryGetter,);
    this.registerInclusionResolver('locationThree', this.locationThree.inclusionResolver);
    this.locationTwo = this.createBelongsToAccessorFor('locationTwo', locationTwoRepositoryGetter,);
    this.registerInclusionResolver('locationTwo', this.locationTwo.inclusionResolver);
    this.locationOne = this.createBelongsToAccessorFor('locationOne', locationOneRepositoryGetter,);
    this.registerInclusionResolver('locationOne', this.locationOne.inclusionResolver);
  }
}
