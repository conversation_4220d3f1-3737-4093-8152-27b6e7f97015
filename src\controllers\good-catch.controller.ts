import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import { GoodCatch } from '../models';
import { GoodCatchRepository, UserLocationRoleRepository, UserRepository, ActionRepository } from '../repositories';
import { inject } from '@loopback/core';
import { SecurityBindings, securityId, UserProfile } from '@loopback/security';
import { authenticate } from '@loopback/authentication';
import moment from "moment";
import { SqsService } from '../services/sqs-service.service';

@authenticate('jwt')
export class GoodCatchController {
  constructor(
    @repository(GoodCatchRepository)
    public goodCatchRepository: GoodCatchRepository,
    @repository(UserLocationRoleRepository)
    public userLocationRoleRepository: UserLocationRoleRepository,
    @repository(UserRepository)
    public userRepository: UserRepository,
    @repository(ActionRepository)
    public actionRepository: ActionRepository,
    @inject('services.SqsService')
    public sqsService: SqsService,
  ) { }

  @post('/good-catches')
  @response(200, {
    description: 'GoodCatch model instance',
    content: { 'application/json': { schema: getModelSchemaRef(GoodCatch) } },
  })
  async create(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(GoodCatch, {
            title: 'NewGoodCatch',
            exclude: ['id'],
          }),
        },
      },
    })
    goodCatch: Omit<GoodCatch, 'id'>,
  ): Promise<GoodCatch> {
    const email = '<EMAIL>'
    const user = await this.userRepository.findOne({ where: { email: email } });
    const count = await this.goodCatchRepository.count();

    goodCatch.maskId = `GC-${moment().format('YYMMDD')}-${count.count + 1}`;
    goodCatch.status = 'Reported';
    goodCatch.created = moment(new Date()).toISOString();
    goodCatch.reporterId = user?.id ?? '';

    const obsData = await this.goodCatchRepository.create(goodCatch);

    const actionItem = {
      application: "GoodCatch",
      actionType: "admin_action",
      comments: '',
      description: obsData.whatDidYouObserve,
      dueDate: '',
      actionToBeTaken: 'Take Required Immediate Action',
      status: "open",
      createdDate: moment(new Date()).toISOString(),
      objectId: obsData.id,
      submittedById: user?.id,
      assignedToId: await this.getUserLocationRolesByRoleId('67602ca8549ac0ec8b0cd857')
    }
    await this.actionRepository.create(actionItem)

    const userIds = await this.getUserLocationRolesByRoleId('67602ca8549ac0ec8b0cd857');

    for (const userId of userIds) {
      const userData = await this.userRepository.findById(userId);
      const title = `New Good Catch Reported - ${obsData.maskId}`;
      const message = `A new good catch has been reported by ${user?.firstName} ${user?.lastName}. 
        <table border="1" cellpadding="10">
  <tr>
    <td>Good Catch Ref Number</td>
    <td>: ${obsData.maskId}</td>
  </tr>
  <tr>
    <td>Good Catch Captured Date</td>
    <td>: ${obsData.created}</td>
  </tr>
  <tr>
    <td>What Could Have Gone Wrong</td>
    <td>: ${obsData.whatCouldHaveGoneWrong}</td>
  </tr>
  <tr>
    <td>Submitted By</td>
    <td>: ${user?.firstName} ${user?.lastName}</td>
  </tr>
  <tr>
    <td>Submitted Date / Time</td>
    <td>: ${moment().format('DD/MM/YYYY')}</td>
  </tr>
</table>
      
      `;
      await this.sqsService.sendMessage(userData, title, message);
    }

    return obsData;
  }

  @patch('/good-catches-admin/{id}/{actionId}')
  @response(204, {
    description: 'GoodCatch PATCH success',
  })
  async updateActionById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @param.path.string('actionId') actionId: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(GoodCatch, { partial: true }),
        },
      },
    })
    goodCatch: GoodCatch,

  ): Promise<void> {

    const email = '<EMAIL>'
    const goodCatchData = await this.goodCatchRepository.findById(id)
    const user = await this.userRepository.findOne({ where: { email: email } });
    goodCatch.adminId = user?.id ?? '';
    if (goodCatch.actionOwnerId) {
      const actionItem = {
        application: "GoodCatch",
        actionType: "action_owner_action",
        comments: goodCatch.comments,
        description: goodCatchData.whatDidYouObserve,
        dueDate: goodCatch.dueDate,
        actionToBeTaken: goodCatch.actionToBeTaken,
        status: "open",
        createdDate: moment(new Date()).toISOString(),
        objectId: id,
        submittedById: user?.id,
        assignedToId: [goodCatch.actionOwnerId]
      }
      await this.actionRepository.create(actionItem)
      goodCatch.status = 'Acknowledged & Assigend to Action Owner'
    } else {
      goodCatch.closureDateTime = moment(new Date()).toISOString()
      goodCatch.status = 'Acknowledged & Closed'
    }
    await this.actionRepository.updateById(actionId, { status: 'completed' })
    await this.goodCatchRepository.updateById(id, goodCatch);
  }


  @patch('/good-catches-action-owner/{id}/{actionId}')
  @response(204, {
    description: 'GoodCatch PATCH success',
  })
  async updateActionOwnerById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @param.path.string('actionId') actionId: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(GoodCatch, { partial: true }),
        },
      },
    })
    goodCatch: GoodCatch,

  ): Promise<void> {
    const email = '<EMAIL>'

    const user = await this.userRepository.findOne({ where: { email: email } });
    goodCatch.closureDateTime = moment(new Date()).toISOString()
    goodCatch.status = 'To be Verified';
    const obsData = await this.goodCatchRepository.findById(id);
    const actionItem = {
      application: "GoodCatch",
      actionType: "admin_verify",
      comments: '',
      description: obsData.whatDidYouObserve,
      dueDate: '',
      actionToBeTaken: 'Verify Action Owner Action',
      status: "open",
      createdDate: moment(new Date()).toISOString(),
      objectId: obsData.id,
      submittedById: user?.id,
      assignedToId: await this.getUserLocationRolesByRoleId('67602ca8549ac0ec8b0cd857')
    }
    await this.actionRepository.create(actionItem)
    await this.actionRepository.updateById(actionId, { status: 'completed' })
    await this.goodCatchRepository.updateById(id, goodCatch);
  }

  @patch('/good-catches-admin-verification/{id}/{actionId}')
  @response(204, {
    description: 'GoodCatch PATCH success',
  })
  async updateAdminById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @param.path.string('actionId') actionId: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(GoodCatch, { partial: true }),
        },
      },
    })
    goodCatch: GoodCatch,

  ): Promise<void> {
    const email = '<EMAIL>'
    const goodCatchData = await this.goodCatchRepository.findById(id)
    const user = await this.userRepository.findOne({ where: { email: email } });
    goodCatch.adminId = user?.id ?? '';

    if (goodCatch.status === 'Verified') {
      goodCatch.status = 'Verified and Closed'
    }

    if (goodCatch.status === 'Returned') {
      if (goodCatch.actionOwnerId) {
        const actionItem = {
          application: "GoodCatch",
          actionType: "action_owner_action",
          comments: goodCatch.comments,
          description: goodCatchData.whatDidYouObserve,
          dueDate: goodCatchData.dueDate,
          actionToBeTaken: goodCatchData.actionToBeTaken,
          status: "open",
          createdDate: moment(new Date()).toISOString(),
          objectId: id,
          submittedById: user?.id,
          assignedToId: [goodCatch.actionOwnerId]
        }
        await this.actionRepository.create(actionItem)
        goodCatch.status = 'Action Re-Assigend to Action Owner'
      }
    }

    await this.actionRepository.updateById(actionId, { status: 'completed' })
    await this.goodCatchRepository.updateById(id, goodCatch);
  }


  @get('/good-catches/count')
  @response(200, {
    description: 'GoodCatch model count',
    content: { 'application/json': { schema: CountSchema } },
  })
  async count(
    @param.where(GoodCatch) where?: Where<GoodCatch>,
  ): Promise<Count> {
    return this.goodCatchRepository.count(where);
  }

  @get('/good-catches')
  @response(200, {
    description: 'Array of GoodCatch model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(GoodCatch, { includeRelations: true }),
        },
      },
    },
  })
  async find(
    @param.filter(GoodCatch) filter?: Filter<GoodCatch>,
  ): Promise<GoodCatch[]> {
    return this.goodCatchRepository.find(filter);
  }

  @patch('/good-catches')
  @response(200, {
    description: 'GoodCatch PATCH success count',
    content: { 'application/json': { schema: CountSchema } },
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(GoodCatch, { partial: true }),
        },
      },
    })
    goodCatch: GoodCatch,
    @param.where(GoodCatch) where?: Where<GoodCatch>,
  ): Promise<Count> {
    return this.goodCatchRepository.updateAll(goodCatch, where);
  }

  @get('/good-catches/{id}')
  @response(200, {
    description: 'GoodCatch model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(GoodCatch, { includeRelations: true }),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(GoodCatch, { exclude: 'where' }) filter?: FilterExcludingWhere<GoodCatch>
  ): Promise<GoodCatch> {
    return this.goodCatchRepository.findById(id, filter);
  }

  @patch('/good-catches/{id}')
  @response(204, {
    description: 'GoodCatch PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(GoodCatch, { partial: true }),
        },
      },
    })
    goodCatch: GoodCatch,
  ): Promise<void> {
    await this.goodCatchRepository.updateById(id, goodCatch);
  }

  @put('/good-catches/{id}')
  @response(204, {
    description: 'GoodCatch PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() goodCatch: GoodCatch,
  ): Promise<void> {
    await this.goodCatchRepository.replaceById(id, goodCatch);
  }

  @del('/good-catches/{id}')
  @response(204, {
    description: 'GoodCatch DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.goodCatchRepository.deleteById(id);
  }

  async getUserLocationRolesByRoleId(roleId: string) {
    const whereCondition = {
      roles: { inq: [[roleId]] },
      or: [
        {
          locationOneId: { inq: ['tier1-all'] },
          locationTwoId: '',
          locationThreeId: '',
          locationFourId: '',
        },
      ],
    };

    const userLocationRoles = await this.userLocationRoleRepository.find({
      where: whereCondition,
    });

    const userIds = userLocationRoles.map(userLocationRole => userLocationRole.userId);
    return Array.from(new Set(userIds));
  }
}
