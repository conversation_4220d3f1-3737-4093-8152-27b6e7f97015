import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {GeneralUser} from '../models';
import {GeneralUserRepository} from '../repositories';

export class GeneralUserController {
  constructor(
    @repository(GeneralUserRepository)
    public generalUserRepository : GeneralUserRepository,
  ) {}

  @post('/general-users')
  @response(200, {
    description: 'GeneralUser model instance',
    content: {'application/json': {schema: getModelSchemaRef(GeneralUser)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(GeneralUser, {
            title: 'NewGeneralUser',
            exclude: ['id'],
          }),
        },
      },
    })
    generalUser: Omit<GeneralUser, 'id'>,
  ): Promise<GeneralUser> {
    return this.generalUserRepository.create(generalUser);
  }

  @get('/general-users/count')
  @response(200, {
    description: 'GeneralUser model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(GeneralUser) where?: Where<GeneralUser>,
  ): Promise<Count> {
    return this.generalUserRepository.count(where);
  }

  @get('/general-users')
  @response(200, {
    description: 'Array of GeneralUser model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(GeneralUser, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(GeneralUser) filter?: Filter<GeneralUser>,
  ): Promise<GeneralUser[]> {
    return this.generalUserRepository.find(filter);
  }

  @patch('/general-users')
  @response(200, {
    description: 'GeneralUser PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(GeneralUser, {partial: true}),
        },
      },
    })
    generalUser: GeneralUser,
    @param.where(GeneralUser) where?: Where<GeneralUser>,
  ): Promise<Count> {
    return this.generalUserRepository.updateAll(generalUser, where);
  }

  @get('/general-users/{id}')
  @response(200, {
    description: 'GeneralUser model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(GeneralUser, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(GeneralUser, {exclude: 'where'}) filter?: FilterExcludingWhere<GeneralUser>
  ): Promise<GeneralUser> {
    return this.generalUserRepository.findById(id, filter);
  }

  @patch('/general-users/{id}')
  @response(204, {
    description: 'GeneralUser PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(GeneralUser, {partial: true}),
        },
      },
    })
    generalUser: GeneralUser,
  ): Promise<void> {
    await this.generalUserRepository.updateById(id, generalUser);
  }

  @put('/general-users/{id}')
  @response(204, {
    description: 'GeneralUser PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() generalUser: GeneralUser,
  ): Promise<void> {
    await this.generalUserRepository.replaceById(id, generalUser);
  }

  @del('/general-users/{id}')
  @response(204, {
    description: 'GeneralUser DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.generalUserRepository.deleteById(id);
  }
}
