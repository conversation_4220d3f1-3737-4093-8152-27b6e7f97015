import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import { TestCase } from '../models';
import { TestCaseRepository } from '../repositories';

export class TestCaseController {
  constructor(
    @repository(TestCaseRepository)
    public testCaseRepository: TestCaseRepository,
  ) { }
  @post('/new-test-cases')
  @response(200, {
    description: 'TestCase model instance',
    content: { 'application/json': { schema: getModelSchemaRef(TestCase) } },
  })
  async createNew(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(TestCase, {
            title: 'NewTestCase',
            exclude: ['id'],
          }),
        },
      },
    })
    testCase: Omit<TestCase, 'id'>,
  ): Promise<TestCase> {
    return this.testCaseRepository.create(testCase);
  }
  @post('/test-cases')
  @response(200, {
    description: 'TestCase model instance',
    content: { 'application/json': { schema: getModelSchemaRef(TestCase) } },
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(TestCase, {
            title: 'NewTestCase',

          }),
        },
      },
    })
    testCase: any,
  ): Promise<any> {
    const cases = testCase.newValues;
    cases.forEach(async (element: any) => {
      const caseItem = await this.testCaseRepository.findOne({ where: { maskId: element.maskId } })

      if (caseItem) {

        return await this.testCaseRepository.updateById(caseItem.id, element);
      } else {

        element.status = 0;
        return await this.testCaseRepository.create(element);
      }
    });
  }

  @del('/new-test-cases/{id}')
  @response(204, {
    description: 'TestCase DELETE success',
  })
  async newDeleteById(@param.path.string('id') id: string): Promise<void> {

    await this.testCaseRepository.deleteById(id);
  }


  @get('/test-cases/count')
  @response(200, {
    description: 'TestCase model count',
    content: { 'application/json': { schema: CountSchema } },
  })
  async count(
    @param.where(TestCase) where?: Where<TestCase>,
  ): Promise<Count> {
    return this.testCaseRepository.count(where);
  }

  @get('/test-cases')
  @response(200, {
    description: 'Array of TestCase model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(TestCase, { includeRelations: true }),
        },
      },
    },
  })
  async find(
    @param.filter(TestCase) filter?: Filter<TestCase>,
  ): Promise<TestCase[]> {
    return this.testCaseRepository.find(filter);
  }

  @patch('/test-cases')
  @response(200, {
    description: 'TestCase PATCH success count',
    content: { 'application/json': { schema: CountSchema } },
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(TestCase, { partial: true }),
        },
      },
    })
    testCase: TestCase,
    @param.where(TestCase) where?: Where<TestCase>,
  ): Promise<Count> {
    return this.testCaseRepository.updateAll(testCase, where);
  }

  @get('/test-cases/{id}')
  @response(200, {
    description: 'TestCase model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(TestCase, { includeRelations: true }),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(TestCase, { exclude: 'where' }) filter?: FilterExcludingWhere<TestCase>
  ): Promise<TestCase> {
    return this.testCaseRepository.findById(id, filter);
  }

  @patch('/test-cases/{id}')
  @response(204, {
    description: 'TestCase PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(TestCase, { partial: true }),
        },
      },
    })
    testCase: TestCase,
  ): Promise<void> {
    await this.testCaseRepository.updateById(id, testCase);
  }

  @put('/test-cases/{id}')
  @response(204, {
    description: 'TestCase PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() testCase: TestCase,
  ): Promise<void> {
    await this.testCaseRepository.replaceById(id, testCase);
  }

  @del('/test-cases/{id}')
  @response(204, {
    description: 'TestCase DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.testCaseRepository.deleteById(id);
  }
}
