import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
  HttpErrors,
} from '@loopback/rest';
import {DcOp} from '../models';
import { inject } from '@loopback/core';
import { SecurityBindings, securityId, UserProfile } from '@loopback/security';
import {DcOpRepository, ActionRepository, UserRepository} from '../repositories';
import { authenticate } from '@loopback/authentication';
import moment from 'moment';
import { SqsService } from '../services/sqs-service.service';

export class DcOpController {
  constructor(
    @repository(DcOpRepository)
    public dcOpRepository : DcOpRepository,
    @repository(UserRepository)
    public userRepository: UserRepository,
    @repository(ActionRepository)
    public actionRepository: ActionRepository,
    @inject('services.SqsService')
    public sqsService: SqsService,
  ) {}

  @post('/dc-ops')
  @response(200, {
    description: 'DcOp model instance',
    content: {'application/json': {schema: getModelSchemaRef(DcOp)}},
  })
  async create(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(DcOp, {
            title: 'NewDcOp',
            exclude: ['id'],
          }),
        },
      },
    })
    dcOp: Omit<DcOp, 'id'>,
  ): Promise<DcOp> {
    const email = '<EMAIL>'
    const user = await this.userRepository.findOne({where: {email: email}});

    dcOp.submittedId = user?.id;
    const count = await this.dcOpRepository.count();
    dcOp.maskId = `DCOP-${moment().format('YYMMDD')}-${count.count + 1}`;
    dcOp.status = 'Initiated';

    const dcOpReport = await this.dcOpRepository.create(dcOp);

    const actionItem = {
      application: "DCOP",
      actionType: "assessor",
      status: "open",
      createdDate: dcOpReport.created,
      objectId: dcOpReport.id,
      submittedById: user?.id,
      assignedToId: [dcOpReport.dcso]
    }

    if(dcOpReport.dcso) {
      const dcso = await this.userRepository.findById(dcOpReport.dcso);
      if(dcso) {this.sqsService.sendMessage(dcso, `Action to be taken on DC OP permit ${dcOpReport.maskId}`, `${dcOpReport.maskId} is submiited by ${user?.firstName} to assess.`) } else {throw new HttpErrors.NotFound(`User not found. Try again`);}
    }

    await this.actionRepository.create(actionItem)
    return dcOpReport;
  }

  @get('/dc-ops/count')
  @response(200, {
    description: 'DcOp model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(DcOp) where?: Where<DcOp>,
  ): Promise<Count> {
    return this.dcOpRepository.count(where);
  }

  @get('/dc-ops')
  @response(200, {
    description: 'Array of DcOp model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(DcOp, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.filter(DcOp) filter?: Filter<DcOp>,
  ): Promise<DcOp[]> {
    const email = '<EMAIL>'
    const user = await this.userRepository.findOne({where: {email: email}});

    return this.dcOpRepository.find({where: {submittedId: user?.id}});
  }

  @patch('/dc-ops')
  @response(200, {
    description: 'DcOp PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(DcOp, {partial: true}),
        },
      },
    })
    dcOp: DcOp,
    @param.where(DcOp) where?: Where<DcOp>,
  ): Promise<Count> {
    return this.dcOpRepository.updateAll(dcOp, where);
  }

  @get('/dc-ops/{id}')
  @response(200, {
    description: 'DcOp model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(DcOp, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(DcOp, {exclude: 'where'}) filter?: FilterExcludingWhere<DcOp>
  ): Promise<DcOp> {
    return this.dcOpRepository.findById(id, filter);
  }

  @patch('/dc-ops/{id}')
  @response(204, {
    description: 'DcOp PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(DcOp, {partial: true}),
        },
      },
    })
    dcOp: DcOp,
  ): Promise<void> {
    await this.dcOpRepository.updateById(id, dcOp);
  }

  @put('/dc-ops/{id}')
  @response(204, {
    description: 'DcOp PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() dcOp: DcOp,
  ): Promise<void> {
    await this.dcOpRepository.replaceById(id, dcOp);
  }

  @del('/dc-ops/{id}')
  @response(204, {
    description: 'DcOp DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.dcOpRepository.deleteById(id);
  }
}
