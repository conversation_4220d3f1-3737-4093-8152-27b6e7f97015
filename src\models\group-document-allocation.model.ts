import {Entity, model, property} from '@loopback/repository';

@model({settings: {strict: false}})
export class GroupDocumentAllocation extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  documentsId?: string;

  @property({
    type: 'string',
  })
  groupsId?: string;
  // Define well-known properties here

  // Indexer property to allow additional data
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [prop: string]: any;

  constructor(data?: Partial<GroupDocumentAllocation>) {
    super(data);
  }
}

export interface GroupDocumentAllocationRelations {
  // describe navigational properties here
}

export type GroupDocumentAllocationWithRelations = GroupDocumentAllocation & GroupDocumentAllocationRelations;
