import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {GeneralGroup, GeneralGroupRelations} from '../models';

export class GeneralGroupRepository extends DefaultCrudRepository<
  GeneralGroup,
  typeof GeneralGroup.prototype.id,
  GeneralGroupRelations
> {
  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource,
  ) {
    super(GeneralGroup, dataSource);
  }
}
