import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  UserLocationRole,
  LocationFour,
} from '../models';
import {UserLocationRoleRepository} from '../repositories';

export class UserLocationRoleLocationFourController {
  constructor(
    @repository(UserLocationRoleRepository)
    public userLocationRoleRepository: UserLocationRoleRepository,
  ) { }

  @get('/user-location-roles/{id}/location-four', {
    responses: {
      '200': {
        description: 'LocationFour belonging to UserLocationRole',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(LocationFour)},
          },
        },
      },
    },
  })
  async getLocationFour(
    @param.path.string('id') id: typeof UserLocationRole.prototype.id,
  ): Promise<LocationFour> {
    return this.userLocationRoleRepository.locationFour(id);
  }
}
