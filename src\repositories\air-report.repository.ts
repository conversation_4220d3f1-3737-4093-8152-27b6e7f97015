import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, BelongsToAccessor, HasManyRepositoryFactory} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {AirReport, AirReportRelations, WorkingGroup, LocationOne, LocationTwo, LocationThree, LocationFour, LocationFive, LocationSix, SurfaceType, SurfaceCondition, Lighting, WeatherCondition, User, GhsOne, GhsTwo, Driver, WorkActivity} from '../models';
import {WorkingGroupRepository} from './working-group.repository';
import {LocationOneRepository} from './location-one.repository';
import {LocationTwoRepository} from './location-two.repository';
import {LocationThreeRepository} from './location-three.repository';
import {LocationFourRepository} from './location-four.repository';
import {LocationFiveRepository} from './location-five.repository';
import {LocationSixRepository} from './location-six.repository';
import {SurfaceTypeRepository} from './surface-type.repository';
import {SurfaceConditionRepository} from './surface-condition.repository';
import {LightingRepository} from './lighting.repository';
import {WeatherConditionRepository} from './weather-condition.repository';
import {UserRepository} from './user.repository';
import {GhsOneRepository} from './ghs-one.repository';
import {GhsTwoRepository} from './ghs-two.repository';
import {DriverRepository} from './driver.repository';
import {WorkActivityRepository} from './work-activity.repository';

export class AirReportRepository extends DefaultCrudRepository<
  AirReport,
  typeof AirReport.prototype.id,
  AirReportRelations
> {

  public readonly workingGroup: BelongsToAccessor<WorkingGroup, typeof AirReport.prototype.id>;

  public readonly locationOne: BelongsToAccessor<LocationOne, typeof AirReport.prototype.id>;

  public readonly locationTwo: BelongsToAccessor<LocationTwo, typeof AirReport.prototype.id>;

  public readonly locationThree: BelongsToAccessor<LocationThree, typeof AirReport.prototype.id>;

  public readonly locationFour: BelongsToAccessor<LocationFour, typeof AirReport.prototype.id>;

  public readonly locationFive: BelongsToAccessor<LocationFive, typeof AirReport.prototype.id>;

  public readonly locationSix: BelongsToAccessor<LocationSix, typeof AirReport.prototype.id>;

  public readonly surfaceType: BelongsToAccessor<SurfaceType, typeof AirReport.prototype.id>;

  public readonly surfaceCondition: BelongsToAccessor<SurfaceCondition, typeof AirReport.prototype.id>;

  public readonly lighting: BelongsToAccessor<Lighting, typeof AirReport.prototype.id>;

  public readonly weatherCondition: BelongsToAccessor<WeatherCondition, typeof AirReport.prototype.id>;

  public readonly reporter: BelongsToAccessor<User, typeof AirReport.prototype.id>;

  public readonly reviewer: BelongsToAccessor<User, typeof AirReport.prototype.id>;

  public readonly workActivityDepartment: BelongsToAccessor<GhsOne, typeof AirReport.prototype.id>;

  public readonly workActivity: BelongsToAccessor<GhsTwo, typeof AirReport.prototype.id>;

  public readonly drivers: HasManyRepositoryFactory<Driver, typeof AirReport.prototype.id>;

  public readonly surveyor: BelongsToAccessor<User, typeof AirReport.prototype.id>;

  public readonly estimator: BelongsToAccessor<User, typeof AirReport.prototype.id>;

  public readonly trainee: BelongsToAccessor<User, typeof AirReport.prototype.id>;

  public readonly gmOps: BelongsToAccessor<User, typeof AirReport.prototype.id>;

  public readonly thirdParty: BelongsToAccessor<User, typeof AirReport.prototype.id>;

  public readonly security: BelongsToAccessor<User, typeof AirReport.prototype.id>;

  public readonly costReviewer: BelongsToAccessor<User, typeof AirReport.prototype.id>;

  public readonly financer: BelongsToAccessor<User, typeof AirReport.prototype.id>;

  public readonly dutyEngManager: BelongsToAccessor<User, typeof AirReport.prototype.id>;

  public readonly medicalOfficer: BelongsToAccessor<User, typeof AirReport.prototype.id>;

  public readonly engineer: BelongsToAccessor<User, typeof AirReport.prototype.id>;

  public readonly incidentTypeName: BelongsToAccessor<WorkActivity, typeof AirReport.prototype.id>;

  public readonly supervisor: BelongsToAccessor<User, typeof AirReport.prototype.id>;

  public readonly hod: BelongsToAccessor<User, typeof AirReport.prototype.id>;

  public readonly hr: BelongsToAccessor<User, typeof AirReport.prototype.id>;

  public readonly trainer: BelongsToAccessor<User, typeof AirReport.prototype.id>;

  public readonly hodCheck: BelongsToAccessor<User, typeof AirReport.prototype.id>;

  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource, @repository.getter('WorkingGroupRepository') protected workingGroupRepositoryGetter: Getter<WorkingGroupRepository>, @repository.getter('LocationOneRepository') protected locationOneRepositoryGetter: Getter<LocationOneRepository>, @repository.getter('LocationTwoRepository') protected locationTwoRepositoryGetter: Getter<LocationTwoRepository>, @repository.getter('LocationThreeRepository') protected locationThreeRepositoryGetter: Getter<LocationThreeRepository>, @repository.getter('LocationFourRepository') protected locationFourRepositoryGetter: Getter<LocationFourRepository>, @repository.getter('LocationFiveRepository') protected locationFiveRepositoryGetter: Getter<LocationFiveRepository>, @repository.getter('LocationSixRepository') protected locationSixRepositoryGetter: Getter<LocationSixRepository>, @repository.getter('SurfaceTypeRepository') protected surfaceTypeRepositoryGetter: Getter<SurfaceTypeRepository>, @repository.getter('SurfaceConditionRepository') protected surfaceConditionRepositoryGetter: Getter<SurfaceConditionRepository>, @repository.getter('LightingRepository') protected lightingRepositoryGetter: Getter<LightingRepository>, @repository.getter('WeatherConditionRepository') protected weatherConditionRepositoryGetter: Getter<WeatherConditionRepository>, @repository.getter('UserRepository') protected userRepositoryGetter: Getter<UserRepository>, @repository.getter('GhsOneRepository') protected ghsOneRepositoryGetter: Getter<GhsOneRepository>, @repository.getter('GhsTwoRepository') protected ghsTwoRepositoryGetter: Getter<GhsTwoRepository>, @repository.getter('DriverRepository') protected driverRepositoryGetter: Getter<DriverRepository>, @repository.getter('WorkActivityRepository') protected workActivityRepositoryGetter: Getter<WorkActivityRepository>,
  ) {
    super(AirReport, dataSource);
    this.hodCheck = this.createBelongsToAccessorFor('hodCheck', userRepositoryGetter,);
    this.registerInclusionResolver('hodCheck', this.hodCheck.inclusionResolver);
    this.trainer = this.createBelongsToAccessorFor('trainer', userRepositoryGetter,);
    this.registerInclusionResolver('trainer', this.trainer.inclusionResolver);
    this.hr = this.createBelongsToAccessorFor('hr', userRepositoryGetter,);
    this.registerInclusionResolver('hr', this.hr.inclusionResolver);
    this.hod = this.createBelongsToAccessorFor('hod', userRepositoryGetter,);
    this.registerInclusionResolver('hod', this.hod.inclusionResolver);
    this.supervisor = this.createBelongsToAccessorFor('supervisor', userRepositoryGetter,);
    this.registerInclusionResolver('supervisor', this.supervisor.inclusionResolver);
    this.incidentTypeName = this.createBelongsToAccessorFor('incidentTypeName', workActivityRepositoryGetter,);
    this.registerInclusionResolver('incidentTypeName', this.incidentTypeName.inclusionResolver);
    this.engineer = this.createBelongsToAccessorFor('engineer', userRepositoryGetter,);
    this.registerInclusionResolver('engineer', this.engineer.inclusionResolver);
    this.medicalOfficer = this.createBelongsToAccessorFor('medicalOfficer', userRepositoryGetter,);
    this.registerInclusionResolver('medicalOfficer', this.medicalOfficer.inclusionResolver);
    this.dutyEngManager = this.createBelongsToAccessorFor('dutyEngManager', userRepositoryGetter,);
    this.registerInclusionResolver('dutyEngManager', this.dutyEngManager.inclusionResolver);
    this.financer = this.createBelongsToAccessorFor('financer', userRepositoryGetter,);
    this.registerInclusionResolver('financer', this.financer.inclusionResolver);
    this.costReviewer = this.createBelongsToAccessorFor('costReviewer', userRepositoryGetter,);
    this.registerInclusionResolver('costReviewer', this.costReviewer.inclusionResolver);
    this.security = this.createBelongsToAccessorFor('security', userRepositoryGetter,);
    this.registerInclusionResolver('security', this.security.inclusionResolver);
    this.thirdParty = this.createBelongsToAccessorFor('thirdParty', userRepositoryGetter,);
    this.registerInclusionResolver('thirdParty', this.thirdParty.inclusionResolver);
    this.gmOps = this.createBelongsToAccessorFor('gmOps', userRepositoryGetter,);
    this.registerInclusionResolver('gmOps', this.gmOps.inclusionResolver);
    this.trainee = this.createBelongsToAccessorFor('trainee', userRepositoryGetter,);
    this.registerInclusionResolver('trainee', this.trainee.inclusionResolver);
    this.estimator = this.createBelongsToAccessorFor('estimator', userRepositoryGetter,);
    this.registerInclusionResolver('estimator', this.estimator.inclusionResolver);
    this.surveyor = this.createBelongsToAccessorFor('surveyor', userRepositoryGetter,);
    this.registerInclusionResolver('surveyor', this.surveyor.inclusionResolver);
    this.drivers = this.createHasManyRepositoryFactoryFor('drivers', driverRepositoryGetter,);
    this.registerInclusionResolver('drivers', this.drivers.inclusionResolver);
    this.workActivity = this.createBelongsToAccessorFor('workActivity', ghsTwoRepositoryGetter,);
    this.registerInclusionResolver('workActivity', this.workActivity.inclusionResolver);
    this.workActivityDepartment = this.createBelongsToAccessorFor('workActivityDepartment', ghsOneRepositoryGetter,);
    this.registerInclusionResolver('workActivityDepartment', this.workActivityDepartment.inclusionResolver);
    this.reviewer = this.createBelongsToAccessorFor('reviewer', userRepositoryGetter,);
    this.registerInclusionResolver('reviewer', this.reviewer.inclusionResolver);
    this.reporter = this.createBelongsToAccessorFor('reporter', userRepositoryGetter,);
    this.registerInclusionResolver('reporter', this.reporter.inclusionResolver);
    this.weatherCondition = this.createBelongsToAccessorFor('weatherCondition', weatherConditionRepositoryGetter,);
    this.registerInclusionResolver('weatherCondition', this.weatherCondition.inclusionResolver);
    this.lighting = this.createBelongsToAccessorFor('lighting', lightingRepositoryGetter,);
    this.registerInclusionResolver('lighting', this.lighting.inclusionResolver);
    this.surfaceCondition = this.createBelongsToAccessorFor('surfaceCondition', surfaceConditionRepositoryGetter,);
    this.registerInclusionResolver('surfaceCondition', this.surfaceCondition.inclusionResolver);
    this.surfaceType = this.createBelongsToAccessorFor('surfaceType', surfaceTypeRepositoryGetter,);
    this.registerInclusionResolver('surfaceType', this.surfaceType.inclusionResolver);
    this.locationSix = this.createBelongsToAccessorFor('locationSix', locationSixRepositoryGetter,);
    this.registerInclusionResolver('locationSix', this.locationSix.inclusionResolver);
    this.locationFive = this.createBelongsToAccessorFor('locationFive', locationFiveRepositoryGetter,);
    this.registerInclusionResolver('locationFive', this.locationFive.inclusionResolver);
    this.locationFour = this.createBelongsToAccessorFor('locationFour', locationFourRepositoryGetter,);
    this.registerInclusionResolver('locationFour', this.locationFour.inclusionResolver);
    this.locationThree = this.createBelongsToAccessorFor('locationThree', locationThreeRepositoryGetter,);
    this.registerInclusionResolver('locationThree', this.locationThree.inclusionResolver);
    this.locationTwo = this.createBelongsToAccessorFor('locationTwo', locationTwoRepositoryGetter,);
    this.registerInclusionResolver('locationTwo', this.locationTwo.inclusionResolver);
    this.locationOne = this.createBelongsToAccessorFor('locationOne', locationOneRepositoryGetter,);
    this.registerInclusionResolver('locationOne', this.locationOne.inclusionResolver);
    this.workingGroup = this.createBelongsToAccessorFor('workingGroup', workingGroupRepositoryGetter,);
    this.registerInclusionResolver('workingGroup', this.workingGroup.inclusionResolver);
  }
}
