import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
  HttpErrors,
} from '@loopback/rest';
import { LocationOne } from '../models';
import { LocationOneRepository, UserRepository, UserLocationRoleRepository } from '../repositories';

import { inject } from '@loopback/core';
import { SecurityBindings, securityId, UserProfile } from '@loopback/security';
import { authenticate } from '@loopback/authentication';

@authenticate('jwt')
export class LocationOneController {
  constructor(
    @repository(LocationOneRepository)
    public locationOneRepository: LocationOneRepository,
    @repository(UserRepository)
    public userRepository: UserRepository,
    @repository(UserLocationRoleRepository)
    public userLocationRoleRepository: UserLocationRoleRepository,
  ) { }

  @post('/location-ones')
  @response(200, {
    description: 'LocationOne model instance',
    content: { 'application/json': { schema: getModelSchemaRef(LocationOne) } },
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(LocationOne, {
            title: 'NewLocationOne',
            exclude: ['id'],
          }),
        },
      },
    })
    locationOne: Omit<LocationOne, 'id'>,
  ): Promise<LocationOne> {
    return this.locationOneRepository.create(locationOne);
  }

  @get('/location-ones/count')
  @response(200, {
    description: 'LocationOne model count',
    content: { 'application/json': { schema: CountSchema } },
  })
  async count(
    @param.where(LocationOne) where?: Where<LocationOne>,
  ): Promise<Count> {
    return this.locationOneRepository.count(where);
  }


  @get('/location-ones')
  @response(200, {
    description: 'Array of LocationOne model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'string' },
              title: { type: 'string' },
            },
          },
        },
      },
    },
  })
  async find(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.filter(LocationOne) filter?: Filter<LocationOne>,
  ): Promise<{ id: string; title: string }[]> {
    const email = '<EMAIL>'
    const user = await this.userRepository.findOne({ where: { email: email } });

    if (!user) {
      throw new HttpErrors.NotFound(`User not found with this email: ${email}`);
    }

    const userLocationRoles = await this.userLocationRoleRepository.find({ where: { userId: user.id } });

    const locationOneIds = userLocationRoles.map((userLocationRole) => userLocationRole.locationOneId);
    const uniqueLocationOneIds = Array.from(new Set(locationOneIds));

    // If 'all' is one of the locationOneIds, then we return all LocationOnes
    if (uniqueLocationOneIds.includes('tier1-all')) {
      const locationOnes = await this.locationOneRepository.find(filter);

      // Transform locationOnes into the desired format
      const transformedLocationOnes = locationOnes.map(locationOne => ({
        id: locationOne.id ?? '',
        title: locationOne.name ?? '',
        ...locationOne
      }));

      return transformedLocationOnes;
    } else {
      const locationOnes = await this.locationOneRepository.find({
        where: { id: { inq: uniqueLocationOneIds } },
      });

      // Transform locationOnes into the desired format
      const transformedLocationOnes = locationOnes.map(locationOne => ({
        id: locationOne.id ?? '',
        title: locationOne.name ?? '',
        ...locationOne
      }));

      return transformedLocationOnes;
    }
  }


  @patch('/location-ones')
  @response(200, {
    description: 'LocationOne PATCH success count',
    content: { 'application/json': { schema: CountSchema } },
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(LocationOne, { partial: true }),
        },
      },
    })
    locationOne: LocationOne,
    @param.where(LocationOne) where?: Where<LocationOne>,
  ): Promise<Count> {
    return this.locationOneRepository.updateAll(locationOne, where);
  }

  @get('/location-ones/{id}')
  @response(200, {
    description: 'LocationOne model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(LocationOne, { includeRelations: true }),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(LocationOne, { exclude: 'where' }) filter?: FilterExcludingWhere<LocationOne>
  ): Promise<LocationOne> {
    return this.locationOneRepository.findById(id, filter);
  }

  @patch('/location-ones/{id}')
  @response(204, {
    description: 'LocationOne PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(LocationOne, { partial: true }),
        },
      },
    })
    locationOne: LocationOne,
  ): Promise<void> {
    await this.locationOneRepository.updateById(id, locationOne);
  }

  @put('/location-ones/{id}')
  @response(204, {
    description: 'LocationOne PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() locationOne: LocationOne,
  ): Promise<void> {
    await this.locationOneRepository.replaceById(id, locationOne);
  }

  @del('/location-ones/{id}')
  @response(204, {
    description: 'LocationOne DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.locationOneRepository.deleteById(id);
  }
}
