import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {TitleConfig, TitleConfigRelations} from '../models';

export class TitleConfigRepository extends DefaultCrudRepository<
  TitleConfig,
  typeof TitleConfig.prototype.id,
  TitleConfigRelations
> {
  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource,
  ) {
    super(TitleConfig, dataSource);
  }
}
