import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasManyThroughRepositoryFactory} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {Checklist, ChecklistRelations, User, UserChecklistAllocation, Groups, GroupChecklistAllocation} from '../models';
import {UserChecklistAllocationRepository} from './user-checklist-allocation.repository';
import {UserRepository} from './user.repository';
import {GroupChecklistAllocationRepository} from './group-checklist-allocation.repository';
import {GroupsRepository} from './groups.repository';

export class ChecklistRepository extends DefaultCrudRepository<
  Checklist,
  typeof Checklist.prototype.id,
  ChecklistRelations
> {

  public readonly users: HasManyThroughRepositoryFactory<User, typeof User.prototype.id,
          UserChecklistAllocation,
          typeof Checklist.prototype.id
        >;

  public readonly groups: HasManyThroughRepositoryFactory<Groups, typeof Groups.prototype.id,
          GroupChecklistAllocation,
          typeof Checklist.prototype.id
        >;

  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource, @repository.getter('UserChecklistAllocationRepository') protected userChecklistAllocationRepositoryGetter: Getter<UserChecklistAllocationRepository>, @repository.getter('UserRepository') protected userRepositoryGetter: Getter<UserRepository>, @repository.getter('GroupChecklistAllocationRepository') protected groupChecklistAllocationRepositoryGetter: Getter<GroupChecklistAllocationRepository>, @repository.getter('GroupsRepository') protected groupsRepositoryGetter: Getter<GroupsRepository>,
  ) {
    super(Checklist, dataSource);
    this.groups = this.createHasManyThroughRepositoryFactoryFor('groups', groupsRepositoryGetter, groupChecklistAllocationRepositoryGetter,);
    this.registerInclusionResolver('groups', this.groups.inclusionResolver);
    this.users = this.createHasManyThroughRepositoryFactoryFor('users', userRepositoryGetter, userChecklistAllocationRepositoryGetter,);
    this.registerInclusionResolver('users', this.users.inclusionResolver);
  }
}
