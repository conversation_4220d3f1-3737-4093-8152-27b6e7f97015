import { Entity, model, property, belongsTo} from '@loopback/repository';
import {Checklist} from './checklist.model';

@model()
export class TestCase extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;


  @property({
    type: 'string',
  })
  date?: string;

  @property({
    type: 'string',
  })
  duration?: string;

  @property({
    type: 'string',
  })
  units?: string;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'string',
  })
  imca?: string;

  @property({
    type: 'string',
  })
  maskId?: string;

  @property({
    type: 'string',
  })
  tierId?: string;

  @property({
    type: 'string',
  })
  tierMode?: string;

  @property({
    type: 'string',
  })
  nextDate?: string;

  @property({
    type: 'string',
  })
  userId?: string;

  @property({
    type: 'number',
    default: 0

  })
  status?: number;

  @belongsTo(() => Checklist)
  checklistId: string;

  constructor(data?: Partial<TestCase>) {
    super(data);
  }
}

export interface TestCaseRelations {
  // describe navigational properties here
}

export type TestCaseWithRelations = TestCase & TestCaseRelations;
