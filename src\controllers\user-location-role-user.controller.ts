import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  UserLocationRole,
  User,
} from '../models';
import {UserLocationRoleRepository} from '../repositories';

export class UserLocationRoleUserController {
  constructor(
    @repository(UserLocationRoleRepository)
    public userLocationRoleRepository: UserLocationRoleRepository,
  ) { }

  @get('/user-location-roles/{id}/user', {
    responses: {
      '200': {
        description: 'User belonging to UserLocationRole',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(User)},
          },
        },
      },
    },
  })
  async getUser(
    @param.path.string('id') id: typeof UserLocationRole.prototype.id,
  ): Promise<User> {
    return this.userLocationRoleRepository.user(id);
  }
}
