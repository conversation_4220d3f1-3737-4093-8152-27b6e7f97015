import {Entity, model, property, hasMany} from '@loopback/repository';
import {HumanPartSecondary} from './human-part-secondary.model';

@model()
export class HumanPartPrimary extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  name?: string;

  @hasMany(() => HumanPartSecondary)
  humanPartSecondaries: HumanPartSecondary[];

  constructor(data?: Partial<HumanPartPrimary>) {
    super(data);
  }
}

export interface HumanPartPrimaryRelations {
  // describe navigational properties here
}

export type HumanPartPrimaryWithRelations = HumanPartPrimary & HumanPartPrimaryRelations;
