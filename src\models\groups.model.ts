import {Entity, model, property, hasMany} from '@loopback/repository';
import {User} from './user.model';
import {GroupUser} from './group-user.model';
import {Unit} from './unit.model';
import {GroupUnit} from './group-unit.model';
import {Checklist} from './checklist.model';
import {GroupChecklistAllocation} from './group-checklist-allocation.model';
import {Documents} from './documents.model';
import {GroupDocumentAllocation} from './group-document-allocation.model';

@model({settings: {strict: false}})
export class Groups extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'string',
  })
  createdAt?: string;

  @hasMany(() => User, {through: {model: () => GroupUser}})
  users: User[];

  @hasMany(() => Unit, {through: {model: () => GroupUnit}})
  units: Unit[];

  @hasMany(() => Checklist, {through: {model: () => GroupChecklistAllocation}})
  checklists: Checklist[];

  @hasMany(() => Documents, {through: {model: () => GroupDocumentAllocation}})
  documents: Documents[];
  // Define well-known properties here

  // Indexer property to allow additional data
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [prop: string]: any;

  constructor(data?: Partial<Groups>) {
    super(data);
  }
}

export interface GroupsRelations {
  // describe navigational properties here
}

export type GroupsWithRelations = Groups & GroupsRelations;
