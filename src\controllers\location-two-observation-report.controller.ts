import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  LocationTwo,
  ObservationReport,
} from '../models';
import {LocationTwoRepository} from '../repositories';

export class LocationTwoObservationReportController {
  constructor(
    @repository(LocationTwoRepository) protected locationTwoRepository: LocationTwoRepository,
  ) { }

  @get('/location-twos/{id}/observation-reports', {
    responses: {
      '200': {
        description: 'Array of LocationTwo has many ObservationReport',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(ObservationReport)},
          },
        },
      },
    },
  })
  async find(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<ObservationReport>,
  ): Promise<ObservationReport[]> {
    return this.locationTwoRepository.observationReports(id).find(filter);
  }

  @post('/location-twos/{id}/observation-reports', {
    responses: {
      '200': {
        description: 'LocationTwo model instance',
        content: {'application/json': {schema: getModelSchemaRef(ObservationReport)}},
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof LocationTwo.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ObservationReport, {
            title: 'NewObservationReportInLocationTwo',
            exclude: ['id'],
            optional: ['locationTwoId']
          }),
        },
      },
    }) observationReport: Omit<ObservationReport, 'id'>,
  ): Promise<ObservationReport> {
    return this.locationTwoRepository.observationReports(id).create(observationReport);
  }

  @patch('/location-twos/{id}/observation-reports', {
    responses: {
      '200': {
        description: 'LocationTwo.ObservationReport PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ObservationReport, {partial: true}),
        },
      },
    })
    observationReport: Partial<ObservationReport>,
    @param.query.object('where', getWhereSchemaFor(ObservationReport)) where?: Where<ObservationReport>,
  ): Promise<Count> {
    return this.locationTwoRepository.observationReports(id).patch(observationReport, where);
  }

  @del('/location-twos/{id}/observation-reports', {
    responses: {
      '200': {
        description: 'LocationTwo.ObservationReport DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(ObservationReport)) where?: Where<ObservationReport>,
  ): Promise<Count> {
    return this.locationTwoRepository.observationReports(id).delete(where);
  }
}
