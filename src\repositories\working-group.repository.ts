import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {WorkingGroup, WorkingGroupRelations} from '../models';

export class WorkingGroupRepository extends DefaultCrudRepository<
  WorkingGroup,
  typeof WorkingGroup.prototype.id,
  WorkingGroupRelations
> {
  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource,
  ) {
    super(WorkingGroup, dataSource);
  }
}
