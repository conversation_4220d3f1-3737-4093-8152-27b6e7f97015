import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  ReportIncident,
  WeatherCondition,
} from '../models';
import {ReportIncidentRepository} from '../repositories';

export class ReportIncidentWeatherConditionController {
  constructor(
    @repository(ReportIncidentRepository)
    public reportIncidentRepository: ReportIncidentRepository,
  ) { }

  @get('/report-incidents/{id}/weather-condition', {
    responses: {
      '200': {
        description: 'WeatherCondition belonging to ReportIncident',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(WeatherCondition)},
          },
        },
      },
    },
  })
  async getWeatherCondition(
    @param.path.string('id') id: typeof ReportIncident.prototype.id,
  ): Promise<WeatherCondition> {
    return this.reportIncidentRepository.weatherCondition(id);
  }
}
