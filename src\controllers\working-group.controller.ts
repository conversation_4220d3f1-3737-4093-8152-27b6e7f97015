import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {WorkingGroup} from '../models';
import {WorkingGroupRepository} from '../repositories';

export class WorkingGroupController {
  constructor(
    @repository(WorkingGroupRepository)
    public workingGroupRepository : WorkingGroupRepository,
  ) {}

  @post('/working-groups')
  @response(200, {
    description: 'WorkingGroup model instance',
    content: {'application/json': {schema: getModelSchemaRef(WorkingGroup)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(WorkingGroup, {
            title: 'NewWorkingGroup',
            exclude: ['id'],
          }),
        },
      },
    })
    workingGroup: Omit<WorkingGroup, 'id'>,
  ): Promise<WorkingGroup> {
    return this.workingGroupRepository.create(workingGroup);
  }

  @get('/working-groups/count')
  @response(200, {
    description: 'WorkingGroup model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(WorkingGroup) where?: Where<WorkingGroup>,
  ): Promise<Count> {
    return this.workingGroupRepository.count(where);
  }

  @get('/working-groups')
  @response(200, {
    description: 'Array of WorkingGroup model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(WorkingGroup, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(WorkingGroup) filter?: Filter<WorkingGroup>,
  ): Promise<WorkingGroup[]> {
    return this.workingGroupRepository.find(filter);
  }

  @patch('/working-groups')
  @response(200, {
    description: 'WorkingGroup PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(WorkingGroup, {partial: true}),
        },
      },
    })
    workingGroup: WorkingGroup,
    @param.where(WorkingGroup) where?: Where<WorkingGroup>,
  ): Promise<Count> {
    return this.workingGroupRepository.updateAll(workingGroup, where);
  }

  @get('/working-groups/{id}')
  @response(200, {
    description: 'WorkingGroup model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(WorkingGroup, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(WorkingGroup, {exclude: 'where'}) filter?: FilterExcludingWhere<WorkingGroup>
  ): Promise<WorkingGroup> {
    return this.workingGroupRepository.findById(id, filter);
  }

  @patch('/working-groups/{id}')
  @response(204, {
    description: 'WorkingGroup PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(WorkingGroup, {partial: true}),
        },
      },
    })
    workingGroup: WorkingGroup,
  ): Promise<void> {
    await this.workingGroupRepository.updateById(id, workingGroup);
  }

  @put('/working-groups/{id}')
  @response(204, {
    description: 'WorkingGroup PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() workingGroup: WorkingGroup,
  ): Promise<void> {
    await this.workingGroupRepository.replaceById(id, workingGroup);
  }

  @del('/working-groups/{id}')
  @response(204, {
    description: 'WorkingGroup DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.workingGroupRepository.deleteById(id);
  }
}
