import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  ObservationReport,
  LocationFive,
} from '../models';
import {ObservationReportRepository} from '../repositories';

export class ObservationReportLocationFiveController {
  constructor(
    @repository(ObservationReportRepository)
    public observationReportRepository: ObservationReportRepository,
  ) { }

  @get('/observation-reports/{id}/location-five', {
    responses: {
      '200': {
        description: 'LocationFive belonging to ObservationReport',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(LocationFive)},
          },
        },
      },
    },
  })
  async getLocationFive(
    @param.path.string('id') id: typeof ObservationReport.prototype.id,
  ): Promise<LocationFive> {
    return this.observationReportRepository.locationFive(id);
  }
}
