import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
  import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
User,
UserUnitAllocation,
Unit,
} from '../models';
import {UserRepository} from '../repositories';

export class UserUnitController {
  constructor(
    @repository(UserRepository) protected userRepository: UserRepository,
  ) { }

  @get('/users/{id}/units', {
    responses: {
      '200': {
        description: 'Array of User has many Unit through UserUnitAllocation',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(Unit)},
          },
        },
      },
    },
  })
  async find(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<Unit>,
  ): Promise<Unit[]> {
    return this.userRepository.units(id).find(filter);
  }

  @post('/users/{id}/units', {
    responses: {
      '200': {
        description: 'create a Unit model instance',
        content: {'application/json': {schema: getModelSchemaRef(Unit)}},
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof User.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Unit, {
            title: 'NewUnitInUser',
            exclude: ['id'],
          }),
        },
      },
    }) unit: Omit<Unit, 'id'>,
  ): Promise<Unit> {
    return this.userRepository.units(id).create(unit);
  }

  @patch('/users/{id}/units', {
    responses: {
      '200': {
        description: 'User.Unit PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Unit, {partial: true}),
        },
      },
    })
    unit: Partial<Unit>,
    @param.query.object('where', getWhereSchemaFor(Unit)) where?: Where<Unit>,
  ): Promise<Count> {
    return this.userRepository.units(id).patch(unit, where);
  }

  @del('/users/{id}/units', {
    responses: {
      '200': {
        description: 'User.Unit DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(Unit)) where?: Where<Unit>,
  ): Promise<Count> {
    return this.userRepository.units(id).delete(where);
  }
}
