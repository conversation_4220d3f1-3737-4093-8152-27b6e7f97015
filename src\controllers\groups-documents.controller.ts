import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
  import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
Groups,
GroupDocumentAllocation,
Documents,
} from '../models';
import {GroupsRepository} from '../repositories';

export class GroupsDocumentsController {
  constructor(
    @repository(GroupsRepository) protected groupsRepository: GroupsRepository,
  ) { }

  @get('/groups/{id}/documents', {
    responses: {
      '200': {
        description: 'Array of Groups has many Documents through GroupDocumentAllocation',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(Documents)},
          },
        },
      },
    },
  })
  async find(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<Documents>,
  ): Promise<Documents[]> {
    return this.groupsRepository.documents(id).find(filter);
  }

  @post('/groups/{id}/documents', {
    responses: {
      '200': {
        description: 'create a Documents model instance',
        content: {'application/json': {schema: getModelSchemaRef(Documents)}},
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof Groups.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Documents, {
            title: 'NewDocumentsInGroups',
            exclude: ['id'],
          }),
        },
      },
    }) documents: Omit<Documents, 'id'>,
  ): Promise<Documents> {
    return this.groupsRepository.documents(id).create(documents);
  }

  @patch('/groups/{id}/documents', {
    responses: {
      '200': {
        description: 'Groups.Documents PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Documents, {partial: true}),
        },
      },
    })
    documents: Partial<Documents>,
    @param.query.object('where', getWhereSchemaFor(Documents)) where?: Where<Documents>,
  ): Promise<Count> {
    return this.groupsRepository.documents(id).patch(documents, where);
  }

  @del('/groups/{id}/documents', {
    responses: {
      '200': {
        description: 'Groups.Documents DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(Documents)) where?: Where<Documents>,
  ): Promise<Count> {
    return this.groupsRepository.documents(id).delete(where);
  }
}
