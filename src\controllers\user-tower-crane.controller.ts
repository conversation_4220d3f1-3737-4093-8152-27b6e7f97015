import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  User,
  TowerCrane,
} from '../models';
import {UserRepository} from '../repositories';

export class UserTowerCraneController {
  constructor(
    @repository(UserRepository) protected userRepository: UserRepository,
  ) { }

  @get('/users/{id}/tower-cranes', {
    responses: {
      '200': {
        description: 'Array of User has many TowerCrane',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(TowerCrane)},
          },
        },
      },
    },
  })
  async find(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<TowerCrane>,
  ): Promise<TowerCrane[]> {
    return this.userRepository.towerCranes(id).find(filter);
  }

  @post('/users/{id}/tower-cranes', {
    responses: {
      '200': {
        description: 'User model instance',
        content: {'application/json': {schema: getModelSchemaRef(TowerCrane)}},
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof User.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(TowerCrane, {
            title: 'NewTowerCraneInUser',
            exclude: ['id'],
            optional: ['applicantId']
          }),
        },
      },
    }) towerCrane: Omit<TowerCrane, 'id'>,
  ): Promise<TowerCrane> {
    return this.userRepository.towerCranes(id).create(towerCrane);
  }

  @patch('/users/{id}/tower-cranes', {
    responses: {
      '200': {
        description: 'User.TowerCrane PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(TowerCrane, {partial: true}),
        },
      },
    })
    towerCrane: Partial<TowerCrane>,
    @param.query.object('where', getWhereSchemaFor(TowerCrane)) where?: Where<TowerCrane>,
  ): Promise<Count> {
    return this.userRepository.towerCranes(id).patch(towerCrane, where);
  }

  @del('/users/{id}/tower-cranes', {
    responses: {
      '200': {
        description: 'User.TowerCrane DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(TowerCrane)) where?: Where<TowerCrane>,
  ): Promise<Count> {
    return this.userRepository.towerCranes(id).delete(where);
  }
}
