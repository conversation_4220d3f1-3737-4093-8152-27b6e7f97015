import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  ChecklistSessionRecord,
  Checklist,
} from '../models';
import {ChecklistSessionRecordRepository} from '../repositories';

export class ChecklistSessionRecordChecklistController {
  constructor(
    @repository(ChecklistSessionRecordRepository)
    public checklistSessionRecordRepository: ChecklistSessionRecordRepository,
  ) { }

  @get('/checklist-session-records/{id}/checklist', {
    responses: {
      '200': {
        description: 'Checklist belonging to ChecklistSessionRecord',
        content: {
          'application/json': {
            schema: getModelSchemaRef(Checklist),
          },
        },
      },
    },
  })
  async getChecklist(
    @param.path.string('id') id: typeof ChecklistSessionRecord.prototype.id,
  ): Promise<Checklist> {
    return this.checklistSessionRecordRepository.checklist(id);
  }
}
