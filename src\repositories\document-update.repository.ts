import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {DocumentUpdate, DocumentUpdateRelations} from '../models';

export class DocumentUpdateRepository extends DefaultCrudRepository<
  DocumentUpdate,
  typeof DocumentUpdate.prototype.id,
  DocumentUpdateRelations
> {
  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource,
  ) {
    super(DocumentUpdate, dataSource);
  }
}
