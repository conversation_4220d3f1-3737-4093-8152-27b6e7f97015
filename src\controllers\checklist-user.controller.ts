import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
  import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
Checklist,
UserChecklistAllocation,
User,
} from '../models';
import {ChecklistRepository} from '../repositories';

export class ChecklistUserController {
  constructor(
    @repository(ChecklistRepository) protected checklistRepository: ChecklistRepository,
  ) { }

  @get('/checklists/{id}/users', {
    responses: {
      '200': {
        description: 'Array of Checklist has many User through UserChecklistAllocation',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(User)},
          },
        },
      },
    },
  })
  async find(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<User>,
  ): Promise<User[]> {
    return this.checklistRepository.users(id).find(filter);
  }

  @post('/checklists/{id}/users', {
    responses: {
      '200': {
        description: 'create a User model instance',
        content: {'application/json': {schema: getModelSchemaRef(User)}},
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof Checklist.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(User, {
            title: 'NewUserInChecklist',
            exclude: ['id'],
          }),
        },
      },
    }) user: Omit<User, 'id'>,
  ): Promise<User> {
    return this.checklistRepository.users(id).create(user);
  }

  @patch('/checklists/{id}/users', {
    responses: {
      '200': {
        description: 'Checklist.User PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(User, {partial: true}),
        },
      },
    })
    user: Partial<User>,
    @param.query.object('where', getWhereSchemaFor(User)) where?: Where<User>,
  ): Promise<Count> {
    return this.checklistRepository.users(id).patch(user, where);
  }

  @del('/checklists/{id}/users', {
    responses: {
      '200': {
        description: 'Checklist.User DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(User)) where?: Where<User>,
  ): Promise<Count> {
    return this.checklistRepository.users(id).delete(where);
  }
}
