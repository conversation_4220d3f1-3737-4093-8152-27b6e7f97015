import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  AirReport,
  User,
} from '../models';
import {AirReportRepository} from '../repositories';

export class AirReportUserController {
  constructor(
    @repository(AirReportRepository)
    public airReportRepository: AirReportRepository,
  ) { }

  @get('/air-reports/{id}/user', {
    responses: {
      '200': {
        description: 'User belonging to AirReport',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(User)},
          },
        },
      },
    },
  })
  async getUser(
    @param.path.string('id') id: typeof AirReport.prototype.id,
  ): Promise<User> {
    return this.airReportRepository.engineer(id);
  }
}
