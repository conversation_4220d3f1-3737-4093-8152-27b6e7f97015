import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
  import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
Unit,
GroupUnit,
Groups,
} from '../models';
import {UnitRepository} from '../repositories';

export class UnitGroupsController {
  constructor(
    @repository(UnitRepository) protected unitRepository: UnitRepository,
  ) { }

  @get('/units/{id}/groups', {
    responses: {
      '200': {
        description: 'Array of Unit has many Groups through GroupUnit',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(Groups)},
          },
        },
      },
    },
  })
  async find(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<Groups>,
  ): Promise<Groups[]> {
    return this.unitRepository.groups(id).find(filter);
  }

  @post('/units/{id}/groups', {
    responses: {
      '200': {
        description: 'create a Groups model instance',
        content: {'application/json': {schema: getModelSchemaRef(Groups)}},
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof Unit.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Groups, {
            title: 'NewGroupsInUnit',
            exclude: ['id'],
          }),
        },
      },
    }) groups: Omit<Groups, 'id'>,
  ): Promise<Groups> {
    return this.unitRepository.groups(id).create(groups);
  }

  @patch('/units/{id}/groups', {
    responses: {
      '200': {
        description: 'Unit.Groups PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Groups, {partial: true}),
        },
      },
    })
    groups: Partial<Groups>,
    @param.query.object('where', getWhereSchemaFor(Groups)) where?: Where<Groups>,
  ): Promise<Count> {
    return this.unitRepository.groups(id).patch(groups, where);
  }

  @del('/units/{id}/groups', {
    responses: {
      '200': {
        description: 'Unit.Groups DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(Groups)) where?: Where<Groups>,
  ): Promise<Count> {
    return this.unitRepository.groups(id).delete(where);
  }
}
