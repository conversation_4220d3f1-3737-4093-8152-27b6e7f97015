import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  RaToolboxTalk,
  RiskAssessment,
} from '../models';
import {RaToolboxTalkRepository} from '../repositories';

export class RaToolboxTalkRiskAssessmentController {
  constructor(
    @repository(RaToolboxTalkRepository)
    public raToolboxTalkRepository: RaToolboxTalkRepository,
  ) { }

  @get('/ra-toolbox-talks/{id}/risk-assessment', {
    responses: {
      '200': {
        description: 'RiskAssessment belonging to RaToolboxTalk',
        content: {
          'application/json': {
            schema: getModelSchemaRef(RiskAssessment),
          },
        },
      },
    },
  })
  async getRiskAssessment(
    @param.path.string('id') id: typeof RaToolboxTalk.prototype.id,
  ): Promise<RiskAssessment> {
    return this.raToolboxTalkRepository.riskAssessment(id);
  }
}
