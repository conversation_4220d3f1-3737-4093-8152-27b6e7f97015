import {Entity, model, property, hasMany} from '@loopback/repository';
import {User} from './user.model';
import {UserChecklistAllocation} from './user-checklist-allocation.model';
import {Groups} from './groups.model';
import {GroupChecklistAllocation} from './group-checklist-allocation.model';

@model({settings: {strict: false}})
export class Checklist extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'string',
  })
  description?: string;

  @property({
    type: 'string',
  })
  application?: string;

  @property({
    type: 'string',
  })
  value?: string;

  @property({
    type: 'string',
  })
  createdAt?: string;

  @hasMany(() => User, {through: {model: () => UserChecklistAllocation}})
  users: User[];

  @hasMany(() => Groups, {through: {model: () => GroupChecklistAllocation}})
  groups: Groups[];
  // Define well-known properties here

  // Indexer property to allow additional data
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [prop: string]: any;

  constructor(data?: Partial<Checklist>) {
    super(data);
  }
}

export interface ChecklistRelations {
  // describe navigational properties here
}

export type ChecklistWithRelations = Checklist & ChecklistRelations;
