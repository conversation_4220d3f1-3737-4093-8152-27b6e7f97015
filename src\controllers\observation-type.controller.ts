import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {ObservationType} from '../models';
import {ObservationTypeRepository} from '../repositories';

export class ObservationTypeController {
  constructor(
    @repository(ObservationTypeRepository)
    public observationTypeRepository : ObservationTypeRepository,
  ) {}

  @post('/observation-types')
  @response(200, {
    description: 'ObservationType model instance',
    content: {'application/json': {schema: getModelSchemaRef(ObservationType)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ObservationType, {
            title: 'NewObservationType',
            exclude: ['id'],
          }),
        },
      },
    })
    observationType: Omit<ObservationType, 'id'>,
  ): Promise<ObservationType> {
    return this.observationTypeRepository.create(observationType);
  }

  @get('/observation-types/count')
  @response(200, {
    description: 'ObservationType model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(ObservationType) where?: Where<ObservationType>,
  ): Promise<Count> {
    return this.observationTypeRepository.count(where);
  }

  @get('/observation-types')
  @response(200, {
    description: 'Array of ObservationType model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(ObservationType, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(ObservationType) filter?: Filter<ObservationType>,
  ): Promise<ObservationType[]> {
    return this.observationTypeRepository.find(filter);
  }

  @patch('/observation-types')
  @response(200, {
    description: 'ObservationType PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ObservationType, {partial: true}),
        },
      },
    })
    observationType: ObservationType,
    @param.where(ObservationType) where?: Where<ObservationType>,
  ): Promise<Count> {
    return this.observationTypeRepository.updateAll(observationType, where);
  }

  @get('/observation-types/{id}')
  @response(200, {
    description: 'ObservationType model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(ObservationType, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(ObservationType, {exclude: 'where'}) filter?: FilterExcludingWhere<ObservationType>
  ): Promise<ObservationType> {
    return this.observationTypeRepository.findById(id, filter);
  }

  @patch('/observation-types/{id}')
  @response(204, {
    description: 'ObservationType PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ObservationType, {partial: true}),
        },
      },
    })
    observationType: ObservationType,
  ): Promise<void> {
    await this.observationTypeRepository.updateById(id, observationType);
  }

  @put('/observation-types/{id}')
  @response(204, {
    description: 'ObservationType PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() observationType: ObservationType,
  ): Promise<void> {
    await this.observationTypeRepository.replaceById(id, observationType);
  }

  @del('/observation-types/{id}')
  @response(204, {
    description: 'ObservationType DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.observationTypeRepository.deleteById(id);
  }
}
