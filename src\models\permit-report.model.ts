import {Entity, model, property, belongsTo} from '@loopback/repository';
import {LocationOne} from './location-one.model';
import {LocationTwo} from './location-two.model';
import {LocationThree} from './location-three.model';
import {LocationFour} from './location-four.model';
import {LocationFive} from './location-five.model';
import {LocationSix} from './location-six.model';

@model()
export class PermitReport extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'array',
    itemType: 'string',
  })
  uploads?: string[];

  @property({
    type: 'array',
    itemType: 'object',
  })
  activities?: object[];

  @property({
    type: 'string',
  })
  workOrderNumber?: string;

  @property({
    type: 'object',
  })
  applicantOrg?: object;

  @property({
    type: 'array',
    itemType: 'object',
  })
  workers?: object[];

   @property({
    type: 'array',
    itemType: 'object',
  })
  customZones?: object[];

  @property({
    type: 'array',
    itemType: 'object',
  })

  additionalPermits? :object[];

  @property({
    type: 'array',
    itemType: 'object',
  })
  
  checklists?: object[];
  @property({
    type: 'string',
  })
  jobTitle?: string;

  @property({
    type: 'string',
  })
  permitStartDate?: string;

  @property({
    type: 'string',
  })
  permitEndDate?: string;

  @property({
    type: 'string',
  })
  contactNumber?: string;

  @property({
    type: 'string',
  })
  description?: string;

  @property({
    type: 'string',
  })
  created?: string;

  @property({
    type: 'string',
  })
  modified?: string;

  @property({
    type: 'string',
  })
  maskId?: string;

  @property({
    type: 'string',
  })
  permitType?: string;

  @property({
    type: 'string',
  })
  status?: string;

  @property({
    type: 'any',
  })
  high_risk?: any;

  @property({
    type: 'any',
  })
  dcop?: any;

  @property({
    type: 'any',
  })
  tower_crane?: any;

  @property({
    type: 'any',
  })
  isolation?: any;

  @property({
    type: 'string',
  })
  applicantId?: string;

  @property({
    type: 'string',
  })
  assessorId?: string;

  @property({
    type: 'string',
  })
  approverId?: string;

  @property({
    type: 'string',
  })
  dcsoApproverId?: string;
  @property({
    type: 'any',
  })
  closure?: any;

  @property({
    type: 'string',
  })
  stage?: string;

  @property({
    type: 'string',
  })
  level?: string;

  @belongsTo(() => LocationOne)
  locationOneId: string;

  @belongsTo(() => LocationTwo)
  locationTwoId: string;

  @belongsTo(() => LocationThree)
  locationThreeId: string;

  @belongsTo(() => LocationFour)
  locationFourId: string;

  @belongsTo(() => LocationFive)
  locationFiveId: string;

  @belongsTo(() => LocationSix)
  locationSixId: string;

  constructor(data?: Partial<PermitReport>) {
    super(data);
  }
}

export interface PermitReportRelations {
  // describe navigational properties here
}

export type PermitReportWithRelations = PermitReport & PermitReportRelations;
