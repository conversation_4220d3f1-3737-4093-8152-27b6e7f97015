import { Entity, model, property, belongsTo, hasMany } from '@loopback/repository';
import { WorkingGroup } from './working-group.model';
import { LocationOne } from './location-one.model';
import { LocationTwo } from './location-two.model';
import { LocationThree } from './location-three.model';
import { LocationFour } from './location-four.model';
import { LocationFive } from './location-five.model';
import { LocationSix } from './location-six.model';
import { SurfaceType } from './surface-type.model';
import { SurfaceCondition } from './surface-condition.model';
import { Lighting } from './lighting.model';
import { WeatherCondition } from './weather-condition.model';
import { User } from './user.model';
import { GhsOne } from './ghs-one.model';
import { GhsTwo } from './ghs-two.model';
import { Driver } from './driver.model';
import { WorkActivity } from './work-activity.model';

@model()
export class AirReport extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  airCategory?: string;

  @property({
    type: 'string',
  })
  humanRisk?: string;

  @property({
    type: 'number',
  })
  incidentCost?: number;

  @property({
    type: 'string',
  })
  incidentDate?: string;


  @property({
    type: 'array',
    itemType: 'object',
  })
  personInvolved?: object[];

  @property({
    type: 'array',
    itemType: 'object',
  })
  personnelImpacted?: object[];

  @property({
    type: 'array',
    itemType: 'object',
  })
  witnessInvolved?: object[];

  @property({
    type: 'number',
  })
  percentageRecovery?: number;

  @property({
    type: 'string',
  })
  isReported?: string;

  @property({
    type: 'boolean',
  })
  isRecoveryFromOtherParty?: boolean;

  @property({
    type: 'boolean',
  })
  isAcknowledgementRecevied?: boolean;

  @property({
    type: 'boolean',
  })
  isArchive?: boolean;

  @property({
    type: 'string',
  })
  archiveReason?: string;

  @property({
    type: 'string',
  })
  insuranceRefNo?: string;

  @property({
    type: 'string',
  })
  surveyStatus?: string;

  @property({
    type: 'number',
  })
  insuranceClaimAmount?: number;

  @property({
    type: 'string',
  })
  ahcDetails?: string;

  @property({
    type: 'string',
  })
  actionTakenAtAhc?: string;

  @property({
    type: 'string',
  })
  claimStatus?: string;

  @property({
    type: 'string',
  })
  claimPaymentRef?: string;

  @property({
    type: 'number',
  })
  claimPaymentAmount?: number;

  @property({
    type: 'any'
  })
  damagedEquipmentNumber?: any;

  @property({
    type: 'string',
  })
  vesselDetails?: string;

  @property({
    type: 'string',
  })
  description?: string;

  @property({
    type: 'string',
  })
  shortDescription?: string;

  @property({
    type: 'string',
  })
  moreDetails?: string;

  @property({
    type: 'string',
  })
  damageType?: string;

  @property({
    type: 'string',
  })
  bodyPartInjured?: string;

  @property({
    type: 'string',
  })
  commentsOnInjury?: string;

  @property({
    type: 'string',
  })
  protectiveGear?: string;

  @property({
    type: 'string',
  })
  protectiveGearType?: string;

  @property({
    type: 'string',
  })
  actionsTaken?: string;

  @property({
    type: 'string',
  })
  medicalNotificationTime?: string;

  @property({
    type: 'string',
  })
  returnStatus?: string;

  @property({
    type: 'string',
  })
  propertyNotificationTime?: string;

  @property({
    type: 'string',
  })
  custodianNotificationTime?: string;

  @property({
    type: 'string',
  })
  insuranceNotificationTime?: string;

  @property({
    type: 'string',
  })
  thirdPartyNotificationTime?: string;

  @property({
    type: 'string',
  })
  investigationNotificationTime?: string;

  @property({
    type: 'array',
    itemType: 'string',
  })
  evidence?: string[];

  @property({
    type: 'array',
    itemType: 'string',
  })
  documents?: string[];


  @property({
    type: 'array',
    itemType: 'string',
  })
  costEstimationFiles?: string[];

  @property({
    type: 'array',
    itemType: 'string',
  })
  hodFiles?: string[];

  @property({
    type: 'array',
    itemType: 'string',
  })
  dutyEngineerManagerFiles?: string[];

  @property({
    type: 'array',
    itemType: 'string',
  })
  claimsCoordinatorFiles?: string[];


  @property({
    type: 'array',
    itemType: 'string',
  })
  investigationDocuments?: string[];


  @property({
    type: 'array',
    itemType: 'string',
  })
  receiptDocuments?: string[];


  @property({
    type: 'array',
    itemType: 'string',
  })
  surveyorDocuments?: string[];


  @property({
    type: 'array',
    itemType: 'string',
  })
  truckDocuments?: string[];

  @property({
    type: 'any',

  })
  bannedTruckDetails?: any;

  @property({
    type: 'array',
    itemType: 'object',
  })
  allEvidence?: object[];

  @property({
    type: 'any',

  })
  truckDetails?: any;

  @property({
    type: 'string',
  })
  status?: string;

  @property({
    type: 'string',
  })
  stage?: string;

  @property({
    type: 'string',
  })
  investigationDate?: string;

  @property({
    type: 'string',
  })
  returnComments?: string;

  @property({
    type: 'string',
  })
  edited?: string;

  @property({
    type: 'string',
  })
  latestEdited?: string;

  @property({
    type: 'boolean',
    default: null
  })
  immediateActionRequired?: boolean;

  @property({
    type: 'boolean',
    default: null
  })
  cllInvolved?: boolean;

  @property({
    type: 'any',
  })
  investigation?: any;

  @property({
    type: 'any',
  })
  medicalReport?: any;

  @property({
    type: 'any',
  })
  gmOpsAction?: any;

  @property({
    type: 'any',
  })
  costEstimation?: any;


  @property({
    type: 'any',
  })
  costDutyEngineerEstimation?: any;

  @property({
    type: 'any',
  })
  costActual?: any;

  @property({
    type: 'string',
  })
  costReviewerComments?: string;

  @property({
    type: 'string',
  })
  chequeNumber?: string;

  @property({
    type: 'string',
  })
  recoveryAmount?: string;

  @property({
    type: 'string',
  })
  recoveryDate?: string;

  @property({
    type: 'string',
  })
  paymentMode?: string;

  @property({
    type: 'string',
  })
  hodReturnComments?: string;

  @property({
    type: 'any',
  })
  incidentRating?: any;



  @property({
    type: 'any',
  })
  thirdPartyForm?: any;

  @property({
    type: 'string',
  })
  costDutyEngineerComments?: string;

  @property({
    type: 'string',
  })
  overallCostComments?: string;

  @property({
    type: 'string',
  })
  insuranceNotify?: string;

  @property({
    type: 'string',
  })
  withdrawStatus?: string;

  @property({
    type: 'string',
  })
  withdrawRequested?: string;

  @property({
    type: 'any',
  })
  traineeAction?: any;

  @property({
    type: 'any',
  })
  engineerComments?: any;

  @property({
    type: 'any',
  })
  hodComments?: any;

  @property({
    type: 'any',
  })
  hrComments?: any;

  @property({
    type: 'any',
  })
  trainerComments?: any;

  @property({
    type: 'any',
  })
  supervisorComments?: any;

  @property({
    type: 'boolean',
    default: null
  })
  isMajorIncident?: boolean;

  @property({
    type: 'boolean',
    default: null
  })
  isMedicalPersonInvolved?: boolean;

  @property({
    type: 'boolean',
    default: null
  })
  inspectionRequired?: boolean;

  @property({
    type: 'boolean',
  })
  truckStatus?: boolean;

  @property({
    type: 'string',
  })
  maskId?: string;

  @property({
    type: 'string',
  })
  insuranceTeamSelected?: string;

  @property({
    type: 'string',
  })
  surveyorEmail?: string;
  @property({
    type: 'any',

  })
  investigationStep?: any;
  @property({
    type: 'string',
  })
  created?: string;

  @property({
    type: 'string',
  })
  modified?: string;

  @belongsTo(() => WorkingGroup)
  workingGroupId: string;

  @belongsTo(() => LocationOne)
  locationOneId: string;

  @belongsTo(() => LocationTwo)
  locationTwoId: string;

  @belongsTo(() => LocationThree)
  locationThreeId: string;

  @belongsTo(() => LocationFour)
  locationFourId: string;

  @belongsTo(() => LocationFive)
  locationFiveId: string;

  @belongsTo(() => LocationSix)
  locationSixId: string;

  @belongsTo(() => SurfaceType)
  surfaceTypeId: string;

  @belongsTo(() => SurfaceCondition)
  surfaceConditionId: string;

  @belongsTo(() => Lighting)
  lightingId: string;

  @belongsTo(() => WeatherCondition)
  weatherConditionId: string;

  @belongsTo(() => User)
  reporterId: string;

  @belongsTo(() => User)
  reviewerId: string;

  @belongsTo(() => GhsOne)
  workActivityDepartmentId: string;

  @belongsTo(() => GhsTwo)
  workActivityId: string;

  @hasMany(() => Driver)
  drivers: Driver[];

  @belongsTo(() => User)
  surveyorId: string;

  @belongsTo(() => User)
  estimatorId: string;

  @belongsTo(() => User)
  traineeId: string;

  @belongsTo(() => User)
  gmOpsId: string;

  @belongsTo(() => User)
  thirdPartyId: string;

  @belongsTo(() => User)
  securityId: string;

  @belongsTo(() => User)
  costReviewerId: string;

  @belongsTo(() => User)
  financerId: string;

  @belongsTo(() => User)
  dutyEngManagerId: string;

  @belongsTo(() => User)
  medicalOfficerId: string;

  @belongsTo(() => User)
  engineerId: string;

  @belongsTo(() => WorkActivity, { name: 'incidentTypeName' })
  incidentType: string;

  @belongsTo(() => User)
  supervisorId: string;

  @belongsTo(() => User)
  hodId: string;

  @belongsTo(() => User)
  hrId: string;

  @belongsTo(() => User)
  trainerId: string;

  @belongsTo(() => User)
  hodCheckId: string;

  locationThree: LocationThree;
  locationFour: LocationFour;
  weatherCondition: WeatherCondition;
  //recovery from belongs to dropdown

  constructor(data?: Partial<AirReport>) {
    super(data);
  }
}

export interface AirReportRelations {
  // describe navigational properties here
}

export type AirReportWithRelations = AirReport & AirReportRelations;
