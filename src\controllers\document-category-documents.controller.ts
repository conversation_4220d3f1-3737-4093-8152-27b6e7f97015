import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  DocumentCategory,
  Documents,
} from '../models';
import {DocumentCategoryRepository} from '../repositories';

export class DocumentCategoryDocumentsController {
  constructor(
    @repository(DocumentCategoryRepository) protected documentCategoryRepository: DocumentCategoryRepository,
  ) { }

  @get('/document-categories/{id}/documents', {
    responses: {
      '200': {
        description: 'Array of DocumentCategory has many Documents',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(Documents)},
          },
        },
      },
    },
  })
  async find(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<Documents>,
  ): Promise<Documents[]> {
    return this.documentCategoryRepository.documents(id).find(filter);
  }

  @post('/document-categories/{id}/documents', {
    responses: {
      '200': {
        description: 'DocumentCategory model instance',
        content: {'application/json': {schema: getModelSchemaRef(Documents)}},
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof DocumentCategory.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Documents, {
            title: 'NewDocumentsInDocumentCategory',
            exclude: ['id'],
            optional: ['documentCategoryId']
          }),
        },
      },
    }) documents: Omit<Documents, 'id'>,
  ): Promise<Documents> {
    return this.documentCategoryRepository.documents(id).create(documents);
  }

  @patch('/document-categories/{id}/documents', {
    responses: {
      '200': {
        description: 'DocumentCategory.Documents PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Documents, {partial: true}),
        },
      },
    })
    documents: Partial<Documents>,
    @param.query.object('where', getWhereSchemaFor(Documents)) where?: Where<Documents>,
  ): Promise<Count> {
    return this.documentCategoryRepository.documents(id).patch(documents, where);
  }

  @del('/document-categories/{id}/documents', {
    responses: {
      '200': {
        description: 'DocumentCategory.Documents DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(Documents)) where?: Where<Documents>,
  ): Promise<Count> {
    return this.documentCategoryRepository.documents(id).delete(where);
  }
}
