import {Entity, model, property} from '@loopback/repository';

@model()
export class Ppe extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'string',
  })
  description?: string;


  constructor(data?: Partial<Ppe>) {
    super(data);
  }
}

export interface PpeRelations {
  // describe navigational properties here
}

export type PpeWithRelations = Ppe & PpeRelations;
