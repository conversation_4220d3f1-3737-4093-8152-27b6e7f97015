import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  ReportIncident,
  IncidentRootCauseDescription,
} from '../models';
import {ReportIncidentRepository} from '../repositories';

export class ReportIncidentIncidentRootCauseDescriptionController {
  constructor(
    @repository(ReportIncidentRepository)
    public reportIncidentRepository: ReportIncidentRepository,
  ) { }

  @get('/report-incidents/{id}/incident-root-cause-description', {
    responses: {
      '200': {
        description: 'IncidentRootCauseDescription belonging to ReportIncident',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(IncidentRootCauseDescription)},
          },
        },
      },
    },
  })
  async getIncidentRootCauseDescription(
    @param.path.string('id') id: typeof ReportIncident.prototype.id,
  ): Promise<IncidentRootCauseDescription> {
    return this.reportIncidentRepository.incidentRootCauseDescription(id);
  }
}
