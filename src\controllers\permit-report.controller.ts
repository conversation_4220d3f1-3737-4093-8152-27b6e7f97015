import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
  HttpErrors,
} from '@loopback/rest';
import { PermitReport } from '../models';
import { PermitReportRepository, ActionRepository, UserRepository } from '../repositories';
import { SqsService } from '../services/sqs-service.service';

import { inject } from '@loopback/core';
import { SecurityBindings, securityId, UserProfile } from '@loopback/security';
import { authenticate } from '@loopback/authentication';
import moment from 'moment';

@authenticate('jwt')
export class PermitReportController {
  constructor(
    @repository(PermitReportRepository)
    public permitReportRepository: PermitReportRepository,
    @repository(ActionRepository)
    public actionRepository: ActionRepository,
    @repository(UserRepository)
    public userRepository: UserRepository,
    @inject('services.SqsService')
    public sqsService: SqsService,
  ) { }

  @post('/permit-reports')
  @response(200, {
    description: 'PermitReport model instance',
    content: { 'application/json': { schema: getModelSchemaRef(PermitReport) } },
  })
  async create(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(PermitReport, {
            title: 'NewPermitReport',
            exclude: ['id'],
          }),
        },
      },
    })
    permitReport: Omit<PermitReport, 'id'>,
  ): Promise<PermitReport> {

    const email = '<EMAIL>'
    const user = await this.userRepository.findOne({ where: { email: email } });


    if (permitReport.permitType === 'DC') {
      permitReport.applicantId = user?.id;
      const count = await this.permitReportRepository.count();
      permitReport.maskId = `${moment().format('YYMMDD')}-PTW-DC-${count.count + 1}`;
      permitReport.status = 'In Review with DCSO Representative'
      const pReport = await this.permitReportRepository.create(permitReport);
      const actionItem = {
        application: "PermitToWork",
        actionType: "dcso_approver",

        description: pReport.description,
        dueDate: pReport.permitStartDate,

        status: "open",
        createdDate: pReport.created,
        objectId: pReport.id,
        submittedById: user?.id,
        assignedToId: [pReport.dcsoApproverId]
      }
      //send notification
      if (pReport.dcsoApproverId && user?.id) {
        const dcsoApprover = await this.userRepository.findById(pReport.dcsoApproverId);
        if (dcsoApprover) { this.sqsService.sendMessage(dcsoApprover, `Action to be taken on Isolation. Permit ${pReport.maskId}`, `Permit ${pReport.maskId} is submitted by ${user?.firstName}. Due Date: ${pReport.permitStartDate}`) } else { throw new HttpErrors.NotFound(`User not found. Try again`); }
      }



      await this.actionRepository.create(actionItem)
      return pReport;
    }

    if (permitReport.permitType === 'TC') {
      permitReport.applicantId = user?.id;
      const count = await this.permitReportRepository.count();
      permitReport.maskId = `${moment().format('YYMMDD')}-PTW-TC-${count.count + 1}`;
      permitReport.status = 'In Review with Assessor'
      const pReport = await this.permitReportRepository.create(permitReport);
      const actionItem = {
        application: "PermitToWork",
        actionType: "assessor_one",

        description: pReport.description,
        dueDate: pReport.permitStartDate,

        status: "open",
        createdDate: pReport.created,
        objectId: pReport.id,
        submittedById: user?.id,
        assignedToId: [pReport.assessorId]
      }
      //send notification
      if (pReport.assessorId && user?.id) {
        const assessor = await this.userRepository.findById(pReport.assessorId);
        if (assessor) { this.sqsService.sendMessage(assessor, `Action to be taken on Isolation. Permit ${pReport.maskId}`, `Permit ${pReport.maskId} is submitted by ${user?.firstName}. Due Date: ${pReport.permitStartDate}`) } else { throw new HttpErrors.NotFound(`User not found. Try again`); }
      }



      await this.actionRepository.create(actionItem)
      return pReport;
    }
    //edit CA
    if (permitReport.permitType === 'A' || permitReport.permitType === 'H') {
      if (user?.id) {
        permitReport.applicantId = user.id;
      }
      const count = await this.permitReportRepository.count();
      permitReport.maskId = `PTW-${moment().format('YY/MM/DD')}-${String(count.count + 1).padStart(4, '0')}`;
      permitReport.status = 'Pending Assessment'
      const pReport = await this.permitReportRepository.create(permitReport);
      const actionItem = {
        application: "PermitToWork",
        actionType: "assessor",

        description: pReport.description,
        dueDate: pReport.permitStartDate,

        status: "open",
        createdDate: pReport.created,
        objectId: pReport.id,
        submittedById: user?.id,
        assignedToId: [pReport.assessorId]
      }



      await this.actionRepository.create(actionItem)
      return pReport;
    }

    throw new HttpErrors.Forbidden('Unauthorized Request')


  }

  @get('/permit-reports/count')
  @response(200, {
    description: 'PermitReport model count',
    content: { 'application/json': { schema: CountSchema } },
  })
  async count(
    @param.where(PermitReport) where?: Where<PermitReport>,
  ): Promise<Count> {
    return this.permitReportRepository.count(where);
  }

  @get('/permit-reports')
  @response(200, {
    description: 'Array of PermitReport model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(PermitReport, { includeRelations: true }),
        },
      },
    },
  })
  async find(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.filter(PermitReport) filter?: Filter<PermitReport>,
  ): Promise<PermitReport[]> {
    const email = '<EMAIL>'
    const user = await this.userRepository.findOne({ where: { email: email } });
    return this.permitReportRepository.find({
      
      include: [
        { relation: 'locationOne' },
        { relation: 'locationTwo' },
        { relation: 'locationThree' },
        { relation: 'locationFour' },
      ]
    });
  }



  @patch('/permit-reports')
  @response(200, {
    description: 'PermitReport PATCH success count',
    content: { 'application/json': { schema: CountSchema } },
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(PermitReport, { partial: true }),
        },
      },
    })
    permitReport: PermitReport,
    @param.where(PermitReport) where?: Where<PermitReport>,
  ): Promise<Count> {
    return this.permitReportRepository.updateAll(permitReport, where);
  }

  @get('/permit-reports/{id}')
  @response(200, {
    description: 'PermitReport model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(PermitReport, { includeRelations: true }),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(PermitReport, { exclude: 'where' }) filter?: FilterExcludingWhere<PermitReport>
  ): Promise<PermitReport> {
    return this.permitReportRepository.findById(id, filter);
  }

  @patch('/permit-reports/{id}/{action_id}')
  @response(204, {
    description: 'PermitReport PATCH success',
  })
  async updateById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @param.path.string('action_id') action_id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(PermitReport, { partial: true }),
        },
      },
    })
    permitReport: PermitReport,
  ): Promise<void> {
    const email = '<EMAIL>'
    const user = await this.userRepository.findOne({ where: { email: email } });
    const permitData = await this.permitReportRepository.findById(id);
    //update the status
    if (permitData.applicantId) {
      const applicant = await this.userRepository.findById(permitData.applicantId);
      if (applicant) {
        this.sqsService.sendMessage(applicant, `Permit ${permitData.maskId} has been Approved `, `Permit ${permitData.maskId} is Approved by ${user?.firstName}`)
      } else {
        throw new HttpErrors.NotFound(`User not found. Try again`);
      }
    }

    await this.actionRepository.updateById(action_id, { status: 'completed' })
    await this.permitReportRepository.updateById(id, permitReport);
  }

  @patch('/permit-reports-approver/{id}/{action_id}')
  @response(204, {
    description: 'PermitReport PATCH success',
  })
  async updateApproverById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @param.path.string('action_id') action_id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(PermitReport, { partial: true }),
        },
      },
    })
    permitReport: PermitReport,
  ): Promise<void> {
    const email = '<EMAIL>'
    const user = await this.userRepository.findOne({ where: { email: email } });
    const permitData = await this.permitReportRepository.findById(id);
    //update the status
    if (permitReport.approverId) {
      const approver = await this.userRepository.findById(permitReport.approverId);
      const actionItem = {
        application: "PermitToWork",
        actionType: "approver",

        description: permitData.description,
        dueDate: permitData.permitStartDate,

        status: "open",
        createdDate: permitData.created,
        objectId: permitData.id,
        submittedById: user?.id,
        assignedToId: [permitReport.approverId]
      }
      await this.actionRepository.create(actionItem)
      if (approver) {
        this.sqsService.sendMessage(approver, `Permit ${permitData.maskId} has been sent for Approval `, `Permit ${permitData.maskId} is sent for approval by ${user?.firstName}`)
      } else {
        throw new HttpErrors.NotFound(`User not found. Try again`);
      }
    }

    await this.actionRepository.updateById(action_id, { status: 'completed' })
    permitReport.status = 'Pending Approval'
    await this.permitReportRepository.updateById(id, permitReport);
  }
  @patch('/permit-reports-approver-stage/{id}/{action_id}')
  @response(204, {
    description: 'PermitReport PATCH success',
  })
  async updateApproverStageById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @param.path.string('action_id') action_id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(PermitReport, { partial: true }),
        },
      },
    })
    permitReport: PermitReport,
  ): Promise<void> {
    const email = '<EMAIL>'
    const user = await this.userRepository.findOne({ where: { email: email } });
    const permitData = await this.permitReportRepository.findById(id);

    // console.log(permitReport.stage);
    // console.log(permitData.level);

    //update the status
    if (permitReport.approverId) {

      if (permitData.level === 'high') {
        if (permitReport.stage !== '6') {

          const approver = await this.userRepository.findById(permitReport.approverId);
          const actionItem = {
            application: "PermitToWork",
            actionType: "approver",
            description: permitData.description,
            dueDate: permitData.permitStartDate,
            status: "open",
            createdDate: permitData.created,
            objectId: permitData.id,
            submittedById: user?.id,
            assignedToId: [permitReport.approverId]
          }
          await this.actionRepository.create(actionItem)

          if (approver) {
            this.sqsService.sendMessage(approver, `Permit ${permitData.maskId} has been sent for Approval `, `Permit ${permitData.maskId} is sent for approval by ${user?.firstName}`)
          } else {
            throw new HttpErrors.NotFound(`User not found. Try again`);
          }

          await this.actionRepository.updateById(action_id, { status: 'completed' })

          permitReport.status = `Pending Approve High ${permitReport.stage}`
          await this.permitReportRepository.updateById(id, permitReport);

        } else {
          await this.actionRepository.updateById(action_id, { status: 'completed' })
          permitReport.status = 'Active'

          await this.permitReportRepository.updateById(id, permitReport);
        }
      } else if (permitData.level === 'medium') {
        if (permitReport.stage !== '3') {

          const approver = await this.userRepository.findById(permitReport.approverId);
          const actionItem = {
            application: "PermitToWork",
            actionType: "approver",
            description: permitData.description,
            dueDate: permitData.permitStartDate,
            status: "open",
            createdDate: permitData.created,
            objectId: permitData.id,
            submittedById: user?.id,
            assignedToId: [permitReport.approverId]
          }
          await this.actionRepository.create(actionItem)
          if (approver) {
            this.sqsService.sendMessage(approver, `Permit ${permitData.maskId} has been sent for Approval `, `Permit ${permitData.maskId} is sent for approval by ${user?.firstName}`)
          } else {
            throw new HttpErrors.NotFound(`User not found. Try again`);
          }

          await this.actionRepository.updateById(action_id, { status: 'completed' })

          permitReport.status = `Pending Approve Medium ${permitReport.stage}`
          await this.permitReportRepository.updateById(id, permitReport);


        } else {
          await this.actionRepository.updateById(action_id, { status: 'completed' })
          permitReport.status = 'Active'

          await this.permitReportRepository.updateById(id, permitReport);
        }
      } else if (permitData.level === 'low') {
        if (permitReport.stage !== '2') {

          const approver = await this.userRepository.findById(permitReport.approverId);
          const actionItem = {
            application: "PermitToWork",
            actionType: "approver",
            description: permitData.description,
            dueDate: permitData.permitStartDate,
            status: "open",
            createdDate: permitData.created,
            objectId: permitData.id,
            submittedById: user?.id,
            assignedToId: [permitReport.approverId]
          }
          await this.actionRepository.create(actionItem)
          if (approver) {
            this.sqsService.sendMessage(approver, `Permit ${permitData.maskId} has been sent for Approval `, `Permit ${permitData.maskId} is sent for approval by ${user?.firstName}`)
          } else {
            throw new HttpErrors.NotFound(`User not found. Try again`);
          }

          await this.actionRepository.updateById(action_id, { status: 'completed' })

          permitReport.status = `Pending Approve low ${permitReport.stage}`
          await this.permitReportRepository.updateById(id, permitReport);


        } else {
          await this.actionRepository.updateById(action_id, { status: 'completed' })

          permitReport.status = 'Active'
          await this.permitReportRepository.updateById(id, permitReport);
        }



      }

    }


  }

  @patch('/permit-reports-assessor/{id}/{action_id}')
  @response(204, {
    description: 'PermitReport PATCH success',
  })
  async updateAssessorById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @param.path.string('action_id') action_id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(PermitReport, { partial: true }),
        },
      },
    })
    permitReport: PermitReport,
  ): Promise<void> {
    const email = '<EMAIL>'
    const user = await this.userRepository.findOne({ where: { email: email } });
    const permitData = await this.permitReportRepository.findById(id);
    //update the status
    if (permitData.assessorId) {
      const assessor = await this.userRepository.findById(permitData.assessorId);
      const actionItem = {
        application: "PermitToWork",
        actionType: "assessor",

        description: permitData.description,
        dueDate: permitData.permitStartDate,

        status: "open",
        createdDate: permitData.created,
        objectId: permitData.id,
        submittedById: user?.id,
        assignedToId: [permitData.assessorId]
      }
      await this.actionRepository.create(actionItem)
      if (assessor) {
        this.sqsService.sendMessage(assessor, `Permit ${permitData.maskId} has been sent for review `, `Permit ${permitData.maskId} is sent for review by ${user?.firstName}`)
      } else {
        throw new HttpErrors.NotFound(`User not found. Try again`);
      }
    }


    await this.actionRepository.updateById(action_id, { status: 'completed' })
    await this.permitReportRepository.updateById(id, permitReport);
  }

  @patch('/permit-reports-approve/{id}/{action_id}')
  @response(204, {
    description: 'PermitReport PATCH success',
  })
  async updateApproveById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @param.path.string('action_id') action_id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(PermitReport, { partial: true }),
        },
      },
    })
    permitReport: PermitReport,
  ): Promise<void> {
    const email = '<EMAIL>'
    const user = await this.userRepository.findOne({ where: { email: email } });
    const permitData = await this.permitReportRepository.findById(id);
    //update the status
    permitReport.status = "Active";

    await this.actionRepository.updateById(action_id, { status: 'closed' })
    await this.permitReportRepository.updateById(id, permitReport);
  }

  @patch('/permit-reports-reject/{id}/{action_id}')
  @response(204, {
    description: 'PermitReport PATCH success',
  })
  async rejectById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @param.path.string('action_id') action_id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(PermitReport, { partial: true }),
        },
      },
    })
    permitReport: PermitReport,
  ): Promise<void> {
    const email = '<EMAIL>'
    const user = await this.userRepository.findOne({ where: { email: email } });
    const permitData = await this.permitReportRepository.findById(id);
    //update the status
    if (permitData.applicantId) {
      const applicant = await this.userRepository.findById(permitData.applicantId);
      if (applicant) {
        this.sqsService.sendMessage(applicant, `Permit ${permitData.maskId} has been Rejected `, `Permit ${permitData.maskId} is Rejected by ${user?.firstName}`)
      } else {
        throw new HttpErrors.NotFound(`User not found. Try again`);
      }
    }
    permitReport.status = 'Rejected'
    await this.actionRepository.updateById(action_id, { status: 'rejected' })
    await this.permitReportRepository.updateById(id, permitReport);
  }

  @patch('/permit-reports-withdraw/{id}/{action_id}')
  @response(204, {
    description: 'PermitReport PATCH success',
  })
  async withdrawById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @param.path.string('action_id') action_id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(PermitReport, { partial: true }),
        },
      },
    })
    permitReport: PermitReport,
  ): Promise<void> {
    const email = '<EMAIL>'
    const user = await this.userRepository.findOne({ where: { email: email } });
    const permitData = await this.permitReportRepository.findById(id);
    //update the status
    if (permitData.applicantId) {
      const applicant = await this.userRepository.findById(permitData.applicantId);
      if (applicant) {
        this.sqsService.sendMessage(applicant, `Permit ${permitData.maskId} has been Withdraw `, `Permit ${permitData.maskId} is withdrawn by ${user?.firstName}`)
      } else {
        throw new HttpErrors.NotFound(`User not found. Try again`);
      }
    }

    await this.actionRepository.updateById(action_id, { status: 'closed' })
    permitReport.status = 'Withdrawn';
    await this.permitReportRepository.updateById(id, permitReport);
  }

  @patch('/permit-reports-withdraw/{id}')
  @response(204, {
    description: 'PermitReport PATCH success',
  })
  async withdrawWholeById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,

    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(PermitReport, { partial: true }),
        },
      },
    })
    permitReport: PermitReport,
  ): Promise<void> {
    const email = '<EMAIL>'
    const user = await this.userRepository.findOne({ where: { email: email } });
    const permitData = await this.permitReportRepository.findById(id);
    //update the status
    if (permitData.applicantId) {
      const applicant = await this.userRepository.findById(permitData.applicantId);
      if (applicant) {
        this.sqsService.sendMessage(applicant, `Permit ${permitData.maskId} has been Withdraw `, `Permit ${permitData.maskId} is withdrawn by ${user?.firstName}`)
      } else {
        throw new HttpErrors.NotFound(`User not found. Try again`);
      }
    }


    await this.actionRepository.updateAll({ status: "closed" }, { objectId: id });

    permitReport.status = 'Withdrawn';
    await this.permitReportRepository.updateById(id, permitReport);
  }


  @patch('/permit-reports-expired/{id}')
  @response(204, {
    description: 'PermitReport PATCH success',
  })
  async expiredWholeById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,

    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(PermitReport, { partial: true }),
        },
      },
    })
    permitReport: PermitReport,
  ): Promise<void> {
    // const email = '<EMAIL>'
    // const user = await this.userRepository.findOne({ where: { email: email } });
    // const permitData = await this.permitReportRepository.findById(id);
    //update the status
    // if (permitData.applicantId) {
    //   const applicant = await this.userRepository.findById(permitData.applicantId);
    //   if (applicant) {
    //     this.sqsService.sendMessage(applicant, `Permit ${permitData.maskId} has been Withdraw `, `Permit ${permitData.maskId} is withdrawn by ${user?.firstName}`)
    //   } else {
    //     throw new HttpErrors.NotFound(`User not found. Try again`);
    //   }
    // }


    // await this.actionRepository.updateAll({ status: "closed" }, { objectId: id } );

    permitReport.status = 'Active: Timed Out';
    await this.permitReportRepository.updateById(id, permitReport);
  }

  @patch('/permit-reports-suspend/{id}')
  @response(204, {
    description: 'PermitReport PATCH success',
  })
  async suspendWholeById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,

    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(PermitReport, { partial: true }),
        },
      },
    })
    permitReport: PermitReport,
  ): Promise<void> {
    // const email = '<EMAIL>'
    // const user = await this.userRepository.findOne({ where: { email: email } });
    // const permitData = await this.permitReportRepository.findById(id);
    //update the status
    // if (permitData.applicantId) {
    //   const applicant = await this.userRepository.findById(permitData.applicantId);
    //   if (applicant) {
    //     this.sqsService.sendMessage(applicant, `Permit ${permitData.maskId} has been Withdraw `, `Permit ${permitData.maskId} is withdrawn by ${user?.firstName}`)
    //   } else {
    //     throw new HttpErrors.NotFound(`User not found. Try again`);
    //   }
    // }


    // await this.actionRepository.updateAll({ status: "closed" }, { objectId: id } );

    permitReport.status = 'Revoked';
    await this.permitReportRepository.updateById(id, permitReport);
  }


  @patch('/permit-reports-close-out/{id}')
  @response(204, {
    description: 'PermitReport PATCH success',
  })
  async closeOutWholeById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,

    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(PermitReport, { partial: true }),
        },
      },
    })
    permitReport: PermitReport,
  ): Promise<void> {
    const email = '<EMAIL>'
    const user = await this.userRepository.findOne({ where: { email: email } });
    const permitData = await this.permitReportRepository.findById(id);
    //update the status
    // if (permitData.applicantId) {
    //   const applicant = await this.userRepository.findById(permitData.applicantId);
    //   if (applicant) {
    //     this.sqsService.sendMessage(applicant, `Permit ${permitData.maskId} has been Withdraw `, `Permit ${permitData.maskId} is withdrawn by ${user?.firstName}`)
    //   } else {
    //     throw new HttpErrors.NotFound(`User not found. Try again`);
    //   }
    // }


    // await this.actionRepository.updateAll({ status: "closed" }, { objectId: id } );
    switch (permitData.permitType) {
      case 'DC':
        permitReport.status = 'Pending Normalization';
        const actionItem = {
          application: "PermitToWork",
          actionType: "normalization",

          description: permitData.description,
          dueDate: permitData.permitStartDate,

          status: "pending_normalization",
          createdDate: permitData.created,
          objectId: permitData.id,
          submittedById: user?.id,
          assignedToId: [permitData.dcsoApproverId]
        }
        await this.actionRepository.create(actionItem)
        if (permitData.dcsoApproverId) {
          const dcsoApprover = await this.userRepository.findById(permitData.dcsoApproverId);
          if (dcsoApprover) {
            this.sqsService.sendMessage(dcsoApprover, `Permit ${permitData.maskId} has been sent for Normalization `, `Permit ${permitData.maskId} is completed and closed by ${user?.firstName}. Please normalize the isolation and close the permit`)
          } else {
            throw new HttpErrors.NotFound(`User not found. Try again`);
          }
        } else {
          throw new HttpErrors.NotFound('DCSO Approver Not Found')
        }


        break;
      default: permitReport.status = 'Closed'; break;
    }
    permitReport.status = 'Closed';
    await this.permitReportRepository.updateById(id, permitReport);
  }

  @patch('/permit-reports-normalization/{id}')
  @response(204, {
    description: 'PermitReport PATCH success',
  })
  async normalizationWholeById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,

    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(PermitReport, { partial: true }),
        },
      },
    })
    permitReport: PermitReport,
  ): Promise<void> {
    // const email = '<EMAIL>'
    // const user = await this.userRepository.findOne({ where: { email: email } });
    const permitData = await this.permitReportRepository.findById(id);
    //update the status
    // if (permitData.applicantId) {
    //   const applicant = await this.userRepository.findById(permitData.applicantId);
    //   if (applicant) {
    //     this.sqsService.sendMessage(applicant, `Permit ${permitData.maskId} has been Withdraw `, `Permit ${permitData.maskId} is withdrawn by ${user?.firstName}`)
    //   } else {
    //     throw new HttpErrors.NotFound(`User not found. Try again`);
    //   }
    // }


    // await this.actionRepository.updateAll({ status: "closed" }, { objectId: id } );
    switch (permitData.permitType) {
      case 'DC': permitReport.status = 'Normalized and Closed'; await this.actionRepository.updateAll({ status: "closed" }, { objectId: id }); break;
      default: permitReport.status = 'Closed'; break;
    }

    await this.permitReportRepository.updateById(id, permitReport);
  }

  @patch('/permit-reports-timeout/{id}/{action_id}')
  @response(204, {
    description: 'PermitReport PATCH success',
  })
  async timeoutById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @param.path.string('action_id') action_id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(PermitReport, { partial: true }),
        },
      },
    })
    permitReport: PermitReport,
  ): Promise<void> {
    const email = '<EMAIL>'
    const user = await this.userRepository.findOne({ where: { email: email } });
    const permitData = await this.permitReportRepository.findById(id);
    //update the status
    if (permitData.applicantId) {
      const applicant = await this.userRepository.findById(permitData.applicantId);
      if (applicant) {
        this.sqsService.sendMessage(applicant, `Permit ${permitData.maskId} has been Timeout `, `Permit ${permitData.maskId} is marked as Timeout by ${user?.firstName}`)
      } else {
        throw new HttpErrors.NotFound(`User not found. Try again`);
      }
    }

    await this.actionRepository.updateById(action_id, { status: 'closed' })
    permitReport.status = 'Inactive: Timed Out';
    await this.permitReportRepository.updateById(id, permitReport);
  }

  @patch('/permit-reports-timeout/{id}')
  @response(204, {
    description: 'PermitReport PATCH success',
  })
  async timeoutWholeById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,

    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(PermitReport, { partial: true }),
        },
      },
    })
    permitReport: PermitReport,
  ): Promise<void> {
    const email = '<EMAIL>'
    const user = await this.userRepository.findOne({ where: { email: email } });
    const permitData = await this.permitReportRepository.findById(id);
    //update the status
    if (permitData.applicantId) {
      const applicant = await this.userRepository.findById(permitData.applicantId);
      if (applicant) {
        this.sqsService.sendMessage(applicant, `Permit ${permitData.maskId} has been Timeout `, `Permit ${permitData.maskId} has timed out`)
      } else {
        throw new HttpErrors.NotFound(`User not found. Try again`);
      }
    }

    await this.actionRepository.updateAll({ status: "closed" }, { objectId: id });
    permitReport.status = 'Inactive: Timed Out';
    await this.permitReportRepository.updateById(id, permitReport);
  }

  @patch('/permit-reports-editing/{id}/{action_id}')
  @response(204, {
    description: 'PermitReport PATCH success',
  })
  async editingById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @param.path.string('action_id') action_id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(PermitReport, { partial: true }),
        },
      },
    })
    permitReport: PermitReport,
  ): Promise<void> {
    const email = '<EMAIL>'
    const user = await this.userRepository.findOne({ where: { email: email } });
    const permitData = await this.permitReportRepository.findById(id);
    //update the status


    await this.actionRepository.updateById(action_id, { status: 'editing' })
    // permitReport.status = 'Editing';
    await this.permitReportRepository.updateById(id, permitReport);
  }

  @patch('/permit-reports-resubmit/{id}/{action_id}')
  @response(204, {
    description: 'PermitReport PATCH success',
  })
  async resubmitById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @param.path.string('action_id') action_id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(PermitReport, { partial: true }),
        },
      },
    })
    permitReport: PermitReport,
  ): Promise<void> {
    const email = '<EMAIL>'
    const user = await this.userRepository.findOne({ where: { email: email } });
    // const permitData = await this.permitReportRepository.findById(id);
    //update the status


    await this.actionRepository.updateById(action_id, { status: 'closed' })

    const actionItem = {
      application: "PermitToWork",
      actionType: "dcso_approver",

      description: permitReport.description,
      dueDate: permitReport.permitStartDate,

      status: "open",
      createdDate: permitReport.created,
      objectId: permitReport.id,
      submittedById: user?.id,
      assignedToId: [permitReport.dcsoApproverId]
    }

    if (permitReport.dcsoApproverId && user?.id) {
      const dcsoApprover = await this.userRepository.findById(permitReport.dcsoApproverId);
      if (dcsoApprover) { this.sqsService.sendMessage(dcsoApprover, `Action to be taken on Isolation. Permit ${permitReport.maskId}`, `Permit ${permitReport.maskId} is submitted by ${user?.firstName}. Due Date: ${permitReport.permitStartDate}`) } else { throw new HttpErrors.NotFound(`User not found. Try again`); }
    }



    await this.actionRepository.create(actionItem)
    await this.permitReportRepository.updateById(id, permitReport);
  }


  @patch('/permit-reports-resubmit/{id}')
  @response(204, {
    description: 'PermitReport PATCH success',
  })
  async resubmitWholeById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(PermitReport, { partial: true }),
        },
      },
    })
    permitReport: PermitReport,
  ): Promise<void> {
    const email = '<EMAIL>'
    const user = await this.userRepository.findOne({ where: { email: email } });
    // const permitData = await this.permitReportRepository.findById(id);
    //update the status

    await this.actionRepository.updateAll({ status: "closed" }, { objectId: id });


    const actionItem = {
      application: "PermitToWork",
      actionType: "dcso_approver",

      description: permitReport.description,
      dueDate: permitReport.permitStartDate,

      status: "open",
      createdDate: permitReport.created,
      objectId: permitReport.id,
      submittedById: user?.id,
      assignedToId: [permitReport.dcsoApproverId]
    }

    if (permitReport.dcsoApproverId && user?.id) {
      const dcsoApprover = await this.userRepository.findById(permitReport.dcsoApproverId);
      if (dcsoApprover) { this.sqsService.sendMessage(dcsoApprover, `Action to be taken on Isolation. Permit ${permitReport.maskId}`, `Permit ${permitReport.maskId} is submitted by ${user?.firstName}. Due Date: ${permitReport.permitStartDate}`) } else { throw new HttpErrors.NotFound(`User not found. Try again`); }
    }



    await this.actionRepository.create(actionItem)
    await this.permitReportRepository.updateById(id, permitReport);
  }

  @put('/permit-reports/{id}')
  @response(204, {
    description: 'PermitReport PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() permitReport: PermitReport,
  ): Promise<void> {
    await this.permitReportRepository.replaceById(id, permitReport);
  }

  @del('/permit-reports/{id}')
  @response(204, {
    description: 'PermitReport DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.permitReportRepository.deleteById(id);
  }
}
