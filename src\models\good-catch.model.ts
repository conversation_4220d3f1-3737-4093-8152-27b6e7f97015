import { Entity, model, property, belongsTo, hasMany } from '@loopback/repository';
import { User } from './user.model';
import { Action } from './action.model';
import { LocationThree } from './location-three.model';
import { LocationFour } from './location-four.model';
import {WorkingGroup} from './working-group.model';

@model()
export class GoodCatch extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  whatDidYouObserve?: string;

  @property({
    type: 'string',
  })
  whereDidYouObserve?: string;

  @property({
    type: 'string',
  })
  whatCouldHaveGoneWrong?: string;

  @property({
    type: 'string',
  })
  maskId?: string;

  @property({
    type: 'string',
  })
  department?: string;

  @property({
    type: 'string',
  })
  preventiveAction?: string;

  @property({
    type: 'string',
  })
  workActivity?: string;

  @property({
    type: 'array',
    itemType: 'string',
  })
  uploads?: string[];

  @property({
    type: 'array',
    itemType: 'string',
  })
  actionOwnerUploads?: string[];

  @property({
    type: 'string',
  })
  created?: string;

  @property({
    type: 'string',
  })
  comments?: string;

  @property({
    type: 'string',
  })
  actionTaken?: string;

  @property({
    type: 'string',
  })
  actionToBeTaken?: string;

  @property({
    type: 'string',
  })
  dueDate?: string;

  @property({
    type: 'string',
  })
  status?: string;

  @property({
    type: 'string',
  })
  immediateActionTaken?: string;

  @property({
    type: 'string',
  })
  immediateActionDate?: string;

  @property({
    type: 'string',
  })
  importanceRating?: string;

  @property({
    type: 'string',
  })
  closureDateTime?: string;

  @belongsTo(() => User)
  reporterId: string;

  @belongsTo(() => User)
  adminId: string;

  @belongsTo(() => User)
  actionOwnerId: string;

  @hasMany(() => Action)
  actions: Action[];

  @belongsTo(() => LocationThree)
  locationThreeId: string;

  @belongsTo(() => LocationFour)
  locationFourId: string;

  @belongsTo(() => WorkingGroup)
  workingGroupId: string;

  constructor(data?: Partial<GoodCatch>) {
    super(data);
  }
}

export interface GoodCatchRelations {
  // describe navigational properties here
}

export type GoodCatchWithRelations = GoodCatch & GoodCatchRelations;
