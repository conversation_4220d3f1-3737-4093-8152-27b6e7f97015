import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {RiskUpdate, RiskUpdateRelations} from '../models';

export class RiskUpdateRepository extends DefaultCrudRepository<
  RiskUpdate,
  typeof RiskUpdate.prototype.id,
  RiskUpdateRelations
> {
  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource,
  ) {
    super(RiskUpdate, dataSource);
  }
}
