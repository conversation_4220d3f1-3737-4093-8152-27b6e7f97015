import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasManyRepositoryFactory} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {LocationThree, LocationThreeRelations, LocationFour, ObservationReport, UserLocationRole, PermitReport} from '../models';
import {LocationFourRepository} from './location-four.repository';
import {ObservationReportRepository} from './observation-report.repository';
import {UserLocationRoleRepository} from './user-location-role.repository';
import {PermitReportRepository} from './permit-report.repository';

export class LocationThreeRepository extends DefaultCrudRepository<
  LocationThree,
  typeof LocationThree.prototype.id,
  LocationThreeRelations
> {

  public readonly locationFours: HasManyRepositoryFactory<LocationFour, typeof LocationThree.prototype.id>;

  public readonly observationReports: HasManyRepositoryFactory<ObservationReport, typeof LocationThree.prototype.id>;

  public readonly userLocationRoles: HasManyRepositoryFactory<UserLocationRole, typeof LocationThree.prototype.id>;

  public readonly permitReports: HasManyRepositoryFactory<PermitReport, typeof LocationThree.prototype.id>;

  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource, @repository.getter('LocationFourRepository') protected locationFourRepositoryGetter: Getter<LocationFourRepository>, @repository.getter('ObservationReportRepository') protected observationReportRepositoryGetter: Getter<ObservationReportRepository>, @repository.getter('UserLocationRoleRepository') protected userLocationRoleRepositoryGetter: Getter<UserLocationRoleRepository>, @repository.getter('PermitReportRepository') protected permitReportRepositoryGetter: Getter<PermitReportRepository>,
  ) {
    super(LocationThree, dataSource);
    this.permitReports = this.createHasManyRepositoryFactoryFor('permitReports', permitReportRepositoryGetter,);
    this.registerInclusionResolver('permitReports', this.permitReports.inclusionResolver);
    this.userLocationRoles = this.createHasManyRepositoryFactoryFor('userLocationRoles', userLocationRoleRepositoryGetter,);
    this.registerInclusionResolver('userLocationRoles', this.userLocationRoles.inclusionResolver);
    this.observationReports = this.createHasManyRepositoryFactoryFor('observationReports', observationReportRepositoryGetter,);
    this.registerInclusionResolver('observationReports', this.observationReports.inclusionResolver);
    this.locationFours = this.createHasManyRepositoryFactoryFor('locationFours', locationFourRepositoryGetter,);
    this.registerInclusionResolver('locationFours', this.locationFours.inclusionResolver);
  }
}
